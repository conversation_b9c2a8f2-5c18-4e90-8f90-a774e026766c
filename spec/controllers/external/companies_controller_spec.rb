require 'rails_helper'

RSpec.describe External::CompaniesController, type: :controller do
  describe 'POST create' do
    context 'when the request is invalid' do
      context 'when the subdomain and authorization is invalid' do
        let(:parameters) do
          {
            'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
            'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
            'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
            'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
          }
        end

        before do
          request.host = 'another.example.com'
          request.headers['Authorization'] =  "Basic Invalid"
        end

        it 'returns the unauthorized status' do
          post :create, xhr: true, params: parameters

          expect(response).to have_http_status(:unauthorized)
        end
      end

      context 'when the subdomain is invalid' do
        let(:parameters) do
          {
            'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
            'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
            'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
            'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
          }
        end

        before do
          request.host = 'another.example.com'
          request.headers['Authorization'] = "Basic #{ENV['EXTERNAL_API_ADMIN_KEY']}"
        end

        it 'returns the unauthorized status' do
          post :create, xhr: true, params: parameters

          expect(response).to have_http_status(:unauthorized)
        end
      end

      context 'when Authorization header is missing' do
        let(:parameters) do
          {
            'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
            'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
            'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
            'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
          }
        end

        before do
          request.host = 'admin.example.com'
        end

        it 'returns the unauthorized status' do
          post :create, xhr: true, params: parameters

          expect(response).to have_http_status(:unauthorized)
        end
      end

      context 'when the authorization is nil' do
        let(:parameters) do
          {
            'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
            'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
            'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
            'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
          }
        end

        before do
          request.host = 'admin.example.com'
          request.headers['Authorization'] =  nil
        end

        it 'returns the unauthorized status' do
          post :create, xhr: true, params: parameters

          expect(response).to have_http_status(:unauthorized)
        end
      end

      context 'when the authorization is invalid' do
        let(:parameters) do
          {
            'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
            'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
            'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
            'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
          }
        end

        before do
          request.host = 'admin.example.com'
          request.headers['Authorization'] =  "Basic Invalid"
        end

        it 'returns the unauthorized status' do
          post :create, xhr: true, params: parameters

          expect(response).to have_http_status(:unauthorized)
        end
      end
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'subdomain' => '' }}

      before do
        request.host = 'admin.example.com'
        request.headers['Authorization'] = "Basic #{ENV['EXTERNAL_API_ADMIN_KEY']}"
      end

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
          'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
          'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
          'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
        }
      end

      before do
        request.host = 'admin.example.com'
        request.headers['Authorization'] = "Basic #{ENV['EXTERNAL_API_ADMIN_KEY']}"
      end

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :subdomain, :page_title, :use_elasticsearch, :chat_enabled, :bypass_approval, :expire_password_after_in_days, :favicon_image, :logo_image, :background_image, :internal_logo_image, :enable_google_oauth,
          :enable_microsoft_oauth, :disable_tips, :enable_open_id, :enable_internationalization, :enable_email_and_password_login, :enable_signup, :welcome_video_url,
          :default_department_id, :restrict_access_by_ip, :token_life_span_in_minutes, :limit_user_on_signup, :chatbot_enabled, :contact_us_enabled, :not_validate_auth_domain_openid,
          :custom_openid_name, :custom_openid_logo,
          allowed_ips: [], auth_domain: [], allowed_sites: [], block_menus: [], open_id_config: %i[host identifier secret]
        )).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'creates the company' do
        expect_any_instance_of(CompanyService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:created)
      end
    end
  end
end
