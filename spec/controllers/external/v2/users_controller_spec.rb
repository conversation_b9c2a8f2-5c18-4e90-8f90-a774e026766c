require 'rails_helper'

RSpec.describe External::V2::UsersController, type: :controller do
  let(:user) { create(:user) }
  let(:administrator) { create(:administrator, authorization_token: SecureRandom.hex(32)) }

  before do
    request.headers['ADMIN_TOKEN'] = administrator.authorization_token
    request.headers['ADMIN_EMAIL'] = administrator.email
  end

  describe 'POST #create' do
    context 'with valid attributes' do
      it 'returns ok status' do
        post :create, format: :json, params: {
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Admin123456789!@',
          password_confirmation: 'Admin123456789!@',
          limited: false,
          coordinator: false,
          notification: false,
          approved: true,
          confirmed_at: Time.zone.now,
          chat_enabled: false,
          block_menus: [],
          department_ids: []
        }
        expect(response).to have_http_status(:created)
      end
    end

    context 'with invalid attributes' do
      it 'returns unprocessable entity status' do
        post :create, format: :json, params: {
          name: 'Test User',
          email: ''
        }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        post :create, format: :json, params: {
          name: 'Test User',
          email: ''
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT #update' do
    context 'with valid attributes' do
      it 'returns ok status' do
        put :update, format: :json, params: {
          name: 'Test User',
          email: user.email,
          password: 'Admin123456789!@',
          password_confirmation: 'Admin123456789!@',
          limited: false,
          coordinator: false,
          notification: false,
          approved: true,
          confirmed_at: Time.zone.now,
          chat_enabled: false,
          block_menus: [],
          department_ids: []
        }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid attributes' do
      it 'returns unprocessable entity status' do
        put :update, format: :json, params: {
          name: 'Test User',
          email: '<EMAIL>'
        }
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        put :update, format: :json, params: {
          name: 'Test User',
          email: ''
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'with valid attributes' do
      it 'returns ok status' do
        delete :destroy, format: :json, params: {
          id: user.id
        }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        delete :destroy, format: :json, params: {
          id: user.id
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
