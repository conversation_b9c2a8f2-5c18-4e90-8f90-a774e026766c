# == Schema Information
#
# Table name: users
#
#  id                     :uuid             not null, primary key
#  provider               :string           default("email"), not null
#  uid                    :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  reset_password_token   :string
#  reset_password_sent_at :datetime
#  remember_created_at    :datetime
#  sign_in_count          :integer          default(0), not null
#  current_sign_in_at     :datetime
#  last_sign_in_at        :datetime
#  current_sign_in_ip     :string
#  last_sign_in_ip        :string
#  confirmation_token     :string
#  confirmed_at           :datetime
#  confirmation_sent_at   :datetime
#  unconfirmed_email      :string
#  name                   :string
#  nickname               :string
#  image                  :string
#  email                  :string
#  tokens                 :json
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  deleted_at             :datetime
#  department_id          :uuid
#  limited                :boolean          default(FALSE)
#  coordinator            :boolean          default(FALSE)
#  password_changed_at    :datetime
#  allow_password_change  :boolean          default(FALSE), not null
#

require 'rails_helper'

RSpec.describe UsersController, type: :controller do
  before do
    @admin = create(:administrator)

    @auth_headers = @admin.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    before do
      @user = create(:user, name: 'Mark')
      @user2 = create(:user, name: 'Dark', deleted_at: Time.zone.now)
      @user3 = create(:user, name: 'Bark')
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(datatable_columns) }

      it 'initializes the user datatable' do
        expect(UserDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end
    end

    context 'json request' do
      context 'with admin' do
        let(:parameters) { @auth_headers }

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_types('*', id: :string, name: :string_or_null, email: :string_or_null, deleted: :boolean_or_null)
        end

        it 'renders the users data ordered by not deleted and name' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_sizes(3)
          expect_json('0', id: @user3.id, name: @user3.name, email: @user3.email, deleted: @user3.discarded?)
          expect_json('1', id: @user.id, name: @user.name, email: @user.email, deleted: @user.discarded?)
          expect_json('2', id: @user2.id, name: @user2.name, email: @user2.email, deleted: @user2.discarded?)
        end

        context 'when requesting for kept users only' do
          let(:parameters) { {only_active: true}.merge(@auth_headers) }

          it 'only return kept users' do
            get :index, xhr: true, format: :json, params: parameters

            expect_json_sizes(2)
            expect_json('0', id: @user3.id, name: @user3.name, email: @user3.email, deleted: @user3.discarded?)
            expect_json('1', id: @user.id, name: @user.name, email: @user.email, deleted: @user.discarded?)
          end
        end
      end

      context 'with user' do
        before do
          @user_auth_headers = @user.create_new_auth_token
        end

        let(:parameters) { @user_auth_headers }

        it 'renders the index page' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to render_template(:index)
        end

        it 'returns ok status' do
          get :index, xhr: true, format: :json, params: parameters

          expect(response).to have_http_status(:ok)
        end

        it 'renders the json structure' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_types('*', id: :string, name: :string_or_null, email: :string_or_null, deleted: :boolean_or_null)
        end

        it 'renders the users data ordered by not deleted and name' do
          get :index, xhr: true, format: :json, params: parameters

          expect_json_sizes(3)
          expect_json('0', id: @user3.id, name: @user3.name, email: nil, deleted: nil)
          expect_json('1', id: @user.id, name: @user.name, email: nil, deleted: nil)
          expect_json('2', id: @user2.id, name: @user2.name, email: nil, deleted: nil)
        end
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'email' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          'name' => 'Foobar', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>',
          'limited' => 'true', 'coordinator' => 'true', 'chat_enabled' => 'true'
        }.with_indifferent_access
      end

      it 'initializes the user service' do
        expect(UserService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:name, :password, :password_confirmation, :email, :limited, :coordinator, :chat_enabled)).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'creates the user' do
        expect_any_instance_of(UserService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:created)
      end
    end
  end

  describe 'PUT update' do
    before do
      @user = create(:user)
    end

    context 'with invalid parameters' do
      let(:parameters) {  { 'email' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @user.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          'id' => @user.id, 'name' => 'Foobar', 'email' => '<EMAIL>', 'limited' => 'true',
          'coordinator' => 'true', 'chat_enabled' => 'true'
        }.with_indifferent_access
      end

      it 'initializes the user service' do
        expect(UserService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:id, :name, :email, :limited, :coordinator, :chat_enabled)).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @user.id).merge(@auth_headers)
      end

      it 'updates the user' do
        expect_any_instance_of(UserService).to receive(:update).with(@user.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @user.id).merge(@auth_headers)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @user.id).merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @user.id }.merge(@auth_headers) }

    before do
      @user = create(:user)
    end

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, email: :string_or_null, deleted: :boolean_or_null, chat_enabled: :boolean)
    end

    it 'returns the user data' do
      get :show, xhr: true, params: parameters
      expect_json(id: @user.id, name: @user.name, email: @user.email, deleted: @user.discarded?, chat_enabled: @user.chat_enabled?)
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @user.id }.merge(@auth_headers) }

    before do
      @user = create(:user)
    end

    context 'with error' do
      let(:user_service) { double(:user_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(UserService).to receive(:new).and_return(user_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the user service' do
        expect(UserService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the user' do
        expect_any_instance_of(UserService).to receive(:destroy).with(@user.id.to_s).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe 'PATCH activate' do
    let(:parameters) { { id: @user.id }.merge(@auth_headers) }

    before do
      @user = create(:user, deleted_at: Time.zone.now)
    end

    context 'with error' do
      let(:user_service) { double(:user_service, restore: false, success: false, errors: ['foo']) }

      before do
        allow(UserService).to receive(:new).and_return(user_service)
      end

      it 'returns bad request status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        patch :activate, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the user service' do
        expect(UserService).to receive(:new).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'activates the user' do
        expect_any_instance_of(UserService).to receive(:restore).with(@user.id.to_s).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'returns no content status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe 'GET notifications' do
    render_views

    before do
      @user = create(:user, notification: true)

      @template = create(:template)
      @template2 = create(:template)

      @field1 = create(:field, template: @template)
      @field2 = create(:field, template: @template2)

      @key_field1 = create(:field, template: @field1.template)
      @key_field2 = create(:field, template: @field1.template)

      @business_group1 = create(:business_group)
      @business_group2 = create(:business_group)
      @business1 = create(:business, business_group: @business_group1, key_fields: [@key_field1], notification: true)
      @business2 = create(:business, business_group: @business_group2, key_fields: [@key_field2], notification: true)

      @step1 = create(:step, business: @business1)
      @step2 = create(:step, business: @business1)

      @step3 = create(:step, business: @business2)
      @step4 = create(:step, business: @business2)

      create(:step_template, template: @template, step: @step1)
      create(:step_template, template: @template2, step: @step2)
      create(:step_template, template: @template, step: @step3)
      create(:step_template, template: @template2, step: @step4)

      @content1 = create(:content, business: @business1, status: :pending).tap do |content|
        @answer1 = create(:answer, user: create(:user), step: @step1, position: 0, content: content, values: { @field1.id => 'SP' })
        @answer2 = create(:answer, step: @step2, position: 1, content: content, values: { @field2.id => 'RJ' }, user: @user)
      end

      @content2 = create(:content, business: @business2, status: :pending).tap do |content|
        @answer1 = create(:answer, step: @step3, position: 0, content: content, values: { @field1.id => 'SP' }, user: @user)
        @answer2 = create(:answer, user: create(:user), step: @step4, position: 1, content: content, values: { @field2.id => 'RJ' })
      end

      @content3 = create(:content, business: create(:business, name: 'A', business_group: @business_group1, notification: true), status: :pending)
    end

    context 'with user' do
      before do
        @user_auth_headers = @user.create_new_auth_token
      end

      let(:parameters) { @user_auth_headers }

      it 'renders the index page' do
        get :notifications, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:notifications)
      end

      it 'returns ok status' do
        get :notifications, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :notifications, xhr: true, format: :json, params: parameters
        expect_json_types('*', business_id: :string, business_name: :string, content_id: :string, step_name: :string, key_fields: :array_of_objects)
      end

      it 'renders the users data ordered by not deleted and name' do
        get :notifications, xhr: true, format: :json, params: parameters

        expect_json_sizes(2)
        expect_json('0', business_id: @business2.id, business_name: @business2.name, content_id: @content2.id, step_name: @step3.name, key_fields: [{ label: @key_field2.label, value: nil }])
        expect_json('1', business_id: @business1.id, business_name: @business1.name, content_id: @content1.id, step_name: @step1.name, key_fields: [{ label: @key_field1.label, value: nil }])
      end
    end

    context 'with administrator' do
      let(:parameters) { @auth_headers }

      it 'renders the index page' do
        get :notifications, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:notifications)
      end

      it 'returns ok status' do
        get :notifications, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :notifications, xhr: true, format: :json, params: parameters
        expect_json_types('*', business_id: :string, business_name: :string, content_id: :string, step_name: :string, key_fields: :array_of_objects)
      end

      it 'renders the users data ordered by not deleted and name' do
        get :notifications, xhr: true, format: :json, params: parameters

        expect_json_sizes(0)
      end
    end
  end

  describe 'GET top_answers' do
    render_views

    before do
      @department1 = create(:department)
      @department2 = create(:department)
      @user = create(:user)
      @user2 = create(:user)

      @user.departments << @department1
      @user.save!

      @user2.departments << @department2
      @user2.save!

      @business_group1 = create(:business_group)
      @business_group2 = create(:business_group)
      @business1 = create(:business, :with_step, business_group: @business_group1, name: 'foo bar', show_on_top_answers: true)
      @business2 = create(:business, :with_step, business_group: @business_group2, name: 'lorem ipsum', show_on_top_answers: true)

      @content1 = create(:content, business: @business1, status: :pending).tap do |_content|
        @answer1 = create(:answer, :with_dependencies, step: @business1.steps.first)
      end

      @content2 = create(:content, business: @business1, status: :done, concluded_at: Time.zone.now).tap do |_content|
        @answer2 = create(:answer, :with_dependencies, step: @business1.steps.first)
      end

      @content3 = create(:content, business: @business2, status: :pending).tap do |_content|
        @answer3 = create(:answer, :with_dependencies, step: @business2.steps.first)
      end

      @content4 = create(:content, business: @business1, status: :done, concluded_at: Time.zone.now).tap do |_content|
        @answer4 = create(:answer, :with_dependencies, step: @business1.steps.first)
      end

      @content5 = create(:content, business: @business2, status: :pending).tap do |_content|
        @answer5 = create(:answer, :with_dependencies, step: @business2.steps.first)
      end

      @answer1.step.step_permissions.create!(department_id: @department1.id)
      @answer2.step.step_permissions.create!(department_id: @department1.id)
      @answer3.step.step_permissions.create!(department_id: @department2.id)
      @answer4.step.step_permissions.create!(department_id: @department1.id)
      @answer5.step.step_permissions.create!(department_id: @department2.id)
    end

    context 'with user' do
      before do
        @user_auth_headers = @user.create_new_auth_token
      end

      let(:parameters) { @user_auth_headers }

      it 'renders the top answers page' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:top_answers)
      end

      it 'returns ok status' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect_json_types('*', id: :string, content_id: :string, business_id: :string, business_name: :string, step_name: :string, status: :string, key_fields: :array_of_objects, updated_at: :string)
      end

      it 'renders the answers data' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect_json_sizes(3)

        expect_json('0', id: @answer4.id, content_id: @answer4.content_id, business_id: @answer4.content.business_id, business_name: @answer4.content.business.name, step_name: @answer4.content.current_step_name, status: @answer4.content.status, key_fields: [], updated_at: @answer4.updated_at.iso8601)
        expect_json('1', id: @answer2.id, content_id: @answer2.content_id, business_id: @answer2.content.business_id, business_name: @answer2.content.business.name, step_name: @answer2.content.current_step_name, status: @answer2.content.status, key_fields: [], updated_at: @answer2.updated_at.iso8601)
        expect_json('2', id: @answer1.id, content_id: @answer1.content_id, business_id: @answer1.content.business_id, business_name: @answer1.content.business.name, step_name: @answer1.content.current_step_name, status: @answer1.content.status, key_fields: [], updated_at: @answer1.updated_at.iso8601)
      end

      context 'filter by status' do
        before do
          @answer4.content.update(status: :done, concluded_at: Time.zone.now)
          @answer2.content.update(status: :done, concluded_at: Time.zone.now)
        end

        it 'renders the answers data' do
          get :top_answers, xhr: true, format: :json, params: parameters.merge(status: :done)

          expect_json_sizes(2)

          expect_json('0', id: @answer4.id, content_id: @answer4.content_id, business_id: @answer4.content.business_id, business_name: @answer4.content.business.name, step_name: @answer4.content.current_step_name, status: @answer4.content.status, key_fields: [], updated_at: @answer4.updated_at.iso8601)
          expect_json('1', id: @answer2.id, content_id: @answer2.content_id, business_id: @answer2.content.business_id, business_name: @answer2.content.business.name, step_name: @answer2.content.current_step_name, status: @answer2.content.status, key_fields: [], updated_at: @answer2.updated_at.iso8601)
        end
      end

      context 'when the user is limited' do
        before do
          @user.update! limited: true
          @answer1.update! created_by_id: @user.id
        end

        it 'renders the filtered answers data' do
          get :top_answers, xhr: true, format: :json, params: parameters

          expect_json_sizes(1)

          expect_json('0', id: @answer1.id, content_id: @answer1.content_id, business_id: @answer1.content.business_id, business_name: @answer1.content.business.name, step_name: @answer1.content.current_step_name, status: @answer1.content.status, key_fields: [], updated_at: @answer1.updated_at.iso8601)
        end
      end
    end

    context 'with administrator' do
      let(:parameters) { @auth_headers }

      it 'renders the top answers page' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:top_answers)
      end

      it 'returns ok status' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :top_answers, xhr: true, format: :json, params: parameters

        expect_json_types('*', id: :string, content_id: :string, business_id: :string, business_name: :string, step_name: :string, status: :string, key_fields: :array_of_objects, updated_at: :string)
      end
    end
  end

  describe 'PUT welcome_video_watched' do
    before do
      @user = create(:user)
    end

    context 'with valid parameters' do
      let(:parameters) { { 'welcome_video_watched' => 'true' }.merge(@auth_headers) }

      it 'initializes the user service' do
        expect(UserService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:welcome_video_watched)).and_call_original

        put :welcome_video_watched, xhr: true, params: parameters.merge(id: @user.id)
      end

      it 'updates the user welcome_video_watched' do
        expect_any_instance_of(UserService).to receive(:update).with(@user.id.to_s).and_call_original

        put :welcome_video_watched, xhr: true, params: parameters.merge(id: @user.id)
      end

      it 'returns the ok status' do
        put :welcome_video_watched, xhr: true, params: parameters.merge(id: @user.id)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PUT users/:id/lock' do
    before do
      @user = create(:user)
      allow(UserService).to receive(:new).and_return(service)
    end

    let!(:service) { instance_double(UserService, lock: true) }
    let!(:admin) { create(:administrator) }
    let!(:params) { { id: @user.id }.merge(@admin.create_new_auth_token) }

    def do_put
      put :lock, xhr: true, params: params
    end

    it 'creates new instance of UserService' do
      do_put

      expect(UserService).to have_received(:new)
    end

    it 'calls lock on UserService' do
      do_put

      expect(service).to have_received(:lock).with(@user.id)
    end

    it 'returns ok status' do
      do_put

      expect(response.status).to eq(200)
    end
  end

  describe 'PUT users/:id/unlock' do
    before do
      @user = create(:user, locked_at: Time.zone.now, unlock_token: 'foo bar')
      allow(UserService).to receive(:new).and_return(service)
    end

    let!(:service) { instance_double(UserService, unlock: true) }
    let!(:admin) { create(:administrator) }
    let!(:params) { { id: @user.id }.merge(@admin.create_new_auth_token) }

    def do_put
      put :unlock, xhr: true, params: params
    end

    it 'creates new instance of UserService' do
      do_put

      expect(UserService).to have_received(:new)
    end

    it 'calls lock on UserService' do
      do_put

      expect(service).to have_received(:unlock).with(@user.id)
    end

    it 'returns ok status' do
      do_put

      expect(response.status).to eq(200)
    end
  end

  describe 'POST token_to_confirm_step' do
    before do
      allow(UserService).to receive(:new).and_return(user_service)
    end

    let(:user_service) { double(:user_service, token_to_confirm_step: true, success: true) }
    let(:parameters) do
      {
        'step_id' => 'Foobar', 'email' => '<EMAIL>', 'answer_id' => 'fooooobar', 'administrator' => 'false'
      }.with_indifferent_access
    end

    it 'initializes the user service' do
      expect(UserService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(:step_id, :email, :answer_id, :administrator)).and_call_original

      post :token_to_confirm_step, xhr: true, params: parameters.merge(@auth_headers)
    end

    it 'calls token_to_confirm_step on service' do
      expect(user_service).to receive(:token_to_confirm_step)

      post :token_to_confirm_step, xhr: true, params: parameters.merge(@auth_headers)
    end

    context 'with service success' do
      it 'returns the ok status' do
        post :token_to_confirm_step, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with service unsuccess' do
      let(:user_service) { double(:user_service, token_to_confirm_step: true, success: false, errors: ['foobar']) }

      it 'returns the bad_request status' do
        post :token_to_confirm_step, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the error message' do
        post :token_to_confirm_step, xhr: true, params: parameters.merge(@auth_headers)
        body = JSON.parse(response.body)

        expect(body['errors']).to eq(['foobar'])
      end
    end
  end
end
