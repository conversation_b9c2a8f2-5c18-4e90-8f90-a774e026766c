# == Schema Information
#
# Table name: public.companies
#
#  id                            :uuid             not null, primary key
#  name                          :string           not null
#  subdomain                     :string           not null
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  api_key                       :string
#  logo_file_name                :string
#  logo_content_type             :string
#  logo_file_size                :integer
#  logo_updated_at               :datetime
#  background_file_name          :string
#  background_content_type       :string
#  background_file_size          :integer
#  background_updated_at         :datetime
#  theme_id                      :integer          default(1)
#  use_elasticsearch             :boolean          default(FALSE)
#  expire_password_after_in_days :integer          default(90), not null
#  auth_provider                 :integer          default("fourmdg"), not null
#  auth_domain                   :text             default([]), is an Array
#  bypass_approval               :boolean          default(FALSE)

require 'rails_helper'

RSpec.describe CompaniesController, type: :controller do
  before do
    @admin = create(:administrator)

    @auth_headers = @admin.create_new_auth_token

    allow(Apartment::Tenant).to receive(:create)
  end

  describe 'GET index' do
    render_views

    before do
      @company_test = Company.find_by(subdomain: 'test')
      @company1 = create(:company, subdomain: 'company1')
      @company2 = create(:company, subdomain: 'company2')
      @company3 = create(:company, subdomain: 'company3')
      @company4 = create(:company, subdomain: 'company4')
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(datatable_columns) }

      before do
        allow(controller).to receive(:authorize)
      end

      it 'initializes the company datatable' do
        expect(CompanyDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end
    end
  end

  describe 'GET show' do
    render_views

    let(:parameters) { { id: @company.id }.merge(@auth_headers) }

    before do
      @company = Company.first
      @company2 = create(:company)
      @company3 = create(:company, subdomain: 'abc')

      @company.update(enable_open_id: true, open_id_config: { host: 'foobar.com', identifier: 'abc', secret: '123' }, auth_domain: ['foo.bar'])

      Apartment::Tenant.switch @company.subdomain do
        create(:data_replacement, text: 'foo', replacement: 'bar')
        create(:data_replacement, text: 'Foo', replacement: '')
      end
    end

    it 'renders the show page' do
      get :show, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :show, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :show, xhr: true, params: parameters

      expect_json_types(
        id: :string, name: :string, subdomain: :string, use_elasticsearch: :boolean, enable_signup: :boolean, data_replacements: :array_of_objects,
        chat_enabled: :boolean, restrict_access_by_ip: :boolean, allowed_ips: :array_of_strings, limit_user_on_signup: :boolean, token_life_span_in_minutes: :integer,
        enable_open_id: :boolean, open_id_config: :object, bypass_approval: :boolean, block_menus: :array_of_strings, chatbot_enabled: :boolean, contact_us_enabled: :boolean,
        not_validate_auth_domain_openid: :boolean, custom_openid_name: :string_or_null
      )

      expect_json_types('data_replacements.*', text: :string, replacement: :string_or_null)
      expect_json_types('open_id_config', host: :string_or_null, identifier: :string_or_null, secret: :string_or_null)
    end

    it 'returns the company data' do
      get :show, xhr: true, params: parameters

      expect_json(
        id: @company.id, name: @company.name, subdomain: @company.subdomain, use_elasticsearch: @company.use_elasticsearch?,
        chat_enabled: @company.chat_enabled?, enable_signup: @company.enable_signup, chatbot_enabled: @company.chatbot_enabled, contact_us_enabled: @company.contact_us_enabled,
        data_replacements: [{ text: 'Foo', replacement: '' }, { text: 'foo', replacement: 'bar' }], restrict_access_by_ip: false, allowed_ips: [], block_menus: [],
        token_life_span_in_minutes: @company.token_life_span_in_minutes, enable_open_id: @company.enable_open_id?,
        open_id_config: { host: @company.open_id_config_host, identifier: @company.open_id_config_identifier, secret: @company.open_id_config_secret },
        bypass_approval: @company.bypass_approval
      )
    end
  end

  describe 'GET details' do
    render_views

    let(:parameters) { { subdomain: @company.subdomain } }

    before do
      @company = Company.first

      @company.update!(bypass_approval: false, restrict_access_by_ip: true, allowed_ips: ['**************', '************'], auth_domain: ['foo.bar'], enable_open_id: true, open_id_config: { host: 'foobar.com', identifier: 'abc', secret: '123' })

      @company2 = create(:company, subdomain: 'abcd')
      @company3 = create(:company, subdomain: 'abcde')

      Apartment::Tenant.switch @company.subdomain do
        create(:data_replacement, text: 'foo', replacement: 'bar')
        create(:data_replacement, text: 'Foo', replacement: '')
      end
    end

    it 'renders the show page' do
      get :detail, xhr: true, params: parameters

      expect(response).to render_template(:detail)
    end

    it 'returns ok status' do
      get :detail, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :detail, xhr: true, params: parameters

      expect_json_types(
        page_title: :string_or_null, logo_url: :string_or_null, background_url: :string_or_null, internal_logo_url: :string_or_null, favicon_url: :string_or_null,
        enable_google_oauth: :boolean, enable_microsoft_oauth: :boolean, enable_email_and_password_login: :boolean,
        enable_internationalization: :boolean, use_elasticsearch: :boolean, enable_signup: :boolean,
        theme_id: :integer_or_null, data_replacements: :array_of_objects,
        enable_open_id: :boolean, bypass_approval: :boolean, not_validate_auth_domain_openid: :boolean,
        custom_openid_name: :string_or_null, custom_openid_logo_url: :string_or_null
      )
      expect_json_types('data_replacements.*', text: :string, replacement: :string_or_null)
    end

    it 'returns the company data' do
      get :detail, xhr: true, params: parameters

      expect_json(
        page_title: nil, logo_url: nil, background_url: nil, internal_logo_url: Rails.application.credentials.default_internal_logo_image, favicon_url: Rails.application.credentials.default_favicon_image, enable_google_oauth: @company.enable_google_oauth?, enable_microsoft_oauth: @company.enable_microsoft_oauth?,
        enable_email_and_password_login: @company.enable_email_and_password_login?, enable_internationalization: @company.enable_internationalization?,
        use_elasticsearch: @company.use_elasticsearch?, enable_signup: @company.enable_signup,
        theme: { id: @company&.theme&.id,
        name: @company&.theme&.name,
        text_color: @company&.theme&.text_color,
        background_color: @company&.theme&.background_color,
        button_color: @company&.theme&.button_color,
        menu_color: @company&.theme&.menu_color,
        menu_focus_color: @company&.theme&.menu_focus_color },
        data_replacements: [{ text: 'Foo', replacement: '' }, { text: 'foo', replacement: 'bar' }], enable_open_id: @company.enable_open_id?,
        bypass_approval: @company.bypass_approval
      )
    end

    context 'when the company does not exist' do
      it 'returns only the default internal logo image url' do
        parameters = { subdomain: 'admin' }

        get :detail, xhr: true, params: parameters

        expect_json(internal_logo_url: Rails.application.credentials.default_internal_logo_image)
      end

      it 'returns only the default favicon image url' do
        parameters = { subdomain: 'admin' }
        get :detail, xhr: true, params: parameters
        expect_json(favicon_url: Rails.application.credentials.default_favicon_image)
      end
    end
  end

  describe 'GET current' do
    render_views

    before do
      @company = Company.first

      @company.update!(bypass_approval: false, restrict_access_by_ip: true, allowed_ips: ['**************', '************'], auth_domain: ['foo.bar'], enable_open_id: true, open_id_config: { host: 'foobar.com', identifier: 'abc', secret: '123' })

      @company2 = create(:company, subdomain: 'abcd')
      @company3 = create(:company, subdomain: 'abcde')

      Apartment::Tenant.switch @company.subdomain do
        create(:data_replacement, text: 'foo', replacement: 'bar')
        create(:data_replacement, text: 'Foo', replacement: '')
      end
    end

    context 'when the user is not logged in' do
      let(:parameters) { { subdomain: @company.subdomain } }

      it 'returns not authorized status' do
        get :current, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'when the user is logged in' do
      let(:parameters) { { subdomain: @company.subdomain }.merge(@auth_headers) }

      it 'renders the show page' do
        get :current, xhr: true, params: parameters

        expect(response).to render_template(:current)
      end

      it 'returns ok status' do
        get :current, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'returns the json structure' do
        get :current, xhr: true, params: parameters

        expect_json_types(
          page_title: :string_or_null, logo_url: :string_or_null, background_url: :string_or_null, internal_logo_url: :string_or_null, favicon_url: :string_or_null,
          enable_google_oauth: :boolean, enable_microsoft_oauth: :boolean, enable_email_and_password_login: :boolean,
          enable_internationalization: :boolean, use_elasticsearch: :boolean, enable_signup: :boolean, chat_enabled: :boolean,
          theme_id: :integer_or_null, data_replacements: :array_of_objects, restrict_access_by_ip: :boolean, limit_user_on_signup: :boolean,
          enable_open_id: :boolean, open_id_config: :object, bypass_approval: :boolean, chatbot_enabled: :boolean, contact_us_enabled: :boolean
        )
        expect_json_types('data_replacements.*', text: :string, replacement: :string_or_null)
        expect_json_types('open_id_config', host: :string_or_null, identifier: :string_or_null, secret: :string_or_null)
      end

      it 'returns the company data' do
        get :current, xhr: true, params: parameters

        expect_json(
          page_title: nil, logo_url: nil, background_url: nil, internal_logo_url: Rails.application.credentials.default_internal_logo_image, favicon_url: Rails.application.credentials.default_favicon_image, enable_google_oauth: @company.enable_google_oauth?, enable_microsoft_oauth: @company.enable_microsoft_oauth?,
          enable_email_and_password_login: @company.enable_email_and_password_login?, enable_internationalization: @company.enable_internationalization?,
          use_elasticsearch: @company.use_elasticsearch?, enable_signup: @company.enable_signup, chat_enabled: @company.chat_enabled?,
          theme_id: @company.theme_id, restrict_access_by_ip: true, allowed_ips: ['**************', '************'], limit_user_on_signup: @company.limit_user_on_signup?,
          data_replacements: [{ text: 'Foo', replacement: '' }, { text: 'foo', replacement: 'bar' }], enable_open_id: @company.enable_open_id?,
          open_id_config: { host: @company.open_id_config_host, identifier: @company.open_id_config_identifier, secret: @company.open_id_config_secret },
          bypass_approval: @company.bypass_approval, chatbot_enabled: @company.chatbot_enabled?, contact_us_enabled: @company.contact_us_enabled?
        )
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'subdomain' => '' }.merge(@auth_headers) }

      before do
        allow(controller).to receive(:authorize)
      end

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'enable_signup' => 'true', 'chat_enabled' => 'true',
          'restrict_access_by_ip' => 'true', 'allowed_ips' => ['*************'], 'limit_user_on_signup' => 'true', 'token_life_span_in_minutes' => '15', 'block_menus' => ['dashboard'],
          'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
          'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
        }
      end

      before do
        allow(controller).to receive(:authorize)
      end

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :subdomain, :page_title, :use_elasticsearch, :chat_enabled, :bypass_approval, :expire_password_after_in_days, :favicon_image, :logo_image, :background_image, :internal_logo_image, :enable_google_oauth,
          :enable_microsoft_oauth, :disable_tips, :enable_open_id, :enable_internationalization, :enable_email_and_password_login, :enable_signup, :welcome_video_url,
          :default_department_id, :restrict_access_by_ip, :token_life_span_in_minutes, :limit_user_on_signup, :chatbot_enabled, :contact_us_enabled, :not_validate_auth_domain_openid,
          :custom_openid_name, :custom_openid_logo,
          allowed_ips: [], auth_domain: [], allowed_sites: [], block_menus: [], open_id_config: %i[host identifier secret]
       )).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'creates the company' do
        expect_any_instance_of(CompanyService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:created)
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @company.id }.merge(@auth_headers) }

    before { @company = create(:company) }

    context 'with error' do
      let(:company_service) { double(:company_service, destroy: false, success?: false, errors: ['foo']) }

      before do
        allow(CompanyService).to receive(:new).and_return(company_service)
        allow(controller).to receive(:authorize)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      let(:company_service) { double(:company_service, destroy: true, success?: true, errors: []) }

      before do
        allow(CompanyService).to receive(:new).and_return(company_service)
        allow(controller).to receive(:authorize)
      end

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).and_return(company_service)

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the company' do
        expect(company_service).to receive(:destroy).with(@company.id.to_s)

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end

    context 'with invalid administrator in public tenant' do
      before do
        Apartment::Tenant.switch! 'public'

        normal_admin = create(:administrator, owner: false)

        @auth_headers_normal_admin = normal_admin.create_new_auth_token
      end

      let(:parameters) { { id: @company.id }.merge(@auth_headers_normal_admin) }

      it 'returns the unauthorized status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns the errors' do
        delete :destroy, xhr: true, params: parameters

        expect(JSON.parse(response.body)).to eq('errors' => 'Você não possui permissão para acessar esse recurso')
      end
    end
  end

  describe 'PUT update' do
    before { @company = create(:company) }

    context 'with invalid parameters' do
      let(:parameters) {  { 'name' => '' }.merge(@auth_headers) }

      before do
        allow(controller).to receive(:authorize)
      end

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @company.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @company.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          'name' => 'Foo', 'subdomain' => 'bar', 'page_title' => 'Foo bar', 'use_elasticsearch' => 'true', 'chat_enabled' => 'true', 'restrict_access_by_ip' => 'true',
          'allowed_ips' => ['*************'], 'token_life_span_in_minutes' => '12', 'block_menus' => ['search'],
          'enable_open_id' => 'true', 'open_id_config' => { 'host' => 'foo.bar.com', 'identifier' => '1232', 'secret' => 'abc' }, 'auth_domain' => ['foo.bar'], 'bypass_approval' => 'false',
          'chatbot_enabled' => 'true', 'contact_us_enabled' => 'true'
        }
      end

      before do
        allow(controller).to receive(:authorize)
      end

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :subdomain, :page_title, :use_elasticsearch, :chat_enabled, :bypass_approval, :expire_password_after_in_days, :favicon_image, :logo_image, :background_image, :internal_logo_image, :enable_google_oauth,
          :enable_microsoft_oauth, :disable_tips, :enable_open_id, :enable_internationalization, :enable_email_and_password_login, :enable_signup, :welcome_video_url,
          :default_department_id, :restrict_access_by_ip, :token_life_span_in_minutes, :limit_user_on_signup, :chatbot_enabled, :contact_us_enabled, :not_validate_auth_domain_openid,
          :custom_openid_name, :custom_openid_logo,
          allowed_ips: [], auth_domain: [], allowed_sites: [], block_menus: [], open_id_config: %i[host identifier secret]
        )).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @company.id).merge(@auth_headers)
      end

      it 'updates the company' do
        expect_any_instance_of(CompanyService).to receive(:update).with(@company.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @company.id).merge(@auth_headers)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @company.id).merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PATCH update_theme' do
    render_views

    before do
      @current_company = Company.find_by(subdomain: 'test')
      @theme = create(:theme)
    end

    context 'with invalid parameters' do
      let(:parameters) {  { 'theme_id' => '' }.merge(@auth_headers) }
      let(:company_service) { double(:company_service, update: false, success: false, errors: ['foo']) }

      before { allow(CompanyService).to receive(:new).and_return(company_service) }

      it 'returns the unprocessable entity status' do
        patch :update_theme, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        patch :update_theme, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'theme_id' => @theme.id.to_s } }

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :theme_id
        )).and_call_original

        patch :update_theme, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'updates the company' do
        expect_any_instance_of(CompanyService).to receive(:update).with(@current_company.id.to_s).and_call_original

        patch :update_theme, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the ok status' do
        patch :update_theme, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PATCH update_allowed_ips' do
    render_views

    before do
      @current_company = Company.find_by(subdomain: 'test')
      @current_company.update_column(:restrict_access_by_ip, true)
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'allowed_ips' => %w[foo bar] }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        patch :update_allowed_ips, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        patch :update_allowed_ips, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'allowed_ips' => ['*************', '**************'] } }

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          allowed_ips: []
        )).and_call_original

        patch :update_allowed_ips, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'updates the company' do
        expect_any_instance_of(CompanyService).to receive(:update).with(@current_company.id.to_s).and_call_original

        patch :update_allowed_ips, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the ok status' do
        patch :update_allowed_ips, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'PATCH update_token_lifespan' do
    render_views

    before do
      @current_company = Company.find_by(subdomain: 'test')
    end

    context 'with invalid parameters' do
      let(:parameters) { { 'token_life_span_in_minutes' => '-2' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        patch :update_token_lifespan, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        patch :update_token_lifespan, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'token_life_span_in_minutes' => '15' } }

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :token_life_span_in_minutes
        )).and_call_original

        patch :update_token_lifespan, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'updates the company' do
        expect_any_instance_of(CompanyService).to receive(:update).with(@current_company.id.to_s).and_call_original

        patch :update_token_lifespan, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the ok status' do
        patch :update_token_lifespan, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET business_groups' do
    render_views

    let(:business_group) { create(:business_group, name: 'BECA') }
    let(:business_group2) { create(:business_group, name: 'ACA') }

    before do
      @company = Company.current

      Apartment::Tenant.switch @company.subdomain do
        @template = create(:template)

        @business = create(:business, name: 'Foo', business_group: business_group)
        create(:step_template, step: create(:step, business: @business), template: @template)

        @business2 = create(:business, name: 'Car', deleted_at: Time.zone.now, business_group: business_group)

        @business3 = create(:business, name: 'Bar', business_group: business_group)
        create(:step, business: @business3)

        @business4 = create(:business, name: 'Foo2', business_group: business_group2)
        create(:step_template, step: create(:step, business: @business4), template: @template)

        @business5 = create(:business, name: 'Car2', deleted_at: Time.zone.now, business_group: business_group2)
        @business6 = create(:business, name: 'Bar2', business_group: business_group2)

        @business7 = create(:business, name: 'Bar1', business_group: business_group2)
        create(:step_template, step: create(:step, business: @business7), template: @template)
      end
    end

    context 'json request' do
      let(:parameters) { @auth_headers }

      it 'returns ok status' do
        get :business_groups, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :business_groups, xhr: true, format: :json, params: parameters

        expect_json_types('*', id: :string, name: :string)
      end

      it 'renders the businesses data ordered by business group, not deleted and name' do
        get :business_groups, xhr: true, format: :json, params: parameters

        menus = JSON.parse(response.body)

        expect(menus).to eq(
          [
            { 'id' => business_group2.id, 'name' => business_group2.name, 'children' => [{ 'id' => @business7.id, 'name' => @business7.name }, { 'id' => @business4.id, 'name' => @business4.name }] },
            { 'id' => business_group.id, 'name' => business_group.name, 'children' => [{ 'id' => @business.id, 'name' => @business.name }] }
          ]
        )
      end
    end
  end

  describe 'GET find_business' do
    render_views

    let(:parameters) { { business_id: @business.id }.merge(@auth_headers) }

    before do
      @company = Company.current

      Apartment::Tenant.switch @company.subdomain do
        @business = create(:business, :with_dependencies)

        @step1 = create(:step, business: @business, order: 0)
        @step2 = create(:step, business: @business, order: 1, deleted_at: Time.zone.now)
        @step3 = create(:step, business: @business, order: 1)
        @step4 = create(:step, business: @business, order: 2)

        @template = create(:template)
        @step_template = create(:step_template, step: @step1, template: @template)
      end
    end

    it 'renders the business show page' do
      get :find_business, xhr: true, params: parameters

      expect(response).to render_template(:show)
    end

    it 'returns ok status' do
      get :find_business, xhr: true, params: parameters

      expect(response).to have_http_status(:ok)
    end

    it 'returns the json structure' do
      get :find_business, xhr: true, params: parameters

      expect_json_types(id: :string, name: :string, description: :string, deleted: :boolean, business_group_id: :string)
    end

    it 'returns the business data with the active steps' do
      get :find_business, xhr: true, params: parameters

      expect_json(id: @business.id, name: @business.name, description: @business.description, deleted: @business.discarded?, business_group_id: @business.business_group_id)
    end
  end

  describe 'GET content_columns' do
    let(:parameters) { { business_id: @business.id }.merge(@auth_headers) }

    before do
      @company = Company.current

      Apartment::Tenant.switch @company.subdomain do
        @business = create(:business, :with_dependencies)
      end
    end

    it 'lists the business fields that must be shown on the listing' do
      expect(ShowOnListField).to receive_message_chain(:active, :where).with(business_id: @business.id).and_return(ShowOnListField)

      get :content_columns, xhr: true, format: :json, params: parameters
    end
  end

  describe 'GET content_datatable' do
    let(:parameters) { { business_id: @business.id }.merge(@auth_headers) }
    let(:business_param) { { 'business_id' => @business.id } }

    before do
      @company = Company.current

      Apartment::Tenant.switch @company.subdomain do
        @business = create(:business, :with_dependencies)
      end
    end

    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(business_param).merge(datatable_columns) }

      it 'initializes the content datatable' do
        expect(ContentDatatable).to receive(:new).and_return(datatable)

        get :content_datatable, xhr: true, format: :datatable, params: parameters
      end
    end
  end

  describe 'PUT update_elasticsearch_index' do
    let(:parameters) { { id: @company.id }.merge(@auth_headers) }

    before { @company = create(:company) }

    it 'indexes all the company businesses' do
      expect(Elasticsearch::BusinessesSetupWorker).to receive(:perform_async).with(@company.subdomain)

      delete :update_elasticsearch_index, xhr: true, params: parameters
    end
  end

  describe 'PATCH update_data_replacement' do
    context 'with invalid parameters' do
      let(:parameters) do
        {
          data_replacement: [
            { text: 'foo', replacement: 'bar' },
            { text: '', replacement: 'bar' }
          ]
        }.merge(@auth_headers)
      end

      it 'returns the unprocessable entity status' do
        patch :update_data_replacement, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        patch :update_data_replacement, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) do
        {
          data_replacement: [
            { text: 'foo', replacement: 'bar' },
            { text: 'Foo', replacement: 'Bar' }
          ]
        }.with_indifferent_access
      end

      it 'initializes the company service' do
        expect(CompanyService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          data_replacement: %i[text replacement]
        )).and_call_original

        patch :update_data_replacement, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'updates the data replacement configuration' do
        expect_any_instance_of(CompanyService).to receive(:update_data_replacement).and_call_original

        patch :update_data_replacement, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the ok status' do
        patch :update_data_replacement, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'DELETE remove_attachment' do
    let(:company) { create(:company) }

    before do
      sign_in @admin
      company.logo_image.attach(io: File.open(file_fixture('logo.png')), filename: 'logo.png', content_type: 'image/png')
    end

    context 'with valid parameters' do
      let(:parameters) { { id: company.id, attachment_name: 'logo_image' }.merge(@auth_headers) }

      it 'calls the remove_attachment method on the CompanyService' do
        expect_any_instance_of(CompanyService).to receive(:remove_attachment).with(company.id, 'logo_image').and_call_original

        delete :remove_attachment, params: parameters, xhr: true
      end

      it 'removes the attachment' do
        delete :remove_attachment, params: parameters, xhr: true
        company.reload
        expect(company.logo_image).not_to be_attached
      end

      it 'returns a success message' do
        delete :remove_attachment, params: parameters, xhr: true
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq('Logo image was successfully removed.')
      end
    end

    context 'with invalid parameters' do
      let(:parameters) { { id: company.id, attachment_name: 'non_existent_attachment' }.merge(@auth_headers) }

      it 'does not remove any attachment' do
        delete :remove_attachment, params: parameters, xhr: true
        company.reload
        expect(company.logo_image).to be_attached
      end

      it 'returns an error message' do
        delete :remove_attachment, params: parameters, xhr: true
        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['errors']).to include('Attachment not found.')
      end
    end
  end
end
