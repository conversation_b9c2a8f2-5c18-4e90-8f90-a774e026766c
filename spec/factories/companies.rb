# == Schema Information
#
# Table name: public.companies
#
#  id                              :uuid             not null, primary key
#  allowed_ips                     :string           default([]), not null, is an Array
#  allowed_sites                   :text             default([]), is an Array
#  api_key                         :string
#  auth_domain                     :text             default([]), is an Array
#  block_menus                     :string           default([]), is an Array
#  bypass_approval                 :boolean          default(FALSE), not null
#  chat_enabled                    :boolean          default(FALSE)
#  chatbot_enabled                 :boolean          default(FALSE)
#  contact_us_enabled              :boolean          default(FALSE)
#  custom_openid_name              :string
#  disable_tips                    :boolean          default(FALSE)
#  enable_email_and_password_login :boolean          default(TRUE), not null
#  enable_google_oauth             :boolean          default(FALSE), not null
#  enable_internationalization     :boolean          default(FALSE), not null
#  enable_microsoft_oauth          :boolean          default(FALSE), not null
#  enable_signup                   :boolean          default(FALSE), not null
#  expire_password_after_in_days   :integer          default(90), not null
#  limit_user_on_signup            :boolean          default(FALSE), not null
#  name                            :string           not null
#  not_validate_auth_domain_openid :boolean          default(FALSE), not null
#  open_id_config                  :jsonb
#  page_title                      :string
#  restrict_access_by_ip           :boolean          default(FALSE), not null
#  smtp_address                    :string
#  smtp_custom                     :boolean          default(FALSE)
#  smtp_from                       :string
#  smtp_password                   :string
#  smtp_port                       :string
#  smtp_starttls                   :boolean          default(TRUE)
#  smtp_username                   :string
#  subdomain                       :string           not null
#  token_life_span_in_minutes      :integer          default(4320), not null
#  twilio_api_key                  :string
#  twilio_api_secret               :string
#  twilio_auth_token               :string
#  twilio_chat_service_sid         :string
#  twilio_sid                      :string
#  use_elasticsearch               :boolean          default(FALSE)
#  welcome_video_url               :string
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  default_department_id           :uuid
#  enable_open_id                  :boolean          default(FALSE)
#  theme_id                        :bigint           default(1)
#  waf_ip_set_id                   :string
#  waf_rule_group_id               :string
#
# Indexes
#
#  index_companies_on_theme_id  (theme_id)
#
FactoryBot.define do
  factory :company do
    name { Faker::Name.unique.name }
    subdomain { Faker::Internet.unique.domain_word }
    page_title { Faker::Name.unique.name }
    chat_enabled { false }
    chatbot_enabled { false }
    contact_us_enabled { false }
  end
end
