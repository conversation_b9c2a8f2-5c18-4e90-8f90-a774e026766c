# == Schema Information
#
# Table name: contents
#
#  id                :uuid             not null, primary key
#  concluded_at      :datetime
#  created_by_ip     :inet
#  deleted_at        :datetime
#  deletion_reason   :string
#  draft             :boolean          default(FALSE)
#  keywords          :string
#  name              :string           default(""), not null
#  note              :text
#  status            :integer          default("pending"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  business_id       :uuid
#  created_by_id     :uuid
#  current_answer_id :uuid
#  deleted_by_id     :uuid
#  parent_id         :uuid
#
# Indexes
#
#  index_contents_on_business_id        (business_id)
#  index_contents_on_created_by_id      (created_by_id)
#  index_contents_on_current_answer_id  (current_answer_id)
#  index_contents_on_deleted_at         (deleted_at)
#  index_contents_on_deleted_by_id      (deleted_by_id)
#  index_contents_on_draft              (draft)
#  index_contents_on_list_query         (keywords,id,status,note,current_answer_id,deleted_at)
#  index_contents_on_parent_id          (parent_id)
#  index_contents_on_status             (status)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (current_answer_id => answers.id)
#  fk_rails_...  (deleted_by_id => users.id)
#  fk_rails_...  (parent_id => contents.id)
#
FactoryBot.define do
  factory :content do
    name { Faker::Name.unique.name }
    note { Faker::Hipster.paragraph }
  end

  trait :with_dependencies do
    business { create(:business, :with_dependencies) }
  end

  trait :with_step do
    after(:create) do |content, _evaluator|
      create_list(:step, 1, business: content.business)
    end
  end

  trait :with_one_answer do
    after(:create) do |content, _evaluator|
      step = content.business.steps.exists? ? content.business.steps.first : create(:step, business: content.business)

      create(:answer, step: step, content: content)
    end
  end

  trait :with_created_by do
    created_by_id { create(:user).id }
  end
end
