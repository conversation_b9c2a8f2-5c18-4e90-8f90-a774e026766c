require 'rails_helper'

RSpec.describe ContentSearcher, type: :searcher do
  include ActiveSupport::Testing::TimeHelpers

  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group: business_group) }
  let(:step1) { create(:step, business: business) }
  let(:template) { create(:template) }
  let(:field1) { create(:field, :random_type, template: template, required: false) }
  let(:field2) { create(:field, :random_type, template: template, required: false) }
  let(:business2) { create(:business, business_group: business_group) }
  let(:step2) { create(:step, business: business2) }

  describe '#search' do
    let(:parameters) { { business_id: business.id, user: @user } }

    subject { described_class.new(parameters) }

    before do
      @user = create(:user)

      travel_to 5.days.ago do
        @content = create(:content, :with_created_by, business: business).tap do |content|
          content.answers << create(:answer, step: step1, position: 0, content: content)
        end
      end

      @content2 = create(:content, :with_created_by, business: business, draft: true).tap do |content|
        content.answers << create(:answer, step: step1, position: 0, content: content)
      end

      @content3 = create(:content, :with_created_by, business: business, deleted_at: Time.zone.now).tap do |content|
        content.answers << create(:answer, step: step1, position: 0, content: content)
      end
    end

    it 'filters the draft contents' do
      expect(subject.search).not_to include(@content2)
    end

    it 'filters the discarded contents' do
      expect(subject.search).not_to include(@content3)
    end

    context 'with pagination parameters' do
      before do
        travel_to 4.days.ago do
          @content4 = create(:content, :with_created_by, business: business).tap do |content|
            content.answers << create(:answer, step: step1, position: 0, content: content)
          end
        end

        travel_to 3.days.ago do
          @content5 = create(:content, :with_created_by, business: business).tap do |content|
            content.answers << create(:answer, step: step1, position: 0, content: content)
          end
        end

        travel_to 2.days.ago do
          @content6 = create(:content, :with_created_by, business: business).tap do |content|
            content.answers << create(:answer, step: step1, position: 0, content: content)
          end
        end
      end

      context 'for the first page' do
        let(:parameters) { { business_id: business.id, user: @user, startRow: 0, endRow: 2 } }

        it 'paginates the results' do
          results = subject.search

          expect(results).to match_array([@content6, @content5])
          expect(subject.metadata).to eq({ total: 4 })
        end
      end

      context 'for other pages' do
        let(:parameters) { { business_id: business.id, user: @user, startRow: 2, endRow: 4 } }

        it 'paginates the results' do
          results = subject.search

          expect(results).to match_array([@content4, @content])
          expect(subject.metadata).to eq({ total: 4 })
        end
      end
    end

    context 'with sorting parameters' do
      before do
        allow(Content).to receive(:not_draft).and_return(Content)
        allow(Content).to receive(:where).and_return(Content)
        allow(Content).to receive(:select).and_return(Content)
        allow(Content).to receive(:distinct).and_return(Content)
        allow(Content).to receive(:limit).and_return(Content)
        allow(Content).to receive(:offset).and_return(Content)
      end

      context 'for the status column' do
        let(:parameters) { { business_id: business.id, user: @user, sort: [{ colId: ContentSearcher::STATUS_COLUMN, sort: 'asc' }] } }

        it 'sorts by the contents.status before contents.keywords column' do
          expect(Content).to receive(:order).with("contents.status #{parameters[:sort][0][:sort]}, contents.keywords #{parameters[:sort][0][:sort]}").and_return(Content)

          subject.search
        end
      end

      context 'for the created at column' do
        let(:parameters) { { business_id: business.id, user: @user, sort: [{ colId: ContentSearcher::CREATED_AT_COLUMN, sort: 'asc' }] } }

        it 'sorts by the contents.created_at column' do
          expect(Content).to receive(:order).with("contents.created_at #{parameters[:sort][0][:sort]}").and_return(Content)

          subject.search
        end
      end

      context 'for the created_by_name column' do
        let(:parameters) { { business_id: business.id, user: @user, sort: [{ colId: ContentSearcher::CREATED_BY_NAME_COLUMN, sort: 'asc' }] } }

        it 'sorts by the contents.created_by.name column' do
          expect(Content).to receive(:order).with("users.name #{parameters[:sort][0][:sort]}").and_return(Content)

          subject.search
        end
      end

      context 'for the updated at column' do
        let(:parameters) { { business_id: business.id, user: @user, sort: [{ colId: ContentSearcher::UPDATED_AT_COLUMN, sort: 'asc' }] } }

        it 'sorts by the contents.updated_at column' do
          expect(Content).to receive(:order).with("contents.updated_at #{parameters[:sort][0][:sort]}").and_return(Content)

          subject.search
        end
      end

      context 'for other columns' do
        context 'when the field has a reference business' do
          let(:field1) { build(:field, :reference_type, :with_dependencies) }
          let(:parameters) { { business_id: business.id, user: @user, sort: [{ colId: '1', sort: 'asc' }] } }

          before {
            allow(Content).to receive(:joins).and_return(Content)
            allow(Field).to receive(:find).and_return(field1)
          }

          it 'sorts by the field_options.label column' do
            expect(Content).to receive(:order).with("fo2_0.label #{parameters[:sort][0][:sort]} NULLS FIRST").and_return(Content)

            subject.search
          end
        end

        context 'when the field does not have a reference business' do
          let(:parameters) { { business_id: business.id, user: @user, sort: [{ colId: field1.id, sort: 'asc' }] } }

          before { allow(Content).to receive(:joins).and_return(Content) }

          it 'sorts by the content_value.value column' do
            expect(Content).to receive(:order).with("content_value_order_0.value #{parameters[:sort][0][:sort]} NULLS FIRST").and_return(Content)

            subject.search
          end
        end
      end
    end

    context 'with no search/query parameters' do
      let(:parameters) { { business_id: business.id, user: @user } }

      it 'returns the non draft contents' do
        expect(subject.search).to eq([@content])
      end
    end

    context 'with search parameters' do
      let(:parameters) { { business_id: business.id, user: @user, search: search_params } }

      context 'for text filter' do
        let(:filter_type) { ContentSearcher::TEXT_FILTER_TYPE }
        let(:query_field1) { 'foo' }
        let(:query_field2) { 'bar' }
        let(:search_params) do
          {
            "#{field1.id}": { filterType: filter_type, type: type, filter: query_field1 },
            "#{field2.id}": { filterType: filter_type, type: type, filter: query_field2 }
          }
        end

        before do
          travel_to 4.days.ago do
            @content4 = create(:content, :with_created_by, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'foo', field2.id => 'bar' })
            end
          end

          travel_to 3.days.ago do
            @content5 = create(:content, :with_created_by, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'foo', field2.id => 'batz' })
            end
          end

          travel_to 2.days.ago do
            @content6 = create(:content, :with_created_by, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'lorem', field2.id => 'bar' })
            end
          end

          travel_to 1.day.ago do
            @content7 = create(:content, :with_created_by, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'lorem', field2.id => 'ipsum' })
            end
          end
        end

        context "for #{ContentSearcher::CONTAINS_FILTER} filter" do
          let(:type) { ContentSearcher::CONTAINS_FILTER }

          it 'returns the contents that contain the filter terms' do
            expect(subject.search).to eq([@content4])
          end

          context 'when its filtering the create_by_name column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_BY_NAME_COLUMN}": { filterType: filter_type, type: type, filter: @content5.created_by.name[0..-2] }
              }
            end

            it 'returns the content that name contains the filter' do
              expect(subject.search).to include(@content5)
            end
          end
        end

        context "for #{ContentSearcher::NOT_CONTAINS_FILTER} filter" do
          let(:type) { ContentSearcher::NOT_CONTAINS_FILTER }

          it 'returns the contents that not contain the filter terms' do
            expect(subject.search).to eq([@content7])
          end

          context 'when its filtering the create_by_name column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_BY_NAME_COLUMN}": { filterType: filter_type, type: type, filter: @content5.created_by.name }
              }
            end

            it 'returns the content that name does not contains the filter' do
              expect(subject.search).not_to include(@content5)
            end
          end
        end

        context "for #{ContentSearcher::EQUALS_FILTER} filter" do
          let(:type) { ContentSearcher::EQUALS_FILTER }

          context 'when its filtering the id column' do
            let(:search_params) do
              {
                "#{ContentSearcher::ID_COLUMN}": { filterType: filter_type, type: type, filter: @content5.id }
              }
            end

            it 'returns the content that id equals the filter' do
              expect(subject.search).to eq([@content5])
            end

            context 'when its filtering the create_by_name column' do
              let(:search_params) do
                {
                  "#{ContentSearcher::CREATED_BY_NAME_COLUMN}": { filterType: filter_type, type: type, filter: @content5.created_by.name }
                }
              end

              it 'returns the content that name is equal the filter' do
                expect(subject.search).to include(@content5)
              end
            end

          end

          context 'when its filtering a field column' do
            it 'returns the contents that are equal the filter terms' do
              expect(subject.search).to eq([@content4])
            end
          end
        end

        context "for #{ContentSearcher::NOT_EQUAL_FILTER} filter" do
          let(:type) { ContentSearcher::NOT_EQUAL_FILTER }

          it 'returns the contents that are not equal the filter terms' do
            expect(subject.search).to eq([@content7])
          end

          context 'when its filtering the create_by_name column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_BY_NAME_COLUMN}": { filterType: filter_type, type: type, filter: @content5.created_by.name }
              }
            end

            it 'returns the content that is not equal the filter' do
              expect(subject.search).not_to include(@content5)
            end
          end
        end

        context "for #{ContentSearcher::STARTS_WITH_FILTER} filter" do
          let(:type) { ContentSearcher::STARTS_WITH_FILTER }
          let(:query_field1) { 'lor' }
          let(:query_field2) { 'ba' }

          it 'returns the contents that start with the filter terms' do
            expect(subject.search).to eq([@content6])
          end

          context 'when its filtering the create_by_name column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_BY_NAME_COLUMN}": { filterType: filter_type, type: type, filter: @content5.created_by.name[0..1] }
              }
            end

            it 'returns the content that name starts with the filter' do
              expect(subject.search).to include(@content5)
            end
          end

        end

        context "for #{ContentSearcher::ENDS_WITH_FILTER} filter" do
          let(:type) { ContentSearcher::ENDS_WITH_FILTER }
          let(:query_field1) { 'rem' }
          let(:query_field2) { 'sum' }

          it 'returns the contents that start with the filter terms' do
            expect(subject.search).to eq([@content7])
          end

          context 'when its filtering the create_by_name column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_BY_NAME_COLUMN}": { filterType: filter_type, type: type, filter: @content5.created_by.name[-2..-1] }
              }
            end

            it 'returns the content that name end_with with the filter' do
              expect(subject.search).to include(@content5)
            end
          end
        end
      end

      context 'for date filter' do
        let(:filter_type) { ContentSearcher::DATE_FILTER_TYPE }

        before do
          field1.update_column(:type, :date)
          field2.update_column(:type, :date)

          travel_to 4.days.ago do
            @content4 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 8.days.ago.to_date.iso8601, field2.id => 6.days.ago.to_date.iso8601 })
            end
          end

          travel_to 3.days.ago do
            @content5 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 5.days.ago.to_date.iso8601, field2.id => 3.days.ago.to_date.iso8601 })
            end
          end

          travel_to 2.days.ago do
            @content6 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 2.days.ago.to_date.iso8601, field2.id => Time.zone.today.iso8601 })
            end
          end

          travel_to 1.day.ago do
            @content7 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 3.days.ago.to_date.iso8601, field2.id => 1.day.from_now.to_date.iso8601 })
            end
          end
        end

        context "for #{ContentSearcher::EQUALS_FILTER} filter" do
          let(:type) { ContentSearcher::EQUALS_FILTER }

          context 'when its filtering the created_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content5.created_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were created at the filter date' do
              expect(subject.search).to eq([@content5])
            end
          end

          context 'when its filtering the updated_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::UPDATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content7.updated_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were updated at the filter date' do
              expect(subject.search).to eq([@content7])
            end
          end

          context 'when its filtering a field column' do
            let(:search_params) do
              {
                "#{field1.id}": { filterType: filter_type, type: type, dateFrom: 4.days.ago.strftime('%Y-%m-%d 00:00:00') },
                "#{field2.id}": { filterType: filter_type, type: type, dateFrom: 2.days.ago.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that dates are equal the filter date' do
              expect(subject.search).to eq([@content6])
            end
          end
        end

        context "for #{ContentSearcher::NOT_EQUAL_FILTER} filter" do
          let(:type) { ContentSearcher::NOT_EQUAL_FILTER }

          context 'when its filtering the created_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content5.created_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were not created at the filter date' do
              expect(subject.search).not_to include(@content5)
            end
          end

          context 'when its filtering the updated_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::UPDATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content7.updated_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were not updated at the filter date' do
              expect(subject.search).not_to include(@content7)
            end
          end

          context 'when its filtering a field column' do
            let(:search_params) do
              {
                "#{field1.id}": { filterType: filter_type, type: type, dateFrom: 4.days.ago.strftime('%Y-%m-%d 00:00:00') },
                "#{field2.id}": { filterType: filter_type, type: type, dateFrom: 2.days.ago.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that dates are not equal the filter date' do
              expect(subject.search).not_to include(@content6)
            end
          end
        end

        context "for #{ContentSearcher::GREATER_THAN_FILTER} filter" do
          let(:type) { ContentSearcher::GREATER_THAN_FILTER }

          context 'when its filtering the created_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content5.created_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were created after the filter date' do
              expect(subject.search).to eq([@content7, @content6, @content5])
            end
          end

          context 'when its filtering the updated_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::UPDATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content6.updated_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were updated after the filter date' do
              expect(subject.search).to eq([@content7, @content6])
            end
          end

          context 'when its filtering a field column' do
            let(:search_params) do
              {
                "#{field1.id}": { filterType: filter_type, type: type, dateFrom: 12.days.ago.strftime('%Y-%m-%d 00:00:00') },
                "#{field2.id}": { filterType: filter_type, type: type, dateFrom: 5.days.ago.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that dates are newer than the filter date' do
              expect(subject.search).to eq([@content7, @content6])
            end
          end
        end

        context "for #{ContentSearcher::LESS_THAN_FILTER} filter" do
          let(:type) { ContentSearcher::LESS_THAN_FILTER }

          context 'when its filtering the created_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content5.created_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were created before the filter date' do
              expect(subject.search).to eq([@content5, @content4, @content])
            end
          end

          context 'when its filtering the updated_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::UPDATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content6.updated_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were updated before the filter date' do
              expect(subject.search).to eq([@content6, @content5, @content4, @content])
            end
          end

          context 'when its filtering a field column' do
            let(:search_params) do
              {
                "#{field1.id}": { filterType: filter_type, type: type, dateFrom: 6.days.ago.strftime('%Y-%m-%d 00:00:00') },
                "#{field2.id}": { filterType: filter_type, type: type, dateFrom: Time.zone.today.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that dates are older than the filter date' do
              expect(subject.search).to eq([@content5, @content4])
            end
          end
        end

        context "for #{ContentSearcher::IN_RANGE_FILTER} filter" do
          let(:type) { ContentSearcher::IN_RANGE_FILTER }

          context 'when its filtering the created_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::CREATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content4.created_at.strftime('%Y-%m-%d 00:00:00'), dateTo: @content6.created_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were created in the range of the filter date' do
              expect(subject.search).to eq([@content6, @content5, @content4])
            end
          end

          context 'when its filtering the updated_at column' do
            let(:search_params) do
              {
                "#{ContentSearcher::UPDATED_AT_COLUMN}": { filterType: filter_type, type: type, dateFrom: @content.updated_at.strftime('%Y-%m-%d 00:00:00'), dateTo: @content5.updated_at.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that were updated in the range of the filter date' do
              expect(subject.search).to eq([@content5, @content4, @content])
            end
          end

          context 'when its filtering a field column' do
            let(:search_params) do
              {
                "#{field1.id}": { filterType: filter_type, type: type, dateFrom: 12.days.ago.strftime('%Y-%m-%d 00:00:00'), dateTo: 4.days.ago.strftime('%Y-%m-%d 00:00:00') },
                "#{field2.id}": { filterType: filter_type, type: type, dateFrom: 6.days.ago.strftime('%Y-%m-%d 00:00:00'), dateTo: 2.days.ago.strftime('%Y-%m-%d 00:00:00') }
              }
            end

            it 'returns the contents that dates are between the filter dates' do
              expect(subject.search).to eq([@content6, @content5])
            end
          end
        end
      end

      context 'for number filter' do
        let(:filter_type) { ContentSearcher::NUMBER_FILTER_TYPE }

        before do
          field1.update_column(:type, :decimal)
          field2.update_column(:type, :integer)

          travel_to 4.days.ago do
            @content4 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 15.5, field2.id => 7 })
            end
          end

          travel_to 3.days.ago do
            @content5 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 12.8, field2.id => 9 })
            end
          end

          travel_to 2.days.ago do
            @content6 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 10.9, field2.id => 3 })
            end
          end

          travel_to 1.day.ago do
            @content7 = create(:content, business: business).tap do |content|
              content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 1.5, field2.id => 1 })
            end
          end
        end

        context "for #{ContentSearcher::EQUALS_FILTER} filter" do
          let(:type) { ContentSearcher::EQUALS_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 15.5 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 7 }
            }
          end

          it 'returns the contents that values are equal the filter' do
            expect(subject.search).to eq([@content4])
          end
        end

        context "for #{ContentSearcher::NOT_EQUAL_FILTER} filter" do
          let(:type) { ContentSearcher::NOT_EQUAL_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 15.5 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 7 }
            }
          end

          it 'returns the contents that values are not equal the filter' do
            expect(subject.search).to eq([@content7, @content6, @content5])
          end
        end

        context "for #{ContentSearcher::LESS_THAN_FILTER} filter" do
          let(:type) { ContentSearcher::LESS_THAN_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 12.8 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 9 }
            }
          end

          it 'returns the contents that values are less than the filter' do
            expect(subject.search).to eq([@content7, @content6])
          end
        end

        context "for #{ContentSearcher::LESS_THAN_OR_EQUAL_FILTER} filter" do
          let(:type) { ContentSearcher::LESS_THAN_OR_EQUAL_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 12.8 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 9 }
            }
          end

          it 'returns the contents that values are less than or equal the filter' do
            expect(subject.search).to eq([@content7, @content6, @content5])
          end
        end

        context "for #{ContentSearcher::GREATER_THAN_FILTER} filter" do
          let(:type) { ContentSearcher::GREATER_THAN_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 12.8 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 6 }
            }
          end

          it 'returns the contents that values are greater than the filter' do
            expect(subject.search).to eq([@content4])
          end
        end

        context "for #{ContentSearcher::GREATER_THAN_OR_EQUAL_FILTER} filter" do
          let(:type) { ContentSearcher::GREATER_THAN_OR_EQUAL_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 12.8 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 7 }
            }
          end

          it 'returns the contents that values are greater than or equal the filter' do
            expect(subject.search).to eq([@content5, @content4])
          end
        end

        context "for #{ContentSearcher::IN_RANGE_FILTER} filter" do
          let(:type) { ContentSearcher::IN_RANGE_FILTER }
          let(:search_params) do
            {
              "#{field1.id}": { filterType: filter_type, type: type, filter: 10.9, filterTo: 12.8 },
              "#{field2.id}": { filterType: filter_type, type: type, filter: 3, filterTo: 9 }
            }
          end

          it 'returns the contents that values are between the filter' do
            expect(subject.search).to eq([@content6, @content5])
          end
        end
      end

      context 'for join operator on same column' do
        context "for #{ContentSearcher::AND_OPERATOR} operator" do
          let(:search_params) do
            {
              "#{field1.id}": {
                operator: ContentSearcher::AND_OPERATOR,
                condition1: { type: ContentSearcher::STARTS_WITH_FILTER, filter: 'foo' },
                condition2: { type: ContentSearcher::ENDS_WITH_FILTER, filter: 'bar' }
              }
            }
          end

          before do
            travel_to 4.days.ago do
              @content4 = create(:content, business: business).tap do |content|
                content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'foo bar abc' })
              end
            end

            travel_to 3.days.ago do
              @content5 = create(:content, business: business).tap do |content|
                content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'abc foo bar' })
              end
            end

            travel_to 2.days.ago do
              @content6 = create(:content, business: business).tap do |content|
                content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'Foo abc baR' })
              end
            end
          end

          it 'returns the contents that contain the filter terms' do
            expect(subject.search).to eq([@content6])
          end
        end

        context "for #{ContentSearcher::OR_OPERATOR} operator" do
          let(:search_params) do
            {
              "#{field1.id}": {
                operator: ContentSearcher::OR_OPERATOR,
                condition1: { type: ContentSearcher::STARTS_WITH_FILTER, filter: 'foo' },
                condition2: { type: ContentSearcher::ENDS_WITH_FILTER, filter: 'bar' }
              }
            }
          end

          before do
            travel_to 4.days.ago do
              @content4 = create(:content, business: business).tap do |content|
                content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'Foo bar abc' })
              end
            end

            travel_to 3.days.ago do
              @content5 = create(:content, business: business).tap do |content|
                content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'abc foo bAr' })
              end
            end

            travel_to 2.days.ago do
              @content6 = create(:content, business: business).tap do |content|
                content.answers << create(:answer, :completed, step: step1, position: 0, content: content, values: { field1.id => 'abc foo bar abc' })
              end
            end
          end

          it 'returns the contents that contain the filter terms' do
            expect(subject.search).not_to match_array([@content5, @content4])
          end
        end
      end
    end

    context 'when the user is limited' do
      before do
        @user.update! limited: true
        @content2.update! draft: false, created_by: @user
      end

      it 'filters the records' do
        expect(subject.search).to eq([@content2])
      end
    end

    context 'when the user is part of a limited department' do
      before { @content2.update! draft: false }

      let!(:department) { create(:department, users: [@user, @content2.created_by], limited: true) }

      it 'filters the records' do
        expect(subject.search).to eq([@content2])
      end
    end
  end

  describe '#search_detail' do
    subject { described_class.new.search_detail(content_id) }

    let(:content_id) { @content.id }

    let(:business_group) { create(:business_group) }

    let(:state_template) { create(:template) }
    let(:city_template) { create(:template) }

    let(:state_business) { create(:business, business_group: business_group, name: 'state') }
    let(:city_business) { create(:business, business_group: business_group, name: 'city') }
    let(:expected_result) { [{ 'business_id' => state_business.id, 'content_id' => @sp_sate_content.id, 'field_label' => 'state', 'id' => nil, 'step_name' => city_business.steps.first.name, 'value' => 'SP' }] }

    before do
      @state_acronym_field = create(:field, template: state_template, type: :text, label: 'acronym')
      @city_state_field = create(:field, template: city_template, type: :reference, label: 'state', reference_business: state_business, reference_field: @state_acronym_field, reference_value_field: @state_acronym_field)
      @city_name_field = create(:field, template: city_template, type: :text, label: 'name')

      StepTemplate.create(step: create(:step, business: state_business), template: state_template)
      StepTemplate.create(step: create(:step, business: city_business), template: city_template)

      @sp_sate_content = create(:content, business: state_business).tap do |content|
        create(:answer, :completed, step: state_business.steps.first, position: 0, content: content, values: { @state_acronym_field.id => 'SP' })
      end

      create(:content, business: state_business).tap do |content|
        create(:answer, :completed, step: state_business.steps.first, position: 0, content: content, values: { @state_acronym_field.id => 'RJ' })
      end

      create(:content, deleted_at: Time.zone.yesterday, business: state_business).tap do |content|
        create(:answer, :completed, step: state_business.steps.first, position: 0, content: content, values: { @state_acronym_field.id => 'RJ' })
      end

      @content = create(:content, business: city_business).tap do |content|
        create(:answer, :completed, step: city_business.steps.first, position: 0, content: content, values: { @city_name_field.id => 'Jacarei', @city_state_field.id => 'SP' })
      end

      create(:content, business: city_business).tap do |content|
        create(:answer, :completed, step: city_business.steps.first, position: 0, content: content, values: { @city_name_field.id => 'Cunha', @city_state_field.id => 'SP' })
      end
    end

    it 'returns expected values' do
      is_expected.to eq expected_result
    end
  end
end
