# == Schema Information
#
# Table name: public.companies
#
#  id                              :uuid             not null, primary key
#  allowed_ips                     :string           default([]), not null, is an Array
#  allowed_sites                   :text             default([]), is an Array
#  api_key                         :string
#  auth_domain                     :text             default([]), is an Array
#  block_menus                     :string           default([]), is an Array
#  bypass_approval                 :boolean          default(FALSE), not null
#  chat_enabled                    :boolean          default(FALSE)
#  chatbot_enabled                 :boolean          default(FALSE)
#  contact_us_enabled              :boolean          default(FALSE)
#  custom_openid_name              :string
#  disable_tips                    :boolean          default(FALSE)
#  enable_email_and_password_login :boolean          default(TRUE), not null
#  enable_google_oauth             :boolean          default(FALSE), not null
#  enable_internationalization     :boolean          default(FALSE), not null
#  enable_microsoft_oauth          :boolean          default(FALSE), not null
#  enable_signup                   :boolean          default(FALSE), not null
#  expire_password_after_in_days   :integer          default(90), not null
#  limit_user_on_signup            :boolean          default(FALSE), not null
#  name                            :string           not null
#  not_validate_auth_domain_openid :boolean          default(FALSE), not null
#  open_id_config                  :jsonb
#  page_title                      :string
#  restrict_access_by_ip           :boolean          default(FALSE), not null
#  smtp_address                    :string
#  smtp_custom                     :boolean          default(FALSE)
#  smtp_from                       :string
#  smtp_password                   :string
#  smtp_port                       :string
#  smtp_starttls                   :boolean          default(TRUE)
#  smtp_username                   :string
#  subdomain                       :string           not null
#  token_life_span_in_minutes      :integer          default(4320), not null
#  twilio_api_key                  :string
#  twilio_api_secret               :string
#  twilio_auth_token               :string
#  twilio_chat_service_sid         :string
#  twilio_sid                      :string
#  use_elasticsearch               :boolean          default(FALSE)
#  welcome_video_url               :string
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  default_department_id           :uuid
#  enable_open_id                  :boolean          default(FALSE)
#  theme_id                        :bigint           default(1)
#  waf_ip_set_id                   :string
#  waf_rule_group_id               :string
#
# Indexes
#
#  index_companies_on_theme_id  (theme_id)
#
require 'rails_helper'

RSpec.describe Company, type: :model do
  subject { build(:company) }

  describe 'associations' do
    it { is_expected.to belong_to(:theme).optional }
    it { should have_many(:bulk_destroying_contents).dependent(:destroy) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:subdomain) }
    it { is_expected.to validate_uniqueness_of(:subdomain).case_insensitive }
    it { is_expected.to validate_uniqueness_of(:name).case_insensitive }
    it { is_expected.to validate_numericality_of(:token_life_span_in_minutes).only_integer.is_greater_than_or_equal_to(5) }

    it 'is invalid if has gmail provider and no domain' do
      expect(build(:company, enable_google_oauth: true, auth_domain: [])).to be_invalid
    end

    it 'is invalid if has microsoft provider and no domain' do
      expect(build(:company, enable_microsoft_oauth: true, auth_domain: [])).to be_invalid
    end

    it 'is invalid if has open id provider and no domain' do
      expect(build(:company, enable_open_id: true, auth_domain: [])).to be_invalid
    end

    context 'when restrict access by ip is true' do
      let(:company) { build(:company, restrict_access_by_ip: true, allowed_ips: [Faker::Internet.ip_v4_cidr, Faker::Internet.ip_v4_address, invalid_ip]) }

      context 'for an invalid IP' do
        let(:invalid_ip) { 'foobar' }

        it 'is invalid' do
          expect(company).to be_invalid
        end

        it 'returns the error message' do
          company.valid?

          expect(company.errors.full_messages).to eq(["O IP #{invalid_ip} não é válido."])
        end
      end

      context 'for an IPV6 address' do
        let(:invalid_ip) { Faker::Internet.ip_v6_address }

        it 'is invalid' do
          expect(company).to be_invalid
        end

        it 'returns the error message' do
          company.valid?

          expect(company.errors.full_messages).to eq(["O IP #{invalid_ip} não é válido."])
        end
      end

      context 'for an IPV6 address' do
        let(:invalid_ip) { Faker::Internet.ip_v6_cidr }

        it 'is invalid' do
          expect(company).to be_invalid
        end

        it 'returns the error message' do
          company.valid?

          expect(company.errors.full_messages).to eq(["O IP #{invalid_ip} não é válido."])
        end
      end
    end

    context 'when the open id login is enabled' do
      subject { build(:company, enable_open_id: true, open_id_config: config, auth_domain: ['foo.bar']) }

      context 'for missing host information' do
        let(:config) { { host: '', identifier: 'abc123', secret: '123abc' } }

        it { is_expected.to be_invalid }

        it 'returns the error message' do
          subject.valid?

          expect(subject.errors[:open_id_config]).to include('Configuração do OpenID inválida, por favor revise os dados.')
        end
      end

      context 'for missing identifier information' do
        let(:config) { { host: 'abc.com.br', identifier: '', secret: '123abc' } }

        it { is_expected.to be_invalid }

        it 'returns the error message' do
          subject.valid?

          expect(subject.errors[:open_id_config]).to include('Configuração do OpenID inválida, por favor revise os dados.')
        end
      end

      context 'for missing secret information' do
        let(:config) { { host: 'abc.com.br', identifier: 'abc123', secret: '' } }

        it { is_expected.to be_invalid }

        it 'returns the error message' do
          subject.valid?

          expect(subject.errors[:open_id_config]).to include('Configuração do OpenID inválida, por favor revise os dados.')
        end
      end

      describe 'validate_block_menus' do
        context 'when menu name is valid' do
          it 'is valid' do
            expect(build(:company, block_menus: %w[dashboard favorites])).to be_valid
          end
        end

        context 'when menu name is not valid' do
          it 'is invalid' do
            user = build(:user, block_menus: %w[dashboard favorites foo])

            expect(user).to be_invalid
            expect(user.errors[:base]).to include('Menu informado não é permitido')
          end
        end
      end
    end
  end

  describe 'when bypass_approval' do
    department_list = FactoryBot.create_list(:department, 3)

    context 'is true' do
      it 'validates default_department_id presence' do
        expect(build(:company, bypass_approval: true)).to be_invalid
      end
    end

    context 'is false' do
      it 'do not validate default_department_id' do
        company = build(:company)
        expect(company).to be_valid
      end
    end
  end

  describe 'chatbot_enabled field' do
    it 'defaults chatbot_enabled to false' do
      company = create(:company)
      expect(company.chatbot_enabled).to be(false)
    end

    it 'allows chatbot_enabled to be updated' do
      company = create(:company, chatbot_enabled: false)
      company.update(chatbot_enabled: true)
      expect(company.chatbot_enabled).to be(true)
    end
  end

  describe 'contact_us_enabled field' do
    it 'defaults contact_us_enabled to false' do
      company = create(:company)
      expect(company.contact_us_enabled).to be(false)
    end

    it 'allows contact_us_enabled to be updated' do
      company = create(:company, contact_us_enabled: false)
      company.update(contact_us_enabled: true)
      expect(company.contact_us_enabled).to be(true)
    end
  end

  describe 'after creation' do
    it 'creates new tenant' do
      expect(Apartment::Tenant).to receive(:create).with(subject.subdomain)

      subject.save
    end
  end

  describe 'after destroying' do
    it 'drop tenant' do
      expect(Apartment::Tenant).to receive(:drop).with(subject.subdomain)

      subject.destroy
    end
  end

  describe 'before destroying' do
    let!(:business) { create(:business, :with_dependencies) }
    let!(:bulk) { create(:bulk_destroying_content, :with_dependencies, business_id: business.id, business_name: business.name) }
    let!(:subject) { bulk.company }

    it 'destroy all bulk destroying contents' do
      expect { subject.destroy }.to change(BulkDestroyingContent, :count).by(-1)
    end
  end

  describe 'before creation' do
    subject { build(:company) }

    before { allow(SecureRandom).to receive(:hex).and_call_original }

    it 'creates a new api_key' do
      expect(SecureRandom).to receive(:hex).with(64).and_call_original

      subject.save

      expect(subject.api_key).to_not be_nil
    end
  end

  describe 'check_auth_domain' do
    subject { create(:company, enable_google_oauth: false, enable_microsoft_oauth: false, enable_open_id: false, auth_domain: %w[foobar lorem ipsum]) }

    it 'clean auth_domain if fourmdg is provider' do
      expect(subject.auth_domain).to be_empty
    end
  end

  describe 'upload images' do
    context 'when upload logo image' do
      it 'creates image' do
        @company = create(:company, subdomain: 'marabraz')

        @company.logo_image.attach(io: File.open(file_fixture('logo.png')), filename: 'logo.png', content_type: 'image/png')

        expect(@company.logo_image).to be_attached
      end
    end

    context 'when upload background image' do
      it 'creates image' do
        @company = create(:company, subdomain: 'another company')

        @company.background_image.attach(io: File.open(file_fixture('bg_company.jpg')), filename: 'bg_company.jpeg', content_type: 'image/jpeg')

        expect(@company.background_image).to be_attached
      end
    end

    context 'when upload internal logo image' do
      it 'creates image' do
        @company = create(:company, subdomain: 'another company')

        @company.internal_logo_image.attach(io: File.open(file_fixture('logo.png')), filename: 'logo.png', content_type: 'image/png')

        expect(@company.internal_logo_image).to be_attached
      end
    end

    context 'when upload favicon image' do
      it 'creates image' do
        @company = create(:company, subdomain: 'another company')

        @company.favicon_image.attach(io: File.open(file_fixture('favicon.png')), filename: 'favicon.png', content_type: 'image/png')

        expect(@company.favicon_image).to be_attached
      end
    end
  end

  describe 'after saving' do
    context 'for allowed ips' do
      let(:aws_service) { class_spy(Aws::WafService, new: service) }
      let(:service) { instance_double(Aws::WafService, update_allowed_ips: true) }
      let(:company) { create(:company, subdomain: 'foobarbatz', restrict_access_by_ip: false) }

      before { stub_const('Aws::WafService', aws_service) }

      context 'when neither allowed ips nor restrict access by ip have changed' do
        before { company.update!(name: 'foobar inc') }

        it 'does not initialize the waf service' do
          expect(aws_service).not_to have_received(:new)
        end

        it 'does not update the allowed ips configuration on waf' do
          expect(service).not_to have_received(:update_allowed_ips)
        end
      end

      context 'when restrict access by ip have changed' do
        before { company.update!(restrict_access_by_ip: true) }

        it 'initializes the waf service' do
          expect(aws_service).to have_received(:new).with(company)
        end

        it 'updates the allowed ips configuration on waf' do
          expect(service).to have_received(:update_allowed_ips)
        end
      end
    end

    context 'for twilio configuration' do
      let(:name) { 'marabraz' }

      context 'when the chat is disabled' do
        let(:chat_enabled) { false }

        it 'does not initialize the twilio subaccount service' do
          expect(Twilio::SubaccountService).not_to receive(:new)

          create(:company, name: name, chat_enabled: chat_enabled)
        end

        it 'does not create a subaccount' do
          expect_any_instance_of(Twilio::SubaccountService).not_to receive(:create)

          create(:company, name: name, chat_enabled: chat_enabled)
        end

        context 'when disabling chat' do
          let(:company) { create(:company, name: name, chat_enabled: true, twilio_sid: '123', twilio_auth_token: 'abc123', twilio_chat_service_sid: 'IS444', twilio_api_key: 'SK123', twilio_api_secret: 'foobar') }

          it 'does not remove the twilio api key' do
            expect { company.update(chat_enabled: false) }.not_to(change { company.reload.twilio_api_key })
          end

          it 'does not remove the twilio api secret' do
            expect { company.update(chat_enabled: false) }.not_to(change { company.reload.twilio_api_secret })
          end

          it 'removes the twilio auth token' do
            expect { company.update(chat_enabled: false) }.to change { company.reload.twilio_auth_token }.from('abc123').to(nil)
          end

          it 'removes the twilio chat service sid' do
            expect { company.update(chat_enabled: false) }.to change { company.reload.twilio_chat_service_sid }.from('IS444').to(nil)
          end

          it 'removes the twilio sid' do
            expect { company.update(chat_enabled: false) }.to change { company.reload.twilio_sid }.from('123').to(nil)
          end
        end
      end

      context 'when the chat is enabled' do
        let(:chat_enabled) { true }
        let(:twilio_service) { double(:twilio_service, create: true, success?: true, record: subaccount_data) }
        let(:subaccount_data) { double(Twilio::REST::Api::V2010::AccountInstance, auth_token: 'abc123', sid: '123abc') }
        let(:twilio_configuration_service) { double(:twilio_configuration_service, fetch: true, success?: true, record: configuration_data, create_key: true, enable_reachability_indicator: true) }
        let(:configuration_data) { double(:configuration_data, default_chat_service_sid: 'IS123', secret: 'secret', sid: 'SK123') }

        before do
          allow(Twilio::SubaccountService).to receive(:new).and_return(twilio_service)
          allow(Twilio::ConfigurationService).to receive(:new).and_return(twilio_configuration_service)
        end

        it 'initializes the twilio subaccount service' do
          expect(Twilio::SubaccountService).to receive(:new).and_return(twilio_service)

          create(:company, name: name, chat_enabled: chat_enabled)
        end

        it 'tries to create the subaccount' do
          expect(twilio_service).to receive(:create).with(name)

          create(:company, name: name, chat_enabled: chat_enabled)
        end

        context 'with error' do
          let(:twilio_service) { double(:twilio_service, create: false, success?: false) }

          it 'does not update the company twilio sid' do
            expect { create(:company, name: name, chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_sid)
          end

          it 'does not update the company twilio auth token' do
            expect { create(:company, name: name, chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_auth_token)
          end
        end

        context 'with success' do
          let(:company) { create(:company, name: name, chat_enabled: false) }

          it 'updates the company twilio sid' do
            expect { company.update(chat_enabled: chat_enabled) }.to change { company.reload.twilio_sid }.from(nil).to(subaccount_data.sid)
          end

          it 'updates the company twilio auth token' do
            expect { company.update(chat_enabled: chat_enabled) }.to change { company.reload.twilio_auth_token }.from(nil).to(subaccount_data.auth_token)
          end

          it 'initializes the twilio configuration service' do
            expect(Twilio::ConfigurationService).to receive(:new).and_return(twilio_configuration_service)

            create(:company, name: name, chat_enabled: chat_enabled)
          end

          context 'when the company does not have a previous api key and secret' do
            let(:configuration_data) { double(:configuration_data, default_chat_service_sid: 'IS123', secret: 'secret', sid: 'SK123') }

            it 'tries to create the api key' do
              expect(twilio_configuration_service).to receive(:create_key).with(company.name)

              company.update(chat_enabled: chat_enabled)
            end

            context 'with error' do
              let(:twilio_configuration_service) { double(:twilio_configuration_service, fetch: false, success?: false, create_key: false) }

              it 'does not update the company twilio api key' do
                expect { create(:company, name: name, chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_api_key)
              end

              it 'does not update the company twilio api secret' do
                expect { create(:company, name: name, chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_api_secret)
              end
            end

            context 'with success' do
              it 'updates the company twilio api key' do
                expect { company.update(chat_enabled: chat_enabled) }.to change { company.reload.twilio_api_key }.from(nil).to(configuration_data.sid)
              end

              it 'updates the company twilio api secret' do
                expect { company.update(chat_enabled: chat_enabled) }.to change { company.reload.twilio_api_secret }.from(nil).to(configuration_data.secret)
              end
            end
          end

          context 'when the company has a previous api key and secret' do
            let(:company) { create(:company, name: name, chat_enabled: false, twilio_api_key: 'SK133', twilio_api_secret: 'SK888') }

            it 'does not try to create the api key' do
              expect(twilio_configuration_service).not_to receive(:create_key)

              company
            end

            it 'does not update the company twilio api key' do
              expect { company.update(chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_api_key)
            end

            it 'does not update the company twilio api secret' do
              expect { company.update(chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_api_secret)
            end
          end

          it 'tries to fetch the conversation configuration' do
            expect(twilio_configuration_service).to receive(:fetch)

            create(:company, name: name, chat_enabled: chat_enabled)
          end

          context 'with error' do
            let(:twilio_configuration_service) { double(:twilio_configuration_service, fetch: false, success?: false, create_key: false) }

            it 'does not update the company twilio chat service sid' do
              expect { create(:company, name: name, chat_enabled: chat_enabled) }.not_to change(Company.last, :twilio_chat_service_sid)
            end
          end

          context 'with success' do
            it 'updates the company twilio chat service sid' do
              expect { company.update(chat_enabled: chat_enabled) }.to change { company.reload.twilio_chat_service_sid }.from(nil).to(configuration_data.default_chat_service_sid)
            end

            it 'enables the reachability indicator' do
              expect(twilio_configuration_service).to receive(:enable_reachability_indicator).with(configuration_data.default_chat_service_sid)

              company.update(chat_enabled: chat_enabled)
            end
          end
        end
      end
    end
  end

  describe 'before_save' do
    context 'allowed_ips' do
      context 'when the restrict_access_by_ip is updated to false' do
        let(:ip1) { Faker::Internet.ip_v4_cidr }
        let(:ip2) { Faker::Internet.ip_v4_cidr }
        let(:company) { create(:company, restrict_access_by_ip: true, allowed_ips: [ip1, ip2]) }

        it 'sets the allowed_ips to nil' do
          expect { company.update! restrict_access_by_ip: false }.to change { company.reload.allowed_ips }.from([ip1, ip2]).to([])
        end
      end
    end

    context 'enable_signup' do
      context 'when the enable_signup is updated to false' do
        let(:company) { create(:company, enable_signup: true, limit_user_on_signup: true) }

        it 'sets the limit_user_on_signup to false' do
          expect { company.update! enable_signup: false }.to change { company.reload.limit_user_on_signup? }.from(true).to(false)
        end
      end
    end

    context 'disabling open id login' do
      subject { create(:company, enable_open_id: true, open_id_config: { host: 'foo.bar.com', identifier: 'abc123', secret: '123abc' }, auth_domain: ['foo.bar']) }

      it 'resets the open id configuration' do
        expect { subject.update! enable_open_id: false }.to change { subject.reload.open_id_config }.to(nil)
      end
    end
  end

  describe '#waf_ip_set_arn' do
    context 'when there is no ip set id' do
      subject { create(:company, waf_ip_set_id: nil).waf_ip_set_arn }

      it { is_expected.to be_nil }
    end

    context 'when there is an ip set id' do
      subject { company.waf_ip_set_arn }

      let(:company) { create(:company, waf_ip_set_id: 'foobar') }

      it { is_expected.to eq("arn:aws:wafv2:#{Rails.application.credentials.aws_region}:#{Rails.application.credentials.aws_account_id}:global/ipset/#{company.subdomain}-#{Rails.env}/#{company.waf_ip_set_id}") }
    end
  end

  describe '#waf_rule_group_arn' do
    context 'when there is no rule group id' do
      subject { create(:company, waf_rule_group_id: nil).waf_rule_group_arn }

      it { is_expected.to be_nil }
    end

    context 'when there is an rule group id' do
      subject { company.waf_rule_group_arn }

      let(:company) { create(:company, waf_rule_group_id: 'foobatz') }

      it { is_expected.to eq("arn:aws:wafv2:#{Rails.application.credentials.aws_region}:#{Rails.application.credentials.aws_account_id}:global/rulegroup/#{company.subdomain}-#{Rails.env}/#{company.waf_rule_group_id}") }
    end
  end

  describe '#allowed_ips_cidr' do
    context 'when there is no allowed ips' do
      subject { create(:company, allowed_ips: nil).allowed_ips_cidr }

      it { is_expected.to be_empty }
    end

    context 'when there are allowed ips' do
      subject { company.allowed_ips_cidr }

      let(:ip1) { Faker::Internet.ip_v4_cidr }
      let(:ip2) { Faker::Internet.ip_v4_address }
      let(:company) { create(:company, restrict_access_by_ip: true, allowed_ips: [ip1, ip2]) }

      it { is_expected.to eq([NetAddr::IPv4Net.parse(ip1).to_s, NetAddr::IPv4Net.parse(ip2).to_s]) }
    end
  end
end
