# == Schema Information
#
# Table name: contents
#
#  id                :uuid             not null, primary key
#  concluded_at      :datetime
#  created_by_ip     :inet
#  deleted_at        :datetime
#  deletion_reason   :string
#  draft             :boolean          default(FALSE)
#  keywords          :string
#  name              :string           not null
#  note              :text
#  status            :integer          default("pending"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  business_id       :uuid
#  created_by_id     :uuid
#  current_answer_id :uuid
#  deleted_by_id     :uuid
#  parent_id         :uuid
#
# Indexes
#
#  index_contents_on_business_id        (business_id)
#  index_contents_on_created_by_id      (created_by_id)
#  index_contents_on_current_answer_id  (current_answer_id)
#  index_contents_on_deleted_at         (deleted_at)
#  index_contents_on_deleted_by_id      (deleted_by_id)
#  index_contents_on_draft              (draft)
#  index_contents_on_parent_id          (parent_id)
#  index_contents_on_status             (status)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (current_answer_id => answers.id)
#  fk_rails_...  (deleted_by_id => users.id)
#  fk_rails_...  (parent_id => contents.id)
#
require 'rails_helper'

RSpec.describe Content, type: :model do
  let(:business_group) { create(:business_group) }
  let(:business) { create(:business, business_group:) }

  describe 'associations' do
    it { is_expected.to belong_to(:business) }
    it { is_expected.to belong_to(:current_answer).optional }
    it { is_expected.to have_many(:answers).order(position: :asc) }
    it { is_expected.to have_many(:steps).through(:answers) }
  end

  describe 'enums' do
    it do
      is_expected.to define_enum_for(:status).with_values(
        pending: 0,
        waiting_authorization: 1,
        done: 2,
        changing: 3,
        under_review: 4,
        rejected: 5
      )
    end
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }

    it 'fails if the business is not present' do
      expect(build(:content, business: nil)).to be_invalid
    end

    it 'fails if the business is inactive' do
      business.discard

      expect(build(:content, business:)).to be_invalid
    end

    context 'for sub_content' do
      let(:sub_business) { create(:business, :with_dependencies, sub_business: true) }

      subject { build(:content, business_id: sub_business.id, parent_id:) }

      context 'with parent_id' do
        let(:parent_content) { create(:content, :with_dependencies) }
        let(:parent_id) { parent_content.id }

        context 'when parent_content has field with reference_sub_business_id = content.business_id' do
          let(:sub_business_field) { create(:field, :with_dependencies, type: :sub_business, reference_sub_business_id: sub_business.id) }

          before do
            step = create(:step, business: parent_content.business)

            create(:step_template, step:, template: sub_business_field.template)
          end

          it { is_expected.to be_valid }
        end

        context 'when parent_content has no field with reference_sub_business_id = content.business_id' do
          it { is_expected.to be_invalid }
        end
      end

      context 'without parent_id' do
        let(:parent_id) { nil }

        it { is_expected.to be_invalid }
      end
    end

    (Content.statuses.values - [Content.statuses[:done]]).each do |status|
      context "for a #{Content.statuses.key(status)} content" do
        it 'fails if the concluded at is present' do
          content = build(:content, business:, status:, concluded_at: Time.zone.now)

          expect(content).to be_invalid
          expect(content.errors[:concluded_at]).to_not be_empty
        end
      end
    end

    context 'for a done content' do
      it 'fails if the concluded at is not present' do
        content = build(:content, business:, status: Content.statuses[:done], concluded_at: nil)

        expect(content).to be_invalid
        expect(content.errors[:concluded_at]).to_not be_empty
      end
    end

    it 'passes' do
      expect(build(:content, business:)).to be_valid
    end

    context 'on restore' do
      before { content.current_user = create(:user) }

      context 'for duplicated pk' do
        let(:answer) { create(:answer, :with_dependencies, :completed) }
        let(:pk_field) { answer.current_fields.first }
        let(:business) { answer.content.business }
        let(:content) { answer.content }

        before do
          business.update!(key_field_ids: [pk_field.id])
          answer.update(values: { pk_field.id => '321' })

          content.discard
        end

        context 'when pk_field already exists' do
          before do
            other_content = create(:content, business:)

            create(:answer, :completed, content: other_content, step: answer.step, values: { pk_field.id => '321' })
          end

          it 'is invalid' do
            expect(content.undiscard).to be false
            expect(content.errors.full_messages).to eq(["Violação de campo(s) chave(s) \"#{pk_field.label}\", não é possível inserir valor duplicado"])
          end
        end

        context 'when pk_field does not exists' do
          it 'is valid' do
            expect(content.undiscard).to be true
          end
        end
      end
    end
  end

  describe 'exists_other_content_for_same_pk' do
    let(:answer) { create(:answer, :with_dependencies, user: create(:user)) }
    let(:pk_field) { answer.current_fields.first }
    let(:business) { answer.content.business }
    let(:content) { answer.content }

    before do
      business.update!(key_field_ids: [pk_field.id])
      answer.update(values: { pk_field.id => '321' })

      content.current_user = create(:user)
      content.discard
    end

    context 'when pk_field already exists' do
      before do
        other_content = create(:content, business:)

        create(:answer, :completed, content: other_content, step: answer.step, values: { pk_field.id => '321' })
      end

      it { expect(content.exists_other_content_for_same_pk).to be true }
    end

    context 'when pk_field does not exists' do
      it { expect(content.exists_other_content_for_same_pk).to be false }
    end

    context 'for composite keys' do
      let(:pk_field2) { create(:field, template: pk_field.template) }

      before do
        business.update!(key_field_ids: [pk_field.id, pk_field2.id])
        answer.update(values: { pk_field.id => '321', pk_field2.id => '123' })

        content.discard
      end

      context 'when both pk_field already exists' do
        before do
          other_content = create(:content, business:)

          create(:answer, :completed, content: other_content, step: answer.step, values: { pk_field.id => '321', pk_field2.id => '123' })
        end

        it { expect(content.exists_other_content_for_same_pk).to be true }
      end

      context 'when only one pk_field already exists' do
        before do
          other_content = create(:content, business:)

          create(:answer, :completed, content: other_content, step: answer.step, values: { pk_field.id => '321', pk_field2.id => 'xxx' })
        end

        it { expect(content.exists_other_content_for_same_pk).to be false }
      end
    end
  end

  describe 'finding content by pk_field' do
    let(:answer) { create(:answer, :with_dependencies, :completed) }
    let(:pk_field) { answer.current_fields.first }
    let(:business) { answer.content.business }
    let(:content) { answer.content }
    let(:values) { { pk_field.id => '321' } }

    before do
      business.update!(key_field_ids: [pk_field.id])
      answer.update(values:)
    end

    context 'when pk_field exists' do
      it { expect(Content.for_pks(business.id, values).first.id).to eq(content.id) }
    end

    context 'when pk_field does not exists' do
      let(:values) { { pk_field.id => '123' } }
      before { content.tap { |content| content.current_user = create(:user) }.discard }

      it { expect(Content.for_pks(business.id, values)).to be_empty }
    end

    context 'for composite keys' do
      let(:pk_field2) { create(:field, template: pk_field.template) }
      let(:values) { { pk_field.id => '123', pk_field2.id => '321' } }

      before do
        business.update!(key_field_ids: [pk_field.id, pk_field2.id])
        answer.update(values:)
      end

      context 'when both pk_field already exists' do
        before do
          other_content = create(:content, business:)
          create(:answer, user: create(:user), content: other_content, step: answer.step, values:)
          content.current_user = create(:user)
          content.discard
        end

        it { expect(Content.for_pks(business.id, values).first.id).to eq(business.contents.first.id) }
      end

      context 'when only one pk_field already exists' do
        before do
          content.current_user = create(:user)
          content.discard
          other_content = create(:content, business:)
          create(:answer, :completed, content: other_content, step: answer.step, values: { pk_field.id => '321', pk_field2.id => 'xxx' })
        end

        it { expect(Content.for_pks(business.id, values)).to be_empty }
      end

      context 'when an pk_field is not given' do
        before do
          content.current_user = create(:user)
          content.discard
          other_content = create(:content, business:)
          create(:answer, :completed, content: other_content, step: answer.step, values: { pk_field.id => '', pk_field2.id => '' })
        end

        it 'searchs for an empty string with the missing pk_field id' do
          expect(Content.for_pks(business.id, {}).first.id).to eq(business.contents.first.id)
        end
      end
    end
  end

  describe 'answer based information' do
    let(:user) { create(:user) }
    let(:step1) { create(:step, business:) }
    let(:step2) { create(:step, business:) }
    let(:step3) { create(:step, business:) }
    let(:step4) { create(:step, business:) }
    let(:template) { create(:template) }
    let(:field) { create(:field, :random_type, template:, required: false) }

    context 'when the content has pending answers' do
      before do
        @content = create(:content, business:).tap do |content|
          values = { 'foo' => 'bar', 'bar' => 'batz' }
          answer = build(:answer, :completed, step: step1, position: 0, content:, values:)
          answer.save!

          values = { 'foo' => 'bar', 'bar' => 'batz' }
          answer = build(:answer, :completed, step: step2, position: 1, content:, values:)
          answer.save!

          answer = build(:answer, step: step3, position: 2, content:, values: nil)
          answer.save!

          answer = build(:answer, step: step4, position: 3, content:, values: nil, user: create(:user))
          answer.save!

          content.update_progress
        end
      end

      describe '#current_step_name' do
        it 'returns the current step name' do
          expect(@content.current_step_name).to eq(@content.answers[2].step.name)
        end
      end

      describe '#current_step' do
        it 'returns the position of the current answer' do
          expect(@content.current_step).to eq(@content.answers[2].position)
        end
      end

      describe '#last_update_at' do
        it 'returns the last update time for the answers' do
          expect(@content.last_update_at).to eq(@content.answers.last.updated_at)
        end
      end

      describe '#last_update_by' do
        it 'returns the last update user for the answers' do
          expect(@content.last_update_by).to eq(@content.answers.last.user)
        end
      end

      describe '#after_undiscard' do
        before do
          @content.current_user = create(:user)
          @content.discard
          @content.reload
        end

        it 'undiscard the answers' do
          expect(@content.answers[0].deleted_at).to_not be_nil
          expect(@content.answers[1].deleted_at).to_not be_nil
          expect(@content.answers[2].deleted_at).to_not be_nil
          expect(@content.answers[3].deleted_at).to_not be_nil

          @content.undiscard
          @content.reload

          expect(@content.answers[0].deleted_at).to be_nil
          expect(@content.answers[1].deleted_at).to be_nil
          expect(@content.answers[2].deleted_at).to be_nil
          expect(@content.answers[3].deleted_at).to be_nil
        end
      end
    end

    context 'when the content does not have pending answers' do
      before do
        @content = create(:content, business:).tap do |content|
          values = { 'foo' => 'bar', 'bar' => 'batz' }
          create(:answer, :completed, step: step1, position: 0, content:, values:)

          values = { 'foo' => 'bar', 'bar' => 'batz' }
          create(:answer, :completed, step: step2, position: 1, content:, values:)

          values = { 'foo' => 'bar', 'bar' => 'batz' }
          create(:answer, :completed, step: step3, position: 2, content:, values:)

          values = { 'foo' => 'bar', 'bar' => 'batz' }
          create(:answer, :completed, step: step4, position: 5, content:, values:)
        end

        @content.reload
      end

      describe '#current_step_name' do
        it 'returns the current step name' do
          expect(@content.current_step_name).to eq(@content.answers[3].step.name)
        end
      end

      describe '#current_step' do
        it 'returns the last answer index' do
          expect(@content.current_step).to eq(3)
        end
      end

      describe '#last_update_at' do
        it 'returns the last update time for the answers' do
          expect(@content.last_update_at).to eq(@content.answers.last.updated_at)
        end
      end
    end
  end

  describe '#values' do
    let(:user) { create(:user) }

    before do
      @content = create(:content, business:).tap do |content|
        content.answers << create(:answer, step: create(:step, business:), position: 0, content:)

        answer = content.answers.first

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 1, content:)

        answer = content.answers.second

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'batz' => '123' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 2, content:)
      end

      @answer1 = @content.answers.first
      @answer2 = @content.answers.second
      @answer3 = @content.answers.third
    end

    it 'returns the values for the answers' do
      expect(@content.values).to match_array([@answer1.values, @answer2.values, []])
    end
  end

  describe '#update_progress' do
    let(:user) { create(:user) }
    let(:step_01) { create(:step, business:, order: 0) }
    let(:step_02) { create(:step, business:, order: 1) }
    let(:step_03) { create(:step, business:, order: 2) }
    let(:step_04) { create(:step, business:, order: 3) }
    let(:content) { create(:content, business:, draft: true) }
    let!(:answer_01) { create(:answer, step: step_01, position: 0, content:) }
    let!(:answer_02) { create(:answer, step: step_02, position: 1, content:) }
    let!(:answer_03) { create(:answer, step: step_03, position: 2, content:) }
    let!(:answer_04) { create(:answer, step: step_04, position: 3, content:) }

    context 'when the content has no concluded answer' do
      it 'does not change the status' do
        expect(content.status).to eq('pending')
      end

      it 'does not change the draft flag' do
        expect(content.draft).to be_truthy
      end

      it 'does not change the concluded at' do
        expect(content.concluded_at).to be_nil
      end
    end

    context 'when the answers change status to pending' do
      context 'when all answers are completed and update first as pending' do
        before do
          answer_01.status = :pending
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update second as pending' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :pending
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update third as pending' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :pending
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update fourth as pending' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :pending
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end
    end

    context 'when the answers change status to done' do
      context 'when the content has one concluded answer' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when the content has 2 answers concluded answer' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when the content has 3 answers concluded answer' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('pending')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done empty' do
          expect(content.first_status_not_done).to eq({})
        end

        it 'sets the status as done' do
          expect(content.status).to eq('done')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'change the concluded at' do
          expect(content.concluded_at).to_not be_nil
        end
      end
    end

    context 'when the answers change status to waiting_authorization' do
      context 'when all answers are completed and update first as waiting_authorization' do
        before do
          answer_01.status = :waiting_authorization
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end

        it 'sets the status as waiting_authorization' do
          expect(content.status).to eq('waiting_authorization')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update second as waiting_authorization' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :waiting_authorization
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end

        it 'sets the status as waiting_authorization' do
          expect(content.status).to eq('waiting_authorization')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update third as waiting_authorization' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :waiting_authorization
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end

        it 'sets the status as waiting_authorization' do
          expect(content.status).to eq('waiting_authorization')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update fourth as waiting_authorization' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :waiting_authorization
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end

        it 'sets the status as waiting_authorization' do
          expect(content.status).to eq('waiting_authorization')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end
    end

    context 'when the answers change status to rejected' do
      context 'when all answers are completed and update first as rejected' do
        before do
          answer_01.status = :rejected
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end

        it 'sets the status as rejected' do
          expect(content.status).to eq('rejected')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update second as rejected' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :rejected
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end

        it 'sets the status as rejected' do
          expect(content.status).to eq('rejected')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update third as rejected' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :rejected
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end

        it 'sets the status as rejected' do
          expect(content.status).to eq('rejected')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update fourth as rejected' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :rejected
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end

        it 'sets the status as rejected' do
          expect(content.status).to eq('rejected')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end
    end

    context 'when the answers change status to changing' do
      context 'when all answers are completed and update first as changing' do
        before do
          answer_01.status = :changing
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end

        it 'sets the status as changing' do
          expect(content.status).to eq('changing')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update second as changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :changing
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end

        it 'sets the status as changing' do
          expect(content.status).to eq('changing')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update third as changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :changing
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end

        it 'sets the status as changing' do
          expect(content.status).to eq('changing')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update fourth as changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :changing
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end

        it 'sets the status as changing' do
          expect(content.status).to eq('changing')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end
    end

    context 'when the answers change status to under_review' do
      context 'when all answers are completed and update first as under_review' do
        before do
          answer_01.status = :under_review
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end

        it 'sets the status as under_review' do
          expect(content.status).to eq('under_review')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update second as under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :under_review
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end

        it 'sets the status as under_review' do
          expect(content.status).to eq('under_review')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update third as under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :under_review
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end

        it 'sets the status as under_review' do
          expect(content.status).to eq('under_review')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when all answers are completed and update fourth as under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :under_review
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end

        it 'sets the status as under_review' do
          expect(content.status).to eq('under_review')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end
    end

    context 'when the answers mix status' do
      context 'when the answers 1 is done, the answer 2 is waiting_authorization, the answer 3 is rejected and the last is done' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :waiting_authorization
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :rejected
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end

        it 'sets the status as waiting_authorization' do
          expect(content.status).to eq('waiting_authorization')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when the answers 1 is done, the answer 2 is waiting_authorization, the answer 3 is rejected and the last is under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :waiting_authorization
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :rejected
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :under_review
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end

        it 'sets the status as waiting_authorization' do
          expect(content.status).to eq('waiting_authorization')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when the first answers is rejected, the second is done, the answer 3 is under_review and the last is changing' do
        before do
          answer_01.status = :rejected
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :under_review
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :changing
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('rejected')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end

      context 'when the answers 1 and 2 are completed, the answer 3 is under_review and the last is changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :under_review
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :changing
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end

        it 'sets the status as pending' do
          expect(content.status).to eq('under_review')
        end

        it 'sets the draft flag to false' do
          expect(content.draft).to be_falsey
        end

        it 'does not change the concluded at' do
          expect(content.concluded_at).to be_nil
        end
      end
    end
  end

  describe '#first_status_not_done' do
    let(:user) { create(:user) }
    let(:step_01) { create(:step, business:, order: 0) }
    let(:step_02) { create(:step, business:, order: 1) }
    let(:step_03) { create(:step, business:, order: 2) }
    let(:step_04) { create(:step, business:, order: 3) }
    let(:content) { create(:content, business:, draft: true) }
    let!(:answer_01) { create(:answer, step: step_01, position: 0, content:) }
    let!(:answer_02) { create(:answer, step: step_02, position: 1, content:) }
    let!(:answer_03) { create(:answer, step: step_03, position: 2, content:) }
    let!(:answer_04) { create(:answer, step: step_04, position: 3, content:) }

    context 'when the content has no concluded answer' do
      it 'returns the first status not done as pending' do
        expect(content.first_status_not_done[:status]).to eq('pending')
      end
    end

    context 'when the answers change status to pending' do
      context 'when all answers are completed and update first as pending' do
        before do
          answer_01.status = :pending
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end

      context 'when all answers are completed and update second as pending' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :pending
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end

      context 'when all answers are completed and update third as pending' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :pending
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end

      context 'when all answers are completed and update fourth as pending' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :pending
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end
    end

    context 'when the answers change status to done' do
      context 'when the content has one concluded answer' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end

      context 'when the content has 2 answers concluded answer' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end

      context 'when the content has 3 answers concluded answer' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          content.update_progress
        end

        it 'returns the first status not done as pending' do
          expect(content.first_status_not_done[:status]).to eq('pending')
        end
      end

      context 'when all answers are completed' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the last status as an empty' do
          expect(content.first_status_not_done).to be_empty
        end
      end
    end

    context 'when the answers change status to waiting_authorization' do
      context 'when all answers are completed and update first as waiting_authorization' do
        before do
          answer_01.status = :waiting_authorization
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end
      end

      context 'when all answers are completed and update second as waiting_authorization' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :waiting_authorization
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end
      end

      context 'when all answers are completed and update third as waiting_authorization' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :waiting_authorization
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end
      end

      context 'when all answers are completed and update fourth as waiting_authorization' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :waiting_authorization
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end
      end
    end

    context 'when the answers change status to rejected' do
      context 'when all answers are completed and update first as rejected' do
        before do
          answer_01.status = :rejected
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end
      end

      context 'when all answers are completed and update second as rejected' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :rejected
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end
      end

      context 'when all answers are completed and update third as rejected' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :rejected
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end
      end

      context 'when all answers are completed and update fourth as rejected' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :rejected
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end
      end
    end

    context 'when the answers change status to changing' do
      context 'when all answers are completed and update first as changing' do
        before do
          answer_01.status = :changing
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end
      end

      context 'when all answers are completed and update second as changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :changing
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end
      end

      context 'when all answers are completed and update third as changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :changing
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end
      end

      context 'when all answers are completed and update fourth as changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :changing
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as changing' do
          expect(content.first_status_not_done[:status]).to eq('changing')
        end
      end
    end

    context 'when the answers change status to under_review' do
      context 'when all answers are completed and update first as under_review' do
        before do
          answer_01.status = :under_review
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end
      end

      context 'when all answers are completed and update second as under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :under_review
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end
      end

      context 'when all answers are completed and update third as under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :under_review
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end
      end

      context 'when all answers are completed and update fourth as under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :done
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = Time.zone.now
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :under_review
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end
      end
    end

    context 'when the answers mix status' do
      context 'when the answers 1 is done, the answer 2 is waiting_authorization, the answer 3 is rejected and the last is done' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :waiting_authorization
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :rejected
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :done
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = Time.zone.now
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end
      end

      context 'when the answers 1 is done, the answer 2 is waiting_authorization, the answer 3 is rejected and the last is under_review' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :waiting_authorization
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = nil
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :rejected
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :under_review
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as waiting_authorization' do
          expect(content.first_status_not_done[:status]).to eq('waiting_authorization')
        end
      end

      context 'when the first answers is rejected, the second is done, the answer 3 is under_review and the last is changing' do
        before do
          answer_01.status = :rejected
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = nil
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :under_review
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :changing
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as rejected' do
          expect(content.first_status_not_done[:status]).to eq('rejected')
        end
      end

      context 'when the answers 1 and 2 are completed, the answer 3 is under_review and the last is changing' do
        before do
          answer_01.status = :done
          answer_01.filled_at = Time.zone.now
          answer_01.first_fill_at = Time.zone.now
          answer_01.concluded_at = Time.zone.now
          answer_01.user = user
          answer_01.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_01.save!

          answer_02.status = :done
          answer_02.filled_at = Time.zone.now
          answer_02.first_fill_at = Time.zone.now
          answer_02.concluded_at = Time.zone.now
          answer_02.user = user
          answer_02.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_02.save!

          answer_03.status = :under_review
          answer_03.filled_at = Time.zone.now
          answer_03.first_fill_at = Time.zone.now
          answer_03.concluded_at = nil
          answer_03.user = user
          answer_03.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_03.save!

          answer_04.status = :changing
          answer_04.filled_at = Time.zone.now
          answer_04.first_fill_at = Time.zone.now
          answer_04.concluded_at = nil
          answer_04.user = user
          answer_04.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

          answer_04.save!

          content.update_progress
        end

        it 'returns the first status not done as under_review' do
          expect(content.first_status_not_done[:status]).to eq('under_review')
        end
      end
    end
  end

  describe '.show_on_form_values' do
    context 'when content has a field with show_on_form set to true' do
      let(:answer) { create(:answer, :completed, :with_dependencies) }
      let(:content) { answer.content }
      let(:target_field) { answer.step.templates.first.fields.first }
      let(:step) { answer.step }
      let(:step_template) { step.step_templates.first }
      let(:user) { create(:user) }
      let!(:step_permission) { create(:step_permission, step: answer.step, user: user)}

      subject { content.show_on_form_values(include_steps: true, user_id: user.id) }
      subject { content.show_on_form_values(include_steps: true, user_id: user.id) }

      before do
        target_field.update!(show_on_form: true)
      end

      it 'returns show_on_form_fields with values' do
        expected_result = [
          {
            step: step.name,
            fields: [
              {
                id: target_field.id,
                label: target_field.label,
                field_type: target_field.type,
                value: ''
              }
            ]
          }
        ]

        expect(subject).to eq(expected_result)
      end
    end

    context 'when content has a field with show_on_form set to true without include_steps' do
      let(:answer) { create(:answer, :completed, :with_dependencies) }
      let(:content) { answer.content }
      let(:target_field) { answer.step.templates.first.fields.first }
      let(:step) { answer.step }
      let(:step_template) { step.step_templates.first }
      let(:user) { create(:user) }
      let!(:step_permission) { create(:step_permission, step: answer.step, user: user)}

      subject { content.show_on_form_values(include_steps: false, user_id: user.id) }

      before do
        target_field.update!(show_on_form: true)
      end

      it 'returns show_on_form_fields with values' do
        expected_result = [
          {
            id: target_field.id,
            label: target_field.label,
            field_type: target_field.type,
            value: '',
            field_order: 0,
            step_order: 0,
            step_template_order: 0
          }
        ]

        expect(subject).to eq(expected_result)
      end
    end

    context 'when only 2 fields have show_on_form set to true' do
      let!(:answer) { create(:answer, :completed, :with_dependencies) }
      let!(:content) { answer.content }
      let!(:step) { answer.step }
      let!(:template_01) { step.templates.first }
      let!(:fields) { create_list(:field, 3, template: template_01) }
      let!(:field_01) { fields[0] }
      let!(:field_02) { fields[1] }
      let!(:field_03) { fields[2] }
      let(:user) { create(:user) }
      let!(:step_permission) { create(:step_permission, step: answer.step, user: user)}

      let(:step_template) { step.step_templates.first }

      before do
        field_01.update!(show_on_form: false)
        field_02.update!(show_on_form: true)
        field_03.update!(show_on_form: true)
        answer.update!(values: { field_02.id => 'value 02', field_03.id => 'value 03' })
      end

      it 'returns show_on_form_fields with correct values' do
        expected_fields = [
          step: step.name,
          fields: [
            {
              id: field_02.id,
              label: field_02.translated_attribute('label'),
              field_type: field_02.type,
              value: 'value 02'
            },
            {
              id: field_03.id,
              label: field_03.translated_attribute('label'),
              field_type: field_03.type,
              value: 'value 03'
            }
          ]
        ]

        expect(content.form_values_to_show(include_steps: true, user_id: user.id)).to eq(expected_fields)
      end

      it 'return show_on_form_fields with correct values not include_steps' do
        expected_fields = [
          {
            id: field_02.id,
            label: field_02.translated_attribute('label'),
            field_type: field_02.type,
            value: 'value 02',
            field_order: 2,
            step_order: 0,
            step_template_order: 0
          },
          {
            id: field_03.id,
            label: field_03.translated_attribute('label'),
            field_type: field_03.type,
            value: 'value 03',
            field_order: 3,
            step_order: 0,
            step_template_order: 0
          }
        ]
        expect(content.form_values_to_show(include_steps: false, user_id: user.id)).to eq(expected_fields)
      end
    end

    context 'when content has a field with show_on_form set to false' do
      let(:answer) { create(:answer, :completed, :with_dependencies) }
      let(:content) { answer.content }
      let(:target_field) { answer.step.templates.first.fields.first }
      let(:step) { answer.step }
      let(:step_template) { step.step_templates.first }
      let(:user) { create(:user) }

      subject { content.show_on_form_values(include_steps: true, user_id: user.id) }

      before do
        target_field.update!(show_on_form: false)
      end

      it 'returns an empty array' do
        expect(subject).to be_empty
      end
    end

    context 'when content has a field with show_on_form set to false with not include_steps' do
      let(:answer) { create(:answer, :completed, :with_dependencies) }
      let(:content) { answer.content }
      let(:target_field) { answer.step.templates.first.fields.first }
      let(:step) { answer.step }
      let(:step_template) { step.step_templates.first }
      let(:user) { create(:user) }

      subject { content.show_on_form_values(include_steps: false, user_id: user.id) }

      before do
        target_field.update!(show_on_form: false)
      end

      it 'returns an empty array' do
        expect(subject).to be_empty
      end
    end

    context 'for upload field' do
      let(:answer) { create(:answer, :completed, :with_dependencies) }
      let(:content) { answer.content }
      let(:step) { answer.step }
      let(:template) { step.templates.first }
      let(:target_field) { template.fields.first }
      let(:step_template) { step.step_templates.first }
      let(:user) { create(:user) }
      let!(:step_permission) { create(:step_permission, step: answer.step, user: user)}

      subject { content.show_on_form_values(include_steps: true, user_id: user.id) }

      before do
        target_field.update!(type: :upload, show_on_form: true)
        answer.update!(values: { target_field.id => ['http://image.teste.com/image.jpg'] })
      end

      it 'returns upload field with correct value' do
        expected_result = [
          {
            step: step.name,
            fields: [
              {
                id: target_field.id,
                field_type: target_field.type,
                label: target_field.label,
                value: ['http://image.teste.com/image.jpg']
              }
            ]
          }
        ]

        expect(subject).to eq(expected_result)
      end
    end

    context 'for upload field with not include steps' do
      let(:answer) { create(:answer, :completed, :with_dependencies) }
      let(:content) { answer.content }
      let(:step) { answer.step }
      let(:template) { step.templates.first }
      let(:target_field) { template.fields.first }
      let(:step_template) { step.step_templates.first }
      let(:user) { create(:user) }
      let!(:step_permission) { create(:step_permission, step: answer.step, user: user)}

      subject { content.show_on_form_values(include_steps: false, user_id: user.id) }

      before do
        target_field.update!(type: :upload, show_on_form: true)
        answer.update!(values: { target_field.id => ['http://image.teste.com/image.jpg'] })
      end

      it 'returns upload field with correct value' do
        expected_result = [
          {
            id: target_field.id,
            field_type: target_field.type,
            label: target_field.label,
            value: ['http://image.teste.com/image.jpg'],
            field_order: 0,
            step_order: 0,
            step_template_order: 0
          }
        ]

        expect(subject).to eq(expected_result)
      end
    end
  end

  describe '.show_on_list_values' do
    subject { content.show_on_list_values }

    let(:answer) { create(:answer, :with_dependencies, :completed) }
    let(:content) { answer.content }
    let(:target_field) { answer.step.templates.first.fields.first }

    before do
      create(:show_on_list_field, business: content.business, field: target_field)
    end

    it { is_expected.to eq([{ id: target_field.id, field_type: target_field.type, label: target_field.label, value: '', order: 0, field_order: 0 }]) }

    context 'for reference field' do
      let(:answer_referenced) { create(:answer, :with_dependencies, :completed) }
      let(:referenced_value_field) { answer_referenced.step.templates.first.fields.first }
      let(:referenced_field) { create(:field, template: referenced_value_field.template) }
      let(:expected_values) do
        [
          { id: target_field.id, label: target_field.label, field_type: target_field.type, value: 'text value', order: 0, field_order: 0 },
          { id: @reference_field.id, label: @reference_field.label, field_type: @reference_field.type, value: 'referenced label', order: 0, field_order: 1 }
        ]
      end

      before do
        answer_referenced.update(values: { referenced_value_field.id => 'value referenced', referenced_field.id => 'referenced label' })

        @reference_field = create(:field, type: :reference, reference_business: answer_referenced.step.business, template: target_field.template, reference_field: referenced_field, reference_value_field: referenced_value_field)
        create(:show_on_list_field, business: content.business, field: @reference_field)
        answer.update(values: { @reference_field.id => 'value referenced', target_field.id => 'text value' })
      end

      it { is_expected.to eq(expected_values) }
    end

    context 'for multiple reference field' do
      let(:answer_referenced) { create(:answer, :with_dependencies, :completed) }
      let(:referenced_value_field) { answer_referenced.step.templates.first.fields.first }
      let(:referenced_field) { create(:field, template: referenced_value_field.template) }
      let(:expected_values) do
        [
          { id: target_field.id, label: target_field.label, field_type: target_field.type, value: 'text value', order: 0, field_order: 0 },
          { id: @reference_field.id, label: @reference_field.label, field_type: @reference_field.type, value: ['referenced label'], order: 0, field_order: 1 }
        ]
      end

      before do
        answer_referenced.update!(values: { referenced_value_field.id => 'value referenced', referenced_field.id => 'referenced label' })

        @reference_field = create(:field, label: 'reference_field', type: :multiple_reference, reference_business: answer_referenced.step.business, template: target_field.template, reference_field: referenced_field, reference_value_field: referenced_value_field)
        create(:show_on_list_field, business: content.business, field: @reference_field)

        answer.update!(values: { @reference_field.id => ['value referenced'], target_field.id => 'text value' })
      end

      it { is_expected.to eq(expected_values) }
    end

    context 'for dropdown field' do
      let(:expected_values) do
        [
          { id: target_field.id, label: target_field.label, field_type: target_field.type, value: 'text value', order: 0, field_order: 0 },
          { id: @dropdown_field.id, label: @dropdown_field.label, field_type: @dropdown_field.type, value: 'Option1', order: 0, field_order: 1 }
        ]
      end

      before do
        option = { 'label' => 'Option1', 'value' => 'op1', 'order' => 1 }
        @dropdown_field = create(:field, type: :dropdown, options: [option], template: target_field.template)
        create(:show_on_list_field, business: content.business, field: @dropdown_field)

        answer.update(values: { @dropdown_field.id => 'op1', target_field.id => 'text value' })
      end

      it { is_expected.to eq(expected_values) }
    end
  end

  describe '#current_answers' do
    before do
      @user = create(:user)

      @step1 = create(:step, business:, name: 'step1')
      @step2 = create(:step, business:, name: 'step2')
      @step3 = create(:step, business:, name: 'step3')
      @step4 = create(:step, business:, name: 'step4')

      @template1 = create(:template)
      @template2 = create(:template)
      @template3 = create(:template)
      @template4 = create(:template)

      @step_template1 = create(:step_template, step: @step1, template: @template1)
      @step_template2 = create(:step_template, step: @step2, template: @template2)
      @step_template3 = create(:step_template, step: @step3, template: @template3)
      @step_template4 = create(:step_template, step: @step4, template: @template4)

      service = ContentService.new(business:, user: @user)
      service.create

      @content = service.record

      @current_answer = @content.answers.find_by(step_id: @step2.id)
      @content.update(current_answer: @current_answer)
    end

    context 'with no changes' do
      it 'returns the answers' do
        expect(@content.current_answers).to eq(@content.answers)
      end
    end

    context 'removing a step' do
      before do
        service = StepService.new
        service.destroy(@step2.id)
      end

      it 'keeps the original steps in answers' do
        expect(@content.answers.length).to eq(4)
        expect(@content.answers.map(&:step_id)).to eq([@step1.id, @step2.id, @step3.id, @step4.id])
      end

      it 'returns the current answers without the removed step' do
        expect(@content.current_answers.length).to eq(3)
        expect(@content.current_answers.map(&:step_id)).to eq([@step1.id, @step3.id, @step4.id])
        expect(@content.current_answer).to_not eq(@current_answer)
      end

      it 'update answers after calling current_answers' do
        @content.current_answers

        expect(@content.answers.length).to eq(3)
        expect(@content.answers.map(&:step_id)).to eq([@step1.id, @step3.id, @step4.id])
      end
    end

    context 'adding a step' do
      before do
        @step5 = create(:step, business:)
      end

      it 'keeps the original steps in answers' do
        expect(@content.answers.length).to eq(4)
        expect(@content.answers.map(&:step_id)).to eq([@step1.id, @step2.id, @step3.id, @step4.id])
      end

      it 'returns the current answers with the added step' do
        expect(@content.current_answers.length).to eq(5)
        expect(@content.current_answers.map(&:step_id)).to eq([@step1.id, @step2.id, @step3.id, @step4.id, @step5.id])
      end

      it 'update answers after calling current_answers' do
        @content.current_answers

        @content.reload

        expect(@content.answers.length).to eq(5)
        expect(@content.answers.map(&:step_id)).to eq([@step1.id, @step2.id, @step3.id, @step4.id, @step5.id])
      end

      it 'copies the template data to the answers' do
        answer = @content.current_answers[0]
        expect(answer.step).to eq(@step1)

        expect(answer.position).to eq(0)
        expect(answer.pending?).to be_truthy
        expect(answer.data.size).to eq(0)

        answer = @content.current_answers[1]
        expect(answer.step).to eq(@step2)

        expect(answer.position).to eq(1)
        expect(answer.pending?).to be_truthy
        expect(answer.data.size).to eq(0)
      end
    end

    context 'adding a step in the middle' do
      before do
        @step5 = create(:step, name: 'step 2.5', business:)
        @step5.update!(order: 2)
      end

      it 'keeps the original steps in answers' do
        expect(@content.answers.length).to eq(4)
        expect(@content.answers.map(&:step_id)).to eq([@step1, @step2, @step3, @step4].map(&:id))
      end

      it 'returns the current answers with the added step' do
        expect(@content.current_answers.length).to eq(5)
        expect(@content.current_answers.map(&:step_id)).to eq([@step1, @step2, @step5, @step3, @step4].map(&:id))
      end

      it 'update answers after calling current_answers' do
        @content.current_answers

        expect(@content.answers.length).to eq(5)
        expect(@content.answers.map(&:step_id)).to eq([@step1, @step2, @step5, @step3, @step4].map(&:id))
      end
    end

    context 'reordering a step' do
      before do
        @step2.update!(order: 2)
      end
      it 'returns the current answers reordered' do
        expect(@content.current_answers.length).to eq(4)
        expect(@content.current_answers.map(&:step_id)).to eq([@step1, @step3, @step2, @step4].map(&:id))
      end
    end
  end

  describe 'after undiscard' do
    let(:user) { create(:user) }

    before do
      @content = create(:content, business:, deleted_at: Time.zone.now, deletion_reason: 'foobar', current_user: user).tap do |content|
        content.answers << create(:answer, step: create(:step, business:), position: 0, content:)

        answer = content.answers.first

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 1, content:)

        answer = content.answers.second

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'batz' => '123' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 2, content:)
      end

      @answer1 = @content.answers.first
      @answer2 = @content.answers.second
      @answer3 = @content.answers.third

      @content.answers.each do |answer|
        answer.current_user = user
        answer.discard!
      end
    end

    context 'when the company does not allow elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: false) }

      it 'does not enqueue the elasticsearch indexing' do
        expect(Elasticsearch::PutDocumentWorker).to_not receive(:perform_async).with(Apartment::Tenant.current, @content.id)

        @content.undiscard
      end
    end

    context 'when the company allows elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: true) }

      it 'enqueues the elasticsearch bulk indexing for all answers' do
        expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, @content.id)

        @content.undiscard
      end
    end

    it 'removes the deletion reason from the record' do
      expect { @content.undiscard }.to change { @content.deletion_reason }.from('foobar').to(nil)
    end

    it 'removes the deleted_at from all answers' do
      expect { @content.undiscard }.to change { @content.answers.pluck(:deleted_at).compact_blank }.from([anything, anything, anything]).to([])
    end

    it 'creates a version for every answer' do
      expect { @content.undiscard }.to change(AnswerVersion, :count).by(2)
    end
  end

  describe 'after destroy' do
    let(:user) { create(:user) }

    before do
      @content = create(:content, business:).tap do |content|
        content.answers << create(:answer, step: create(:step, business:), position: 0, content:)

        answer = content.answers.first

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 1, content:)

        answer = content.answers.second

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'batz' => '123' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 2, content:)
      end

      @answer1 = @content.answers.first
      @answer2 = @content.answers.second
      @answer3 = @content.answers.third

      @content.update!(current_answer_id: nil)
    end

    context 'when the company does not allow elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: false) }

      it 'does not enqueue the elasticsearch deletion' do
        expect(Elasticsearch::PutDocumentWorker).to_not receive(:perform_async)
        @content.destroy!
      end
    end

    context 'when the company allows elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: true) }

      context 'but the content is a draft' do
        before { @content.update_attribute(:draft, true) }

        it 'does not enqueue the elasticsearch index' do
          expect(Elasticsearch::PutDocumentWorker).to_not receive(:perform_async)
          @content.destroy!
        end
      end

      context 'and the content is not a draft' do
        it 'enqueues the elasticsearch index for content_id' do
          expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, @content.id)
          @content.destroy!
        end
      end
    end
  end

  describe 'after discard' do
    let(:user) { create(:user) }

    before do
      @content = create(:content, business:, current_user: user).tap do |content|
        content.answers << create(:answer, step: create(:step, business:), position: 0, content:)

        answer = content.answers.first

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'foo' => 'bar', 'bar' => 'batz' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 1, content:)

        answer = content.answers.second

        answer.status = :done
        answer.filled_at = Time.zone.now
        answer.first_fill_at = Time.zone.now
        answer.concluded_at = Time.zone.now
        answer.user = user
        answer.data[:values] = { 'batz' => '123' }

        answer.save!

        content.answers << create(:answer, step: create(:step, business:), position: 2, content:)
      end

      @answer1 = @content.answers.first
      @answer2 = @content.answers.second
      @answer3 = @content.answers.third
    end

    context 'when the company does not allow elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: false) }

      it 'does not enqueue the elasticsearch deletion' do
        expect(Elasticsearch::PutDocumentWorker).to_not receive(:perform_async)
        @content.discard
      end
    end

    context 'when the company allows elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: true) }

      context 'but the content is a draft' do
        before { @content.update_attribute(:draft, true) }

        it 'does not enqueue the elasticsearch deletion' do
          expect(Elasticsearch::PutDocumentWorker).to_not receive(:perform_async)
          @content.discard
        end
      end

      context 'and the content is not a draft' do
        it 'enqueues the elasticsearch deletion for all answers' do
          expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, @content.id)

          @content.discard
        end
      end
    end
  end

  describe 'before discard' do
    let(:business_group) { create(:business_group) }
    let(:content) { create(:content, business:) }
    let(:business) { create(:business, business_group:) }
    let(:sub_business) { create(:business, :with_dependencies, :with_step, name: 'subbusiness', sub_business: true) }
    let!(:field) { create(:field, label: 'subbusiness field', template: create(:template), reference_sub_business_id: sub_business.id, required: true, type: :sub_business) }
    let(:step) { create(:step, business:, order: 0) }

    subject { create(:content, business: sub_business, parent: content, created_by: create(:user), status: :done, draft: false, concluded_at: Time.zone.now) }

    before { create(:step_template, step:, template: field.template) }

    it 'does not allow to discard the subcontent' do
      expect { subject.discard }.not_to(change { subject.reload.deleted_at })
    end

    it 'returns the error message' do
      subject.discard

      expect(subject.errors.full_messages).to eq(['O campo desse content é requerido, o último registro não pode ser excluído'])
    end

    context 'when the field is not required' do
      before { field.update! required: false }

      it 'allows to discard the subcontent' do
        expect { subject.discard }.to change { subject.reload.deleted_at }.from(nil).to(a_value_within(2.seconds).of(Time.zone.now))
      end

      it 'returns no errors' do
        subject.discard

        expect(subject.errors.full_messages).to be_empty
      end
    end

    context 'when there is more than one subcontent' do
      let!(:sub_content) { create(:content, business: sub_business, parent: content, created_by: create(:user), status: :done, draft: false, concluded_at: Time.zone.now) }

      it 'allows to discard the subcontent' do
        expect { subject.discard }.to change { subject.reload.deleted_at }.from(nil).to(a_value_within(2.seconds).of(Time.zone.now))
      end

      it 'returns no errors' do
        subject.discard

        expect(subject.errors.full_messages).to be_empty
      end
    end
  end
end
