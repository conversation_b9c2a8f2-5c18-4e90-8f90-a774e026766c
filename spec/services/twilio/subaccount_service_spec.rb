require 'rails_helper'

RSpec.describe Twilio::SubaccountService, type: :service do
  describe '#create' do
    subject { described_class.new }

    let(:name) { 'foobar' }
    let(:twilio_client) { double(:twilio_client, api: twilio_api) }
    let(:twilio_api) { double(:twilio_api, v2010: twilio_v2010) }
    let(:twilio_v2010) { double(:twilio_v2010, accounts: twilio_accounts) }
    let(:twilio_accounts) { double(:twilio_accounts, list: list_response) }
    let(:list_response) { [double(Twilio::REST::Api::V2010::AccountInstance, auth_token: 'abc123', sid: '123abc')] }

    before do
      allow(Twilio::REST::Client).to receive(:new).and_return(twilio_client)
    end

    it 'initializes the twilio client with the main account credentials' do
      expect(Twilio::REST::Client).to receive(:new).with(Rails.application.credentials.twilio_account_sid, Rails.application.credentials.twilio_auth_token).and_return(twilio_client)

      subject.create(name)
    end

    it 'checks if the company already has a subaccount' do
      expect(twilio_accounts).to receive(:list).with(friendly_name: name, limit: 1).and_return(list_response)

      subject.create(name)
    end

    context 'with listing error' do
      before { allow(twilio_accounts).to receive(:list).and_raise(Twilio::REST::RestError, 'foobar') }

      it 'returns false' do
        subject.create(name)

        expect(subject).not_to be_success
      end

      it 'returns nothing' do
        subject.create(name)

        expect(subject.record).to be_nil
      end
    end

    context 'with listing success' do
      context 'when the company already has a subaccount' do
        it 'does not try to create a new one' do
          expect(twilio_accounts).not_to receive(:create)

          subject.create(name)
        end

        it 'returns success' do
          subject.create(name)

          expect(subject).to be_success
        end

        it 'returns the subaccount data' do
          subject.create(name)

          expect(subject.record).to eq(list_response[0])
        end
      end

      context 'when the company does not have a subaccount' do
        let(:twilio_accounts) { double(:twilio_accounts, list: [], create: create_response) }
        let(:create_response) { double(Twilio::REST::Api::V2010::AccountInstance, auth_token: 'abc123', sid: '123abc') }

        it 'tries to create a new one' do
          expect(twilio_accounts).to receive(:create).with(friendly_name: name).and_return(create_response)

          subject.create(name)
        end

        context 'with creation error' do
          before { allow(twilio_accounts).to receive(:create).and_raise(Twilio::REST::RestError, 'foobar') }

          it 'returns false' do
            subject.create(name)

            expect(subject).not_to be_success
          end

          it 'returns nothing' do
            subject.create(name)

            expect(subject.record).to be_nil
          end
        end

        context 'with creation success' do
          it 'returns success' do
            subject.create(name)

            expect(subject).to be_success
          end

          it 'returns the subaccount data' do
            subject.create(name)

            expect(subject.record).to eq(create_response)
          end
        end
      end
    end
  end
end
