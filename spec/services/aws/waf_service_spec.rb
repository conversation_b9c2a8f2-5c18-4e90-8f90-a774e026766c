require 'rails_helper'

RSpec.describe Aws::WafService, type: :service do
  describe '#update_allowed_ips' do
    subject { described_class.new(company.reload) }

    let(:aws_client) { class_spy(Aws::WAFV2::Client, new: client) }
    let(:company) { Company.current }

    before { stub_const('Aws::WAFV2::Client', aws_client) }

    context 'when the company does not have the ip access restriction enabled' do
      let(:get_web_acl_response) { JSON.parse(file_fixture('aws/get_web_acl_with_rule_group.json').read).to_h.with_indifferent_access }
      let(:get_rule_group_response) { JSON.parse(file_fixture('aws/get_rule_group.json').read).to_h.with_indifferent_access }
      let(:get_ip_set_response) { JSON.parse(file_fixture('aws/get_ip_set.json').read).to_h.with_indifferent_access }
      let(:client) { Aws::WAFV2::Client.new(stub_responses: true) }
      let(:rule_group_id) { '96e751f9-bb01-425f-b8c8-8aee19da096f' }
      let(:ip_set_id) { 'c3b81b91-1bf1-4ee7-ba70-4c11ae91ada7' }

      before do
        company.update_columns(restrict_access_by_ip: false, allowed_ips: [Faker::Internet.ip_v4_address], waf_rule_group_id: rule_group_id, waf_ip_set_id: ip_set_id)

        client.stub_responses(:get_rule_group, get_rule_group_response)
        client.stub_responses(:delete_rule_group)
        client.stub_responses(:get_ip_set, get_ip_set_response)
        client.stub_responses(:delete_ip_set)
        client.stub_responses(:get_web_acl, get_web_acl_response)
        client.stub_responses(:update_web_acl)
      end

      context 'and it does not have a rule group id' do
        before { company.update_columns(waf_rule_group_id: nil) }

        it 'does not update the web acl' do
          expect(client).not_to receive(:update_web_acl)

          subject.update_allowed_ips
        end

        it 'does not delete any rule group' do
          expect(client).not_to receive(:delete_rule_group)

          subject.update_allowed_ips
        end

        it 'logs a warning' do
          expect(Rails.logger).to receive(:warn).with("[WAFSERVICE] Company #{company.subdomain} does not have a rule group id...skipping deletion")

          subject.update_allowed_ips
        end
      end

      context 'when it has a rule group id' do
        it 'fetches the web acl token' do
          subject.update_allowed_ips

          api_request = client.api_requests.find { |request| request[:operation_name] == :get_web_acl }

          expect(api_request).not_to be_nil
          expect(api_request[:params]).to eq(name: Rails.application.credentials.waf_name, scope: "CLOUDFRONT", id: Rails.application.credentials.waf_id)
        end

        context 'with success on fetching the web acl lock token' do
          it 'updates the web acl removing the company rule group' do
            subject.update_allowed_ips

            api_request = client.api_requests.find { |request| request[:operation_name] == :update_web_acl }

            expect(api_request).not_to be_nil
            expect(api_request[:params]).to include(
              default_action: { allow: {} },
              name: Rails.application.credentials.waf_name, scope: Aws::WafService::CLOUDFRONT, id: Rails.application.credentials.waf_id,
              rules: [
                hash_including(name: 'CrossSiteScripting_BODY_WithHeader', priority: 0),
                hash_including(name: 'AWS-AWSManagedRulesAmazonIpReputationList', priority: 1),
                hash_including(name: 'AWS-AWSManagedRulesCommonRuleSet', priority: 2),
                hash_including(name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet', priority: 3),
                hash_including(name: 'AWS-AWSManagedRulesLinuxRuleSet', priority: 4),
                hash_including(name: 'AWS-AWSManagedRulesSQLiRuleSet', priority: 5)
              ],
              visibility_config: { cloud_watch_metrics_enabled: true, metric_name: '4mdg-waf-test', sampled_requests_enabled: true },
              lock_token: get_web_acl_response[:lock_token]
            )
          end

          context 'with success on updating the web acl rules' do
            it 'fetches the rule group lock token' do
              subject.update_allowed_ips

              api_request = client.api_requests.find { |request| request[:operation_name] == :get_rule_group }

              expect(api_request).not_to be_nil
            end

            context 'with success on fetching the lock token' do
              it 'tries to delete the rule group with the lock token' do
                subject.update_allowed_ips

                api_request = client.api_requests.find { |request| request[:operation_name] == :delete_rule_group }

                expect(api_request).not_to be_nil
                expect(api_request[:params]).to eq(name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT, id: rule_group_id, lock_token: get_rule_group_response[:lock_token])
              end

              context 'with success on deletion' do
                it 'clears the rule group id' do
                  expect { subject.update_allowed_ips }.to change { company.reload.waf_rule_group_id }.to(nil)
                end
              end

              context 'with error on deleting the rule group' do
                before { client.stub_responses(:delete_rule_group, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

                it 'logs an error' do
                  expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

                  subject.update_allowed_ips
                end
              end
            end

            context 'with error on fetching the lock token' do
              before { client.stub_responses(:get_rule_group, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

              it 'does not delete the rule group' do
                expect(client).not_to receive(:delete_rule_group)

                subject.update_allowed_ips
              end

              it 'logs an error' do
                expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

                subject.update_allowed_ips
              end
            end
          end

          context 'with error on updating the web acl' do
            before { client.stub_responses(:update_web_acl, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

            it 'logs an error' do
              expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

              subject.update_allowed_ips
            end
          end
        end
      end

      context 'and it does not have an ip set id' do
        before { company.update_columns(waf_ip_set_id: nil) }

        it 'does not delete any ip set' do
          expect(client).not_to receive(:delete_ip_set)

          subject.update_allowed_ips
        end

        it 'logs a warning' do
          expect(Rails.logger).to receive(:warn).with("[WAFSERVICE] Company #{company.subdomain} does not have an ip set id...skipping deletion")

          subject.update_allowed_ips
        end
      end

      context 'when it has a ip set id' do
        it 'fetches the ip set lock token' do
          subject.update_allowed_ips

          api_request = client.api_requests.find { |request| request[:operation_name] == :get_ip_set }

          expect(api_request).not_to be_nil
          expect(api_request[:params]).to eq(name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT, id: ip_set_id)
        end

        context 'with success on fetching the lock token' do
          it 'tries to delete the ip set with the lock token' do
            subject.update_allowed_ips

            api_request = client.api_requests.find { |request| request[:operation_name] == :delete_ip_set }

            expect(api_request).not_to be_nil
            expect(api_request[:params]).to eq(name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT, id: ip_set_id, lock_token: get_ip_set_response[:lock_token])
          end

          context 'with success on deletion' do
            it 'clears the ip set id' do
              expect { subject.update_allowed_ips }.to change { company.reload.waf_ip_set_id }.to(nil)
            end
          end

          context 'with error on deleting the ip set' do
            before { client.stub_responses(:delete_ip_set, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

            it 'logs an error' do
              expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

              subject.update_allowed_ips
            end
          end
        end

        context 'with error on fetching the lock token' do
          before { client.stub_responses(:get_ip_set, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

          it 'does not delete the ip set' do
            expect(client).not_to receive(:delete_ip_set)

            subject.update_allowed_ips
          end

          it 'logs an error' do
            expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

            subject.update_allowed_ips
          end
        end
      end
    end

    context 'when the company has the ip access restriction enabled' do
      let(:create_ip_set_response) { JSON.parse(file_fixture('aws/create_ip_set.json').read).to_h.with_indifferent_access }
      let(:create_rule_group_response) { JSON.parse(file_fixture('aws/create_rule_group.json').read).to_h.with_indifferent_access }
      let(:get_web_acl_response) { JSON.parse(file_fixture('aws/get_web_acl_without_rule_group.json').read).to_h.with_indifferent_access }
      let(:client) { Aws::WAFV2::Client.new(stub_responses: true) }
      let(:rule_group_id) { '96e751f9-bb01-425f-b8c8-8aee19da096f' }
      let(:ip_set_id) { 'c3b81b91-1bf1-4ee7-ba70-4c11ae91ada7' }

      before do
        company.update_columns(restrict_access_by_ip: false, allowed_ips: [Faker::Internet.ip_v4_address], waf_rule_group_id: rule_group_id, waf_ip_set_id: ip_set_id)

        client.stub_responses(:create_ip_set, create_ip_set_response)
        client.stub_responses(:create_rule_group, create_rule_group_response)
        client.stub_responses(:get_web_acl, get_web_acl_response)
        client.stub_responses(:update_web_acl)
      end

      context 'and it does not have an ip set id' do
        let(:ip1) { Faker::Internet.ip_v4_address }
        let(:ip2) { Faker::Internet.ip_v4_address }

        before { company.update_columns(restrict_access_by_ip: true, waf_ip_set_id: nil, allowed_ips: [ip1, ip2, nil]) }

        it 'tries to create an ip set with the allowed ips' do
          subject.update_allowed_ips

          api_request = client.api_requests[0]

          expect(api_request[:operation_name]).to eq(:create_ip_set)
          expect(api_request[:params]).to eq(
            name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT,
            description: "IP set for #{company.subdomain}, #{Rails.env} environment",
            ip_address_version: "IPV4", addresses: company.allowed_ips_cidr,
            tags: [{ key: 'Stack', value: Rails.env }, { key: 'Name', value: "4mdg-#{Rails.env}" }]
          )
        end

        context 'with success on creation' do
          it 'updates the ip set id' do
            expect { subject.update_allowed_ips }.to change { company.reload.waf_ip_set_id }.from(nil).to(create_ip_set_response[:summary][:id])
          end
        end

        context 'with error on creation' do
          before { client.stub_responses(:create_ip_set, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

          it 'does not update the ip set id' do
            expect { subject.update_allowed_ips }.not_to change { company.reload.waf_ip_set_id }
          end

          it 'logs an error' do
            expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

            subject.update_allowed_ips
          end
        end
      end

      context 'and it has an ip set id' do
        let(:get_ip_set_response) { JSON.parse(file_fixture('aws/get_ip_set.json').read).to_h.with_indifferent_access }
        let(:ip1) { Faker::Internet.ip_v4_address }

        before do
          company.update_columns(restrict_access_by_ip: true, waf_ip_set_id: 'foobar-123', allowed_ips: [ip1, nil])

          client.stub_responses(:get_ip_set, get_ip_set_response)
        end

        it 'fetches the ip set lock token' do
          subject.update_allowed_ips

          api_request = client.api_requests[0]

          expect(api_request[:operation_name]).to eq(:get_ip_set)
          expect(api_request[:params]).to eq(name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT, id: company.waf_ip_set_id)
        end

        context 'with success on fetching the lock token' do
          it 'tries to update the ip set with the allowed ips and the lock token' do
            subject.update_allowed_ips

            api_request = client.api_requests[1]

            expect(api_request[:operation_name]).to eq(:update_ip_set)
            expect(api_request[:params]).to eq(
              name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT,
              id: company.waf_ip_set_id, addresses: company.allowed_ips_cidr, lock_token: get_ip_set_response[:lock_token]
            )
          end

          context 'with error on updating the token' do
            before { client.stub_responses(:update_ip_set, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

            it 'logs an error' do
              expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

              subject.update_allowed_ips
            end
          end
        end

        context 'with error on fetching the lock token' do
          before { client.stub_responses(:get_ip_set, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

          it 'does not update the ip set' do
            expect(client).not_to receive(:update_ip_set)

            subject.update_allowed_ips
          end

          it 'logs an error' do
            expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

            subject.update_allowed_ips
          end
        end
      end

      context 'and it does not have an rule group id' do
        before { company.update_columns(restrict_access_by_ip: true, waf_ip_set_id: nil, allowed_ips: [Faker::Internet.ip_v4_address, nil], waf_rule_group_id: nil) }

        it 'tries to create a rule group' do
          subject.update_allowed_ips

          api_request = client.api_requests.find { |request| request[:operation_name] == :create_rule_group }

          expect(api_request).not_to be_nil
          expect(api_request[:params]).to eq(
            name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT,
            capacity: 3, description: "Rule group for #{company.subdomain}, #{Rails.env} environment",
            rules: [
              {
                name: "#{Aws::WafService::RULE_NAME}-#{company.subdomain}-#{Rails.env}", priority: 0,
                statement: {
                  and_statement: {
                    statements: [
                      {
                        byte_match_statement: {
                          search_string: "#{company.subdomain}.#{Rails.env}.4mdg.com.br",
                          field_to_match: { single_header: { name: 'host' } },
                          text_transformations: [{ priority: 0, type: Aws::WafService::NONE }],
                          positional_constraint: Aws::WafService::EXACTLY
                        }
                      },
                      { not_statement: { statement: { ip_set_reference_statement: { arn: company.waf_ip_set_arn } } } }
                    ]
                  }
                },
                action: { block: { custom_response: { response_code: 403, custom_response_body_key: "#{company.subdomain}-#{Rails.env}-response-key" } } },
                visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "#{Aws::WafService::RULE_NAME}-#{company.subdomain}-#{Rails.env}" }
              }
            ],
            visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "rule-group-#{company.subdomain}-#{Rails.env}" },
            tags: [{ key: 'Stack', value: Rails.env }, { key: 'Name', value: "4mdg-#{Rails.env}" }],
            custom_response_bodies: {
              "#{company.subdomain}-#{Rails.env}-response-key" => { content_type: "TEXT_HTML", content: Aws::WafService::FORBIDDEN_PAGE_PATH.read }
            }
          )
        end

        context 'with success on creation' do
          it 'updates the rule group id' do
            expect { subject.update_allowed_ips }.to change { company.reload.waf_rule_group_id }.from(nil).to(create_rule_group_response[:summary][:id])
          end
        end

        context 'with error on creation' do
          before { client.stub_responses(:create_rule_group, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

          it 'does not update the rule group id' do
            expect { subject.update_allowed_ips }.not_to change { company.reload.waf_rule_group_id }
          end

          it 'logs an error' do
            expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

            subject.update_allowed_ips
          end
        end
      end

      context 'and it has a rule group id' do
        let(:get_ip_set_response) { JSON.parse(file_fixture('aws/get_ip_set.json').read).to_h.with_indifferent_access }
        let(:get_rule_group_response) { JSON.parse(file_fixture('aws/get_rule_group.json').read).to_h.with_indifferent_access }
        let(:get_web_acl_response) { JSON.parse(file_fixture('aws/get_web_acl_without_rule_group.json').read).to_h.with_indifferent_access }
        let(:ip1) { Faker::Internet.ip_v4_address }

        before do
          company.update_columns(restrict_access_by_ip: true, waf_ip_set_id: 'c3b81b91-1bf1-4ee7-ba70-4c11ae91ada7', allowed_ips: [ip1, nil], waf_rule_group_id: '96e751f9-bb01-425f-b8c8-8aee19da096f')

          client.stub_responses(:get_ip_set, get_ip_set_response)
          client.stub_responses(:get_rule_group, get_rule_group_response)
          client.stub_responses(:get_web_acl, get_web_acl_response)
        end

        it 'fetches the rule group lock token' do
          subject.update_allowed_ips

          api_request = client.api_requests.find { |request| request[:operation_name] == :get_rule_group }

          expect(api_request).not_to be_nil
          expect(api_request[:params]).to eq(name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT, id: company.waf_rule_group_id)
        end

        context 'with success on fetching the lock token' do
          it 'tries to update the rule group with the lock token' do
            subject.update_allowed_ips

            api_request = client.api_requests.find { |request| request[:operation_name] == :update_rule_group }

            expect(api_request).not_to be_nil
            expect(api_request[:params]).to eq(
              name: "#{company.subdomain}-#{Rails.env}", scope: Aws::WafService::CLOUDFRONT, id: company.waf_rule_group_id,
              rules: [
                {
                  name: "#{Aws::WafService::RULE_NAME}-#{company.subdomain}-#{Rails.env}", priority: 0,
                  statement: {
                    and_statement: {
                      statements: [
                        {
                          byte_match_statement: {
                            search_string: "#{company.subdomain}.#{Rails.env}.4mdg.com.br",
                            field_to_match: { single_header: { name: 'host' } },
                            text_transformations: [{ priority: 0, type: Aws::WafService::NONE }],
                            positional_constraint: Aws::WafService::EXACTLY
                          }
                        },
                        { not_statement: { statement: { ip_set_reference_statement: { arn: company.waf_ip_set_arn } } } }
                      ]
                    }
                  },
                  action: { block: { custom_response: { response_code: 403, custom_response_body_key: "#{company.subdomain}-#{Rails.env}-response-key" } } },
                  visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "#{Aws::WafService::RULE_NAME}-#{company.subdomain}-#{Rails.env}" }
                }
              ],
              visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "rule-group-#{company.subdomain}-#{Rails.env}" },
              custom_response_bodies: {
                "#{company.subdomain}-#{Rails.env}-response-key" => { content_type: "TEXT_HTML", content: Aws::WafService::FORBIDDEN_PAGE_PATH.read }
              },
              lock_token: get_rule_group_response[:lock_token]
            )
          end

          context 'with error on updating the rule group' do
            before { client.stub_responses(:update_rule_group, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

            it 'logs an error' do
              expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

              subject.update_allowed_ips
            end
          end
        end

        context 'with error on fetching the lock token' do
          before { client.stub_responses(:get_rule_group, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

          it 'does not update the rule group' do
            expect(client).not_to receive(:update_rule_group)

            subject.update_allowed_ips
          end

          it 'logs an error' do
            expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

            subject.update_allowed_ips
          end
        end
      end

      describe 'web acl rules configuration' do
        before { company.update_columns(restrict_access_by_ip: true, waf_ip_set_id: nil, allowed_ips: [Faker::Internet.ip_v4_address]) }

        it 'fetches the web acl token' do
          subject.update_allowed_ips

          api_request = client.api_requests.find { |request| request[:operation_name] == :get_web_acl }

          expect(api_request).not_to be_nil
          expect(api_request[:params]).to eq(name: Rails.application.credentials.waf_name, scope: "CLOUDFRONT", id: Rails.application.credentials.waf_id)
        end

        context 'with success on fetching the web acl lock token' do
          context 'when the rule group is not present on the waf rules' do
            let(:client) { Aws::WAFV2::Client.new(stub_responses: true) }

            before do
              client.stub_responses(:get_web_acl, JSON.parse(file_fixture('aws/get_web_acl_without_rule_group.json').read).to_h.with_indifferent_access)
            end

            it 'updates the web acl appending the company rule group' do
              subject.update_allowed_ips

              api_request = client.api_requests.find { |request| request[:operation_name] == :update_web_acl }

              expect(api_request).not_to be_nil
              expect(api_request[:params]).to include(
                default_action: { allow: {} },
                name: Rails.application.credentials.waf_name, scope: Aws::WafService::CLOUDFRONT, id: Rails.application.credentials.waf_id,
                rules: [
                  hash_including(
                    name: "#{company.subdomain}-#{Rails.env}", priority: 0,
                    statement: { rule_group_reference_statement: { arn: "arn:aws:wafv2:us-foo-1:123456789123:global/rulegroup/test-test/96e751f9-bb01-425f-b8c8-8aee19da096f" } },
                    override_action: { none: {} },
                    visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "#{company.subdomain}-#{Rails.env}" }
                  ),
                  hash_including(name: 'CrossSiteScripting_BODY_WithHeader', priority: 1, visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: 'CrossSiteScripting_BODY_WithHeader' }),
                  hash_including(name: 'AWS-AWSManagedRulesAmazonIpReputationList', priority: 2, visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: 'AWS-AWSManagedRulesAmazonIpReputationList' }),
                  hash_including(name: 'AWS-AWSManagedRulesCommonRuleSet', priority: 3, visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: 'AWS-AWSManagedRulesCommonRuleSet' }),
                  hash_including(name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet', priority: 4, visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet' }),
                  hash_including(name: 'AWS-AWSManagedRulesLinuxRuleSet', priority: 5, visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: 'AWS-AWSManagedRulesLinuxRuleSet' }),
                  hash_including(name: 'AWS-AWSManagedRulesSQLiRuleSet', priority: 6, visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: 'AWS-AWSManagedRulesSQLiRuleSet' })
                ],
                visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: '4mdg-waf-test' },
                lock_token:get_web_acl_response[:lock_token]
              )
            end

            context 'with error on updating the web acl' do
              before { client.stub_responses(:update_web_acl, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

              it 'logs an error' do
                expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

                subject.update_allowed_ips
              end
            end
          end

          context 'when the rule group is present on the waf rules' do
            let(:get_web_acl_response) { JSON.parse(file_fixture('aws/get_web_acl_with_rule_group.json').read).to_h.with_indifferent_access }

            it 'updates the web acl replacing the company rule group' do
              subject.update_allowed_ips

              api_request = client.api_requests.find { |request| request[:operation_name] == :update_web_acl }

              expect(api_request).not_to be_nil
              expect(api_request[:params]).to include(
                default_action: { allow: {} },
                name: Rails.application.credentials.waf_name, scope: Aws::WafService::CLOUDFRONT, id: Rails.application.credentials.waf_id,
                rules: [
                  hash_including(name: 'CrossSiteScripting_BODY_WithHeader', priority: 0),
                  hash_including(name: 'AWS-AWSManagedRulesAmazonIpReputationList', priority: 1),
                  hash_including(name: 'AWS-AWSManagedRulesCommonRuleSet', priority: 2),
                  hash_including(name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet', priority: 3),
                  hash_including(name: 'AWS-AWSManagedRulesLinuxRuleSet', priority: 4),
                  hash_including(name: 'AWS-AWSManagedRulesSQLiRuleSet', priority: 5),
                  hash_including(
                    name: "#{company.subdomain}-#{Rails.env}", priority: 6,
                    statement: { rule_group_reference_statement: { arn: "arn:aws:wafv2:us-foo-1:123456789123:global/rulegroup/test-test/96e751f9-bb01-425f-b8c8-8aee19da096f" } },
                    override_action: { none: {} },
                    visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "#{company.subdomain}-#{Rails.env}" }
                  )
                ],
                visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: '4mdg-waf-test' },
                lock_token: get_web_acl_response[:lock_token]
              )
            end

            context 'with error on updating the web acl' do
              before { client.stub_responses(:update_web_acl, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

              it 'logs an error' do
                expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

                subject.update_allowed_ips
              end
            end
          end
        end

        context 'with error on fetching the lock token' do
          before { client.stub_responses(:get_web_acl, Aws::WAFV2::Errors::WAFInvalidParameterException.new('foobar', 'foobar')) }

          it 'does not update the web acl' do
            expect(client).not_to receive(:update_web_acl)

            subject.update_allowed_ips
          end

          it 'logs an error' do
            expect(Rails.logger).to receive(:error).with("[WAFSERVICE][Aws::WAFV2::Errors::WAFInvalidParameterException] foobar")

            subject.update_allowed_ips
          end
        end
      end
    end
  end
end
