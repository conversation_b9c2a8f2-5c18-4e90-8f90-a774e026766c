en:
  locale:
    pt-BR_enum: 0
    en_enum: 1
    es_enum:  2

  authorization_mailer:
    notification_mailer:
      click_here: Click here for the access

  activemodel:
    errors:
      models:
        dashboard_searcher:
          attributes:
            business_id:
              blank: The business must be informed

            business_group_id:
              blank: The business group must be informed

            days_elapsed:
              blank: The number of days in traffic must be informed
              not_a_number: The number of days in traffic do not a number
              not_an_integer: The number of days in traffic must be an integer number
              greater_than_or_equal_to: The number of days in traffic must be larger or equal the %{count}

            days_elapsed_search:
              blank: The filter of number days in traffic must be informed
              inclusion: The filter of number days in traffic informed is do not valid
        field:
          default_value:
            match_type: The default value provided does not match the field type.

  activerecord:
    errors:
      messages:
        record_invalid: "The validation fail: %{errors}"
        restrict_dependent_destroy:
          has_one: Cannot exclude the register because a dependent %{record} exist.
          has_many: Cannot exclude the register because a dependent %{record} exists.
        password_recovery: "This password recovery link has already been used. Please request a new one."
        forbidden: "You not have permission"
        forbidden_to_view_record: "You do not have permission to access this record"
        not_found: "Record not found"
        cant_delete_search:
          one: "This search cannot be deleted as it is present in the '%{title}' statistic"
          many: "This search cannot be deleted as it is present in the '%{title}' statistics"

      services:
        answer:
          step_is_not_awaiting_approval: "The step '%{step_name}' is not awaiting approval"
          step_requires_approval_but_no_authorizer: "The step requires approval but there is no user enabled to authorize it"
          user_cannot_approve_this_step: "This user cannot approve this step."
          cannot_review_from_this_step: "Cannot review from this step"
          step_does_not_belong_to_the_business: "The step does not belong to the business informed"
          user_must_be_present: "The user id must be present for the step %{step_id}"
          user_unauthorized_to_update_step: "The user is unauthorized for to update this step."
          answer_not_found: 'Answer not found.'
          cannot_set_status_to_changing: 'Cannot set status to: changing. The step has not been completed.'
          content_discarded: 'Cannot set status to: changing. The content has been deleted.'

        bulk_answer:
          no_updated_data: "There is no data to update"
          alteration_processing: "Select a change before proceeding"

        content:
          invalid_steps: "The steps with IDs %{invalid_steps} are invalid."
          step_is_not_available_for_filling: "The step %{step_name} was skipped as it is not available for filling"
          tag_not_found_for_step: "The step %{step_name} was skipped as a matching <step> tag was not founded in the submitted xml"
          business_not_fount: "Cannot found the business %{guid}"
          invalid_xml: "The submitted xml document is invalid"
          error_saving_xml_content: "Cannot create/update content from the xml %{content_xml}: %{errors}"
          could_not_update_step: "Cannot update the step %{step_name} of content %{content_id}: %{errors}"
          subbusiness_saving: "Error saving subbusiness : %{errors}"
          steps_to_change_ids_should_be_array: The attribute steps_to_change_ids must be an array
          step_id_should_be_string: "'%{step_id}' is invalid, it must be a string."
          step_id_should_be_a_guid: "'%{step_id}' is invalid, it must be a guid."

        elastic_searcher:
          contents_must_belong_to_same_business: "The contents must belong to same business"
          business_without_field_marked_to_show: "This business does not have any fields marked to view in the listing. Please select the fields you want to view on the previous screen"
          too_many_fields: "Too many fields to be processed! Please filter the fields for view."
          invalid_attribute: "Invalid attribute"
          index_not_found: "Elasticsearch index not found"
          invalid_request: "Invalid request to Elasticsearch"
          generic_error: "Elasticsearch error"
          empty_scope:
            one: "The id '%{id}' does not belong to any active business or does not exist"
            many: "The ids '%{id}' do not belong to any active business or do not exist"

        field:
          cannot_add_new: "This field does not allow new items"

        twilio:
          chat_disabled: 'Chat feature disabled'

        validation_url:
          comunication_error: "Cannot communicate with validation URL %{validation_url} - Code: %{code}"
          simple_comunication_error: "Cannot communicate with validation URL %{validation_url} - Message: %{message}"
          timeout_error: "Timeout in communicate with validation URL %{validation_url}"
          invalid_response: "Invalid response from the validation URL defined in the step"
          invalid_return: The step validation URL did not return a valid response.

        verification_url:
          timeout_error: "Timeout in communicate with verification URL %{verification_url}"

      validators:
        invalid_date: "The value '%{value}' is not a valid date for field %{field}"
        invalid_decimal: "The value '%{value}' is not a valid value for field %{field}"
        invalid_decimal_separator: "The value '%{value}' is not a valid value for field %{field} (the decimal separator must be '.')"
        invalid_integer: "The value '%{value}' is not a valid integer for field %{field}"
        invalid_option: "The value %{value} is not among the valid options for field %{field}"
        invalid_link_format: "The value %{value} is an invalid for field %{field}. It must have the format {\"url\": \"\", \"label\": \"\"}"
        invalid_link_json: "The value %{value} is an invalid for field %{field}. It must be a json"
        invalid_multiple: "The value %{value} is not an array for field %{field}"
        invalid_reference: "The value %{value} is an invalid for field %{field}"
        invalid_required: "The field %{field} is required"
        invalid_file: "The file %{filename} is an invalid. Files %{invalid_files} is an invalids."
        invalid_file_size: "The file %{filename} is very large (%{size} MB). The maximum size allowed is %{max_size} MB."
        update_disabled: "The field %{label} value cannot be changed."
        invalid_ip: "The IP %{ip} is not valid."

      workers:
        foreign_key_constraint_violation: "Violation of foreign key restriction on fields: %{fields_labels} from business: %{businesses_names}"
        unconfigured_primary_key: "The primary key for business %{business_name} is unconfigured."
        record_with_same_keys: "Already exist a record with same keys: %{keys}."
        no_record_found: 'No record was found with the key(s) provided'
        no_parent_record_found: 'No parent record was found with the key(s) provided'
        webhook_request_faild: 'A communication error occurred with the url provided for webhook'

      models:
        alteration_processing:
          attributes:
            alterations:
              blank: At least one change must be added.
              required: At least one change must be added.

            total_alterations:
              greater_than: To be valid, the criteria must change at least one record

        administrator:
          attributes:
            password:
              blank: The password must be filled in

        answer:
          attributes:
            authorized_at:
              blank: The authorization date must informed

            authorizer:
              blank: The authorizer must be informed
              cannot_approve: This user cannot approve this step

            available_at:
              blank: The of availability date must be informed
              update_unallowed: It is not allowed to update the availability date
              step_is_not_yet_available: No record was found with the key(s) provided

            content:
              required: The content must be informed

            concluded_at:
              blank: The date of conclusion must be informed

            filled_at:
              blank: The fill date must be informed

            first_fill_at:
              blank: The date of the first filling must be informed

            step:
              required: The step must be informed
              taken: There is already exist a filled of this step for this form

            user:
              required: The user must be informed

            data:
              invalid_json: The filled values are invalid

          duplicate_key_field_value: "Violation of key(s) field(s) \"%{pk_labels}\", there is already a record with same keys: %{duplicate_pk_labels}"

        answer_processing:
          duplicate_key_field_value: "Violation of key(s) field(s) \"%{pk_labels}\", cannot insert duplicate value"

        business:
          attributes:
            business_group:
              required: Select a business group
            sub_business:
              cannot_be_updated: Cannot be changed

        business_header:
          cannot_be_multiple: cannot be multiple reference
          items_exceeded: Number of items exceeded

        company:
          invalid_open_id_config: Invalid OpenID configuration, please review the data.
          attributes:
            block_menus:
              unpermitted_value: Entered menu is not allowed

        content:
          attributes:
            business:
              inactive: This business is inactive
              required: The business must be informed

          duplicate_key_field_value: "Violation of key(s) field(s) \"%{pk_labels}\", cannot insert duplicate value"
          invalid_business: "Invalid business id: Parent business must have among its fields a sub business field referencing the business %{business_name}"
          cant_delete_sub_content: This content field is required, the last record cannot be deleted

        dependent_field_rule:
          field_required: "Validation error: %{field_label} is required."
          validation: "Validation error: %{field_label} must be %{field_operator} \"%{field_value}\"."
          must_belong_to_a_step_before_field: "Field: \"%{field_label}\" from %{step_order} step: \"%{step_name}\" must belong to a step before field: \"%{parent_field_label}\" from %{parent_step_order} Step 1: \"%{parent_step_name}\""
          must_belong_to_a_same_step_field: "Field: \"%{field_label}\" from %{step_order} step: \"%{step_name}\" must belong to the same step of field: \"%{parent_field_label}\" from %{parent_step_order} Step 1: \"%{parent_step_name}\""
          cannot_be_multiple: "Condition filter field cannot be of multiple type, file upload, link, subbusiness or multiple reference"
          cannot_be_updated: "The field to be changed cannot be of the multiple type, link, subbusiness or multiple reference"

        dependent_reference_field:
          field:
            must_be_reference: "Field %{field_label} must be of type reference"
          parent_field:
            must_be_reference: "Field %{field_label} must be of type reference"
            invalid_parent_field_reference_field: "The 'Value Field' register in the business reference field '%{field_reference_business_name}' for the business '%{parent_field_reference_business_name}' must be the same as the field '%{parent_field_reference_value_field_label}' of the business '%{parent_field_reference_business_name}'."

        field:
          attributes:
            order:
              not_a_number: The order must be a number
              numericality:
                only_integer: The order must be a integer
                greater_than_or_equal_to: The order must be a number greater than or equal to %{count}

              blank: The order must be filled

            options:
              blank: The options must be filled
              present: Options can only be provided when the field is a list.
              invalid_json: The options provided are invalid.

            reference_field_id:
              present: A reference should only be provided when the field is of type 'reference'

            reference_business_id:
              present: A reference should only be provided when the field is of type 'reference'

            template:
              required: Select a template

          cannot_reference_itself: "The reference field cannot be the field itself"
          subbusiness_cannot_reference_itself: "Sub business field must reference a valid sub business"
          duplicate_subbusiness_reference: "There is already another field registered referencing this sub business"
          max_subbusiness_reached: "The maximum number of sub business has already been reached"
          unallowed_duplicates: "Duplicate options are not allowed"
          unallowed_update: "It is not possible to change the type of a field already filled in a form"
          same_label_with_different_type_with_steps: "Already exists at least one field with the same name and different type on the steps %{steps}"
          same_label_with_different_type: "Already exists at least one field with the same name and different type on the template"
          dependent_field_rule: "It is not possible to delete the field, as it is being used in some business rule."

        field_validation:
          attributes:
            operator:
              taken: There is already a validation of this type for the same operator
              number_of_characters: "This operator is not allowed for character number validation."
              content: "This operator is not allowed for content validation."
              extension: "This operator is not allowed for file extension validation."
              file_size: "This operator is not allowed for file size validation."
              regex: "This operator is not allowed for regex validation."
            field_id:
              no_validations_allowed: "Field type does not allow validation"
            type:
              unallowed_validation_for_upload: "This validation is only allowed for upload type field"
              unallowed_validation_for_text: "This validation is only allowed for text type fields"
            data:
              valid_regex: The %{data} regex is not a valid regular expression"

        step:
          attributes:
            description:
              blank: The description must be filled.

            business:
              required: Select a business

            order:
              not_a_number: The order must be a number
              numericality:
                only_integer: The order must be an integer
                greater_than_or_equal_to: The order must be a number greater than or equal to %{count}

              blank: Order must be filled

          cannot_have_inactive_steps: "The business cannot inactive steps"
          error_moving: "Error moving, step lock is registered with a step that must be before"
          repeated_order: "Error saving, order is repeated!"
          cannot_add_more_steps: "Cannot add more than one step to a sub business"
          only_steps_after: "It is not allowed to add for reopening steps that are before the current one"
          cannot_add_not_self: "It is not allowed to add itself to be reopened"
          informed_user_has_no_access: "The informed user does not have access to this step"

        step_template:
          attributes:
            step:
              required: Select a step

            template:
              required: Select a template

            template_id:
              taken: The template has already been added to this step

            order:
              not_a_number: The order must be a number
              numericality:
                only_integer: The order must be an integer
                greater_than_or_equal_to: The order must be a number greater than or equal to %{count}

              blank: Order must be filled

          repeated_subbusiness: "There is already another business referencing this template with sub business."

        template:
          already_taken: "This template is in use in the business(ies): %{business_name}"

        translation:
          actable_uniqueness: "There is already a translation for this language"

        user:
          attributes:
            block_menus:
              unpermitted_value: Entered menu is not allowed
            password:
              blank: Password must be filled
              too_short:
                one: 'is too short (minimum: 1 character)'
                other: 'is too short (minimum: %{count} characters)'

        step_permission:
          attributes:
            user:
              at_least_user_or_department: User or Department must be filled
              only_user_or_department: Only User or Department must be filled.

    models:
      administrator:
        one: Administrator
        many: Administrators

      answer:
        one: Form
        many: Forms

      business:
        one: Business
        many: Negócios

      business_group:
        one: Business group
        many: Business groups

      business_header:
        one: Header
        many: Headers

      company:
        one: Company
        many: Companies

      content:
        one: Contents
        many: Contents

      data_replacement:
        one: Data replacement
        many: Data replacements

      field:
        one: Field
        many: Fields

      search:
        one: Search
        many: Searches

      step:
        one: Step
        many: Steps

      step_template:
        one: Step Template
        many: Step Templates

      template:
        one: Template
        many: Templates

      user:
        one: User
        many: Users

      department:
        one: Department
        many: Departments

      step_permission:
        one: Step permission
        many: Step permissions

      field_validation:
        one: Field validation
        many: Field validations

      notification:
        one: Notification
        many: Notifications

      answer_processing: Response processing
      bulk_saving_answer: Bulk Response Saving
      content_value: Content value
      favorite: Favorite
      field_option: Field option
      theme: Theme
      troubleshooting: Problems solution
      alteration_processing: Change processing
      dependent_field_rule: Dependent field rule
      dependent_reference_field: Dependent reference field
      habtm_key_fields: Habtm Key Fields
      old_password: Old Password
      active_storage/attachment: Active storage / attachment
      active_storage/blob: Storage / active blob
      active_storage/variant_record: Active storage / variant record
      bulk_destroying_content: Mass deletion
      content_destroying: Content deletion

    attributes:
      administrator:
        confirmation_sent_at: confirmation sent on
        confirmation_token: confirmation token
        confirmed_at: confirmed in
        current_sign_in_at: current login on
        current_sign_in_ip: Current login IP
        email: email
        encrypted_password: encrypted password
        image: image
        last_sign_in_at: last login in
        last_sign_in_ip: Last login IP
        name: name
        nickname: nickname
        provider: provider
        remember_created_at: remember created in
        reset_password_sent_at: reset password sent on
        reset_password_token: reset password token
        tokens: tokens
        uid: uid
        unconfirmed_email: unconfirmed email
        allow_password_change: Allow password change
        approved: Approved
        deleted_at: Delete at

      answer:
        values: values
        content_id: content
        content: content
        order: order
        status: status
        status_list:
          pending: pending
          done: done
          waiting_authorization: waiting authorization
          rejected: reject
          changing: changing
          under_review: under review

        step_id: step
        step: step
        user_id: user
        user: user
        data: data
        deleted_at: delet at
        description: Description
        name: name
        concluded_at: Date of the conclusion
        validation_url: Validation URL
        verification_url: Verification URL
        authorized_at: Authorized at
        authorizer: Authorized by
        requires_authorization: Requires authorization
        available_at: Available at
        filled_at: Filled at
        first_fill_at: First fill at
        required_authorization: Required authorization
        content_values: Content values
        position: Position
        answer_processings: Answer processings
        created_by: :activerecord.models.created_by

      business:
        business_group: business group
        business_group_id: business group
        description: description
        help_url: help URL
        name: name
        notification: notification
        sub_business: sub business
        type: type
        type_list:
          everyone: public
          individual: physical person
          legal: legal person
          city_hall: city Hall
          offshore: offshore
          foreign: foreign
          refund: professional reimbursement
          raw_material: raw material
          finished_good: finished product
          resale_material: resale material

        active_steps: active steps
        deleted_at: deleted at
        steps: steps
        contents: Contents
        key_fields: key fields
        show_on_dashboard: Show on dashboard
        sub_business: Sub business
        show_bulk_alteration: Show bulk alteration
        show_bulk_insert: Show bulk insert
        bulk_insert_on_first_step_validates_pk: Validate primary key duplication in step 1 of bulk registration
        fields: Fields

      business_group:
        description: description
        name: name
        businesses: business
        deleted_at: deleted at

      business_header:
        business: business
        business_id: business
        field: field
        field_id: field
        step: step
        step_id: step

      company:
        name: name
        subdomain: subdomain
        api_key: API key
        background_content_type: Background Content Type
        background_file_name: Background file name
        background_file_size: Background file size
        background_updated_at: Background updated at
        internal_logo_content_type: Internal logo Content Type
        internal_logo_file_name: Internal logo file name
        internal_logo_file_size: Internal logo file size
        internal_logo_updated_at: Internal logo updated at
        favicon_content_type: Favicon Content Type
        favicon_file_name: Favicon file name
        favicon_file_size: Favicon file size
        favicon_updated_at: Favicon updated at
        logo_content_type: Logo Content Type
        logo_file_name: Logo file name
        logo_file_size: Logo file size
        logo_updated_at: Logo updated at
        theme: Theme
        expire_password_after_in_days: Password expires after a few days
        use_elasticsearch: Use elasticsearch
        allowed_sites: Allowed sites
        auth_domain: Authentication domain
        background_image_attachment: :activerecord.models.background_image_attachment
        background_image_blob: :activerecord.models.background_image_blob
        enable_email_and_password_login: Enable email login and password
        enable_google_oauth: Enable google oauth
        enable_microsoft_oauth: Enable microsoft oauth
        logo_image_attachment: :activerecord.models.logo_image_attachment
        logo_image_blob: :activerecord.models.logo_image_blob
        internal_logo_image_attachment: :activerecord.models.internal_logo_image_attachment
        internal_logo_image_blob: :activerecord.models.internal_logo_image_blob
        favicon_image_attachment: :activerecord.models.favicon_image_attachment
        favicon_image_blob: :activerecord.models.favicon_image_blob

      content:
        business_id: business
        business: business
        answers: answers
        deleted_at: deleted at
        name: name
        status: status
        status_list:
          pending: pending
          doing: doing
          done: done
          waiting_authorization: waiting authorization
          rejected: reject
          changing: changing
          under_review: under review

        steps: steps
        draft: draft
        concluded_at: Conclued at
        content_values: Content values
        created_by: Created by
        parent: Parent record
        current_answer: :activerecord.models.current_answer
        keywords: Keywords
        note: Note

      data_replacement:
        replacement: Replace with
        text: Text

      field:
        char_max_limit: maximum character limit
        label: label
        order: order
        required: required
        size: size
        reference_sub_business_id: Sub business
        size_list:
          small: small
          normal: medium
          big: big

        template: template
        template_id: template
        tooltip: tooltip
        type: type
        reference_business_id: business
        reference_field_id: field for display
        reference_field: field for display
        type_list:
          text: text
          integer: integer
          text_area: long text
          dropdown: list
          date: date
          decimal: decimal
          telephone: telephone
          reference: reference
          multiple: multiple text

        show_on_list: show on list
        show_on_form: show on form
        deleted_at: deleted at
        options: options
        reference_business: business
        validations: validations
        reference_value_field: Value field
        enabled: active
        visible: visible
        field_options: Field options
        reference_sub_business: Sub business
        default_value: Default value
        text_transformation: text transformation
        text_transformation_list:
          uppercase: uppercase
          lowercase: lowercase
          capitalize: capitalize

      search:
        advanced: advanced
        description: description
        field_names: fields to be displayed
        query: query
        title: title
        business_id: business
        user_id: user

      step:
        name: name
        description: description
        business: business
        business_id: business
        order: order
        deleted_at: deleted at
        step_permissions: step permissions
        step_templates: step by step models
        templates: templates
        verification_url: verification URL
        validation_url: validation URL
        requires_authorization: Requires authorization
        authorizers: Authorizers
        dependent_reference_fields: Dependent reference fields
        fields: Fields
        success_message: Success message

      step_template:
        step: step
        step_id: step
        template: template
        template_id: template
        order: order

      template:
        description: description
        name: name
        active_fields: active fields
        deleted_at: deleted at
        fields: fields
        step_templates: step by step models
        steps: steps
        deleted_fields: deleted fields

      translation:
        attribute_name: attribute name
        language: language
        translated_text: translated text
        type_list:
          portuguese: portuguese
          english: english
          spanish: spanish

      user:
        confirmation_sent_at: confirmation sent at
        confirmation_token: token de confirmação
        confirmed_at: confirmation token
        current_sign_in_at: current login at
        current_sign_in_ip: current login IP
        email: email
        encrypted_password: encrypted password
        image: image
        last_sign_in_at: last login at
        last_sign_in_ip: Last login IP
        name: name
        nickname: nickname
        provider: provider
        password: password
        remember_created_at: remember created at
        reset_password_sent_at: reset password sent at
        reset_password_token: reset password token
        tokens: tokens
        uid: uid
        unconfirmed_email: unconfirmed email
        deleted_at: deleted at
        department: :activerecord.models.department
        authorizations: Authorizations
        department_authorizations: Department authorizations
        department_permissions: Department permissions
        permissions: Permissions
        coordinator: Coordinator
        limited: Limited
        old_passwords: Old passwords
        password_changed_at: Password changed at
        allow_password_change: Allow password change
        approved: Approved

      department:
        name: name
        deleted_at: deleted at
        users: users
        authorizations: Authorizations
        permissions: Permissions
        limited: Limited

      step_permission:
        department: department
        step: step
        user: user
        scope: Scope

      field_validation:
        data: data
        deleted_at: deleted at
        field: field
        operator: operator
        operator_list:
          less_than: Less than
          equal_to: Equal to
          greater_than: Greater than
          contains: Contains
          not_contains: Not contains

        type: type
        type_list:
          number_of_characters: Number of characters
          content: Content
          extension: File extension
          file_size: File size

        error_message: Error message

      answer_processing:
        bulk_saving_answer: Bulk Response Save
        data: Data
        processing_errors: Processing errors
        status: Status
        data_with_verification_url_response: Data with verification url response

      bulk_saving_answer:
        answer_processings: processing
        end_at: End at
        origin: Origin
        start_at: Start at
        status: Status
        step: Step
        user: User
        alteration_processing: Change processing
        business: Business

      content_value:
        answer: Answer
        content: Content
        field: Field
        value: Value

      favorite:
        business: Business
        user: User

      field_option:
        content: Content
        field: Field
        label: Label
        order: Order
        value: Value

      theme:
        background_color: Background color
        button_color: Button color
        menu_color: Menu color
        menu_focus_color: Menu focus color
        name: Name
        text_color: Text color

      troubleshooting:
        data: Data
        duration_in_seconds: Duration in seconds
        end_at: Ent at
        external: External
        response_code: Response code
        start_at: Start at
        subdomain: Subdomain
        url: URL

      alteration_processing:
        alterations: Alterations
        approvals: Approvals
        bulk_action: Action
        bulk_saving_answer: Grouping of changes
        criterions: Criterions
        processing_errors: Processing errors
        status: Status
        successes: Successes
        total_alterations: Total alterations
        validation_url: Validation URL
        verification_url: Verification URL

      dependent_field_rule:
        description: Description
        rule_type: Type
        condition_operator: Condition operator
        condition_value: Condition value
        field: Field
        field_required: Field required
        field_value: Value field
        parent_field: Dependent field
        parent_step: Dependent step
        step: step
        rule_type_list:
          dependent: 'Fill'
          validation: 'Validation'
          dynamic_dependent: 'Dynamic fill'
          access_control: 'Access control'
        condition_operator_list: &operator_list
          equals: Equals
          lower: Lower
          greater: Greater
          less_than: Less than
          greater_than: Greater than
          contains: Contains
          not_contains: Not contains
          regex_match: Regex matches
          regex_not_match: Regex does not match
          not_equal: Different
        field_operator_list:
          <<: *operator_list

      dependent_reference_field:
        field: Field
        parent_field: Dependent field
        parent_step: Dependent step
        step: Step

      habtm_key_fields:
        key_field: :activerecord.models.key_field
        left_side: :activerecord.models.left_side

      old_password:
        encrypted_password: Encrypted password
        password_archivable: :activerecord.models.password_archivable
        password_archivable_type: Archiveable password type
        password_salt: Password salt

      active_storage/attachment:
        blob: :activerecord.models.blob
        name: Name
        record: :activerecord.models.record
        record_type: Record type

      active_storage/blob:
        attachments: Attachments
        byte_size: Byte size
        checksum: Checksum
        content_type: Content type
        filename: Filename
        key: Key
        metadata: Metadata
        preview_image_attachment: :activerecord.models.preview_image_attachment
        preview_image_blob: :activerecord.models.preview_image_blob
        service_name: Service name
        variant_records: Variant Records

      active_storage/variant_record:
        blob: :activerecord.models.blob
        image_attachment: :activerecord.models.image_attachment
        image_blob: :activerecord.models.image_blob
        variation_digest: Variation summary

      bulk_destroying_content:
        business_name: Business name
        company: :activerecord.models.company
        content_destroyings: Content destruction
        end_at: End at
        start_at: Start at
        status: Status

      content_destroying:
        bulk_destroying_content: :activerecord.models.bulk_destroying_content
        processing_errors: Processing errors
        status: Status

      show_on_list_field:
        field: Field
        field_id: Field

      notification:
        message: Message
        title: Title
        user_id: User
        user: User

  datatables:
    answer_version:
      ip_not_obtained: 'IP NOT OBTAINED'

  services:
    answer:
      answer_not_found: 'Answer not found.'
      cannot_set_status_to_changing: 'Cannot set status to: changing. The step has not been completed.'
      content_discarded: 'Cannot set status to: changing. The content has been deleted.'
      review_request:
        message: '%{date} - Review request for step %{step} requested by user %{user}'
        note: 'Observation: %{note}'
      step_reviewed:
        message: '%{date} - Step %{step} reviewed by user %{user}'
        note: 'Observation: %{note}'
