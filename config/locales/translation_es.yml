es:
  locale:
    pt-BR_enum: 0
    en_enum: 1
    es_enum:  2

  authorization_mailer:
    notification_mailer:
      click_here: Clic aquí para acceder

  activemodel:
    errors:
      models:
        dashboard_searcher:
          attributes:
            business_id:
              blank: El negocio debe ser informado

            business_group_id:
              blank: El grupo de negocios debe ser informado

            days_elapsed:
              blank: El número de días en tránsito debe ser reportado
              not_a_number: El número de días de tránsito no es un número
              not_an_integer: El número de días de tránsito debe ser un número entero
              greater_than_or_equal_to: El número de días en tránsito debe ser mayor o igual a %{count}

            days_elapsed_search:
              blank: El filtro del número de días en tránsito debe ser informado
              inclusion: El filtro del número de días en tránsito introducido no es válido
        field:
          default_value:
            match_type: El valor predeterminado proporcionado no corresponde al tipo de campo.

  activerecord:
    errors:
      messages:
        record_invalid: "La validación ha fallado:%{errors}"
        restrict_dependent_destroy:
          has_one: El registro no puede ser eliminado porque hay un %{record} dependiente
          has_many: No es posible eliminar el registro porque hay  %{record} dependientes
        password_recovery: "Este link de recuperación de contraseña ya ha sido utilizado. Por favor, solicite uno nuevo."
        forbidden: "No tienes permiso"
        forbidden_to_view_record: "No tienes permiso para ver este registro"
        not_found: "Registro no encontrado"
        cant_delete_search:
          one: "Esta encuesta no se puede eliminar porque está presente en la estadística '%{title}'"
          many: "Esta búsqueda no se puede eliminar porque está presente en las estadísticas de '%{title}'"

      services:
        answer:
          step_is_not_awaiting_approval: "El paso '%{step_name}' no está pendiente de aprobación"
          step_requires_approval_but_no_authorizer: "El paso requiere aprobación pero ningún usuario puede autorizarlo"
          user_cannot_approve_this_step: "Este usuario no puede aprobar este paso"
          cannot_review_from_this_step: "No es posible revisar desde esta fase"
          step_does_not_belong_to_the_business: "El paso no pertenece a la empresa denunciada"
          user_must_be_present: "El id de usuario debe estar presente para el id de paso %{step_id}"
          user_unauthorized_to_update_step: "El usuario no puede modificar este paso."
          answer_not_found: "Respuesta no encontrada."
          cannot_set_status_to_changing: "No se puede establecer el estado en: cambiando. El paso no se ha completado."
          content_discarded: 'No se puede establecer el estado en: cambiando. El contenido ha sido eliminado.'

        bulk_answer:
          no_updated_data: "No hay datos que mantener"
          alteration_processing: "Seleccione un cambio antes de continuar"

        content:
          invalid_steps: "Los pasos con IDs %{invalid_steps} no son válidos."
          step_is_not_available_for_filling: "El paso %{step_name} ha sido omitido ya que no está disponible para rellenar"
          tag_not_found_for_step: "Se ha omitido el paso %{step_name} porque no se ha encontrado la etiqueta <step> correspondiente en el XML enviado"
          business_not_fount: "No se pudo encontrar el acuerdo %{guid}"
          invalid_xml: "El documento XML enviado no es válido"
          error_saving_xml_content: "No se pudo crear/actualizar el contenido del xml %{content_xml}: %{errors}"
          could_not_update_step: "No se ha podido actualizar el paso %{step_name} del contenido %{content_id}: %{errors}"
          subbusiness_saving: "Error al guardar la sub negocio : %{errors}"
          steps_to_change_ids_should_be_array: El atributo steps_to_change_ids debe ser un arreglo
          step_id_should_be_string: "'%{step_id}' no es válido, debe ser una cadena."
          step_id_should_be_a_guid: "'%{step_id}' no es válido, debe ser un guid."

        elastic_searcher:
          contents_must_belong_to_same_business: "El contenido debe pertenecer a la misma negocio"
          business_without_field_marked_to_show: "Este negocio no tiene ningún campo seleccionado para ver en el listado. Seleccione los campos que desea ver en la pantalla anterior"
          too_many_fields: "Demasiados campos para procesar. Por favor, filtre los campos a mostrar."
          invalid_attribute: "Atributo inválido"
          index_not_found: "Índice de Elasticsearch no encontrado"
          invalid_request: "Solicitud inválida a Elasticsearch"
          generic_error: "Error de Elasticsearch"
          empty_scope:
            one: "El id '%{id}' no pertenece a ningún negocio activo o no existe"
            many: "Los ids '%{id}' no pertenecen a ningún negocio activo o no existen"

        field:
          cannot_add_new: "Este campo no permite agregar nuevos elementos"

        twilio:
          chat_disabled: 'Chat desabilitado'

        validation_url:
          comunication_error: "No se ha podido comunicar con la URL de validación %{validation_url} - Código: %{code}"
          simple_comunication_error: "No se ha podido comunicar con la URL de validación %{validation_url} - Mensaje: %{message}"
          timeout_error: "Tiempo de espera en la comunicación con la URL de validación %{validation_url}"
          invalid_response: "Respuesta no válida de la URL de validación definida en el paso"
          invalid_return: "La URL de validación del paso no devolvió una respuesta válida."

        verification_url:
          timeout_error: "Tiempo de espera en la comunicación con la URL de validación %{verification_url}"
      validators:
        invalid_date: "El valor '%{value}' no es una fecha válida para el campo %{field}"
        invalid_decimal: "El valor '%{value}' no es un valor válido para el campo %{field}"
        invalid_decimal_separator: "El valor '%{value}' no es un valor válido para el campo %{field} (el separador decimal debe ser '.')"
        invalid_integer: "El valor '%{value}' no es un entero válido para el campo %{field}"
        invalid_option: "El valor %{value} no está entre las opciones válidas para el campo %{field}"
        invalid_link_format: "El valor %{value} no es válido para el campo %{field}. Debe adoptar la forma {\"url\": \"\", \"label\": \"\"}"
        invalid_link_json: "El valor %{value} no es válido para el campo %{field}. Debe ser un json"
        invalid_multiple: "El valor %{value} no es una array para el campo %{field}"
        invalid_reference: "El valor %{value} no es válido para el campo %{field}}"
        invalid_required: "El campo %{field} debe ser rellenado"
        invalid_file: "El archivo %{filename} no está permitido. Los archivos %{invalid_files} no están permitidos."
        invalid_file_size: "El archivo %{filename} es demasiado grande (%{size} MB). El tamaño máximo permitido es %{max_size} MB."
        update_disabled: "El valor del campo %{label} no puede ser cambiado."
        invalid_ip: "La IP %{ip} no es válido."

      workers:
        foreign_key_constraint_violation: "Violación de la restricción de clave foránea en los campos: %{fields_labels} del negocio: %{businesses_names}"
        unconfigured_primary_key: "No se ha establecido la clave primaria para lo negocio %{business_name}."
        record_with_same_keys: "Ya existe un registro con las mismas claves: %{keys}."
        no_record_found: 'No se encontró ningún registro con las claves proporcionadas.'
        no_parent_record_found: 'No se encontró ningún registro padre con las claves proporcionadas.'
        webhook_request_faild: 'Se produjo un error de comunicación con la URL proporcionada para el webhook'

      models:
        alteration_processing:
          attributes:
            alterations:
              blank: Hay que añadir al menos un cambio.
              required: Hay que añadir al menos un cambio.

            total_alterations:
              greater_than: Para ser válidos, los criterios deben cambiar al menos un registro

        administrator:
          attributes:
            password:
              blank: Hay que rellenar la contraseña

        answer:
          attributes:
            authorized_at:
              blank: La fecha de aprobación debe ser informada

            authorizer:
              blank: El autorizador debe ser informado
              cannot_approve: Este usuario no puede aprobar este paso

            available_at:
              blank: Debe informarse de la fecha de liberación
              update_unallowed: No se permite la actualización de la fecha de lanzamiento
              step_is_not_yet_available: Este paso aún no está disponible para completarse en el registro proporcionado.

            content:
              required: El contenido debe ser informado

            concluded_at:
              blank: La fecha de finalización debe ser informada

            filled_at:
              blank: Debe informarse de la fecha de finalización

            first_fill_at:
              blank: Debe informarse de la fecha del primer llenado

            step:
              required: El paso debe ser informado
              taken: Este paso ya existe para este formulario

            user:
              required: El usuario debe ser informado

            data:
              invalid_json: Los valores introducidos no son válidos

          duplicate_key_field_value: "Violación de campo(s) clave(s) \"%{pk_labels}\", ya existe un registro con las mismas claves: %{duplicate_pk_labels}"

        answer_processing:
          duplicate_key_field_value: "Violación de campo(s) clave(s) \"%{pk_labels}\", no se puede introducir un valor duplicado"

        business:
          attributes:
            business_group:
              required: Seleccione un grupo de negocios
            sub_business:
              cannot_be_updated: no se puede modificar

        business_header:
          cannot_be_multiple: no se puede ser múltiple
          items_exceeded: Número de elementos superados

        company:
          invalid_open_id_config: Configuración de OpenID no válida, revise los datos.
          attributes:
            block_menus:
              unpermitted_value: El menú ingresado no está permitido

        content:
          attributes:
            business:
              inactive: Este negocio está inactivo
              required: El negocio debe ser informado

          duplicate_key_field_value: "Violación de campo(s) clave(s) \"%{pk_labels}\", no se puede introducir un valor duplicado"
          invalid_business: "ID de negocio inválido: El negocio padre debe tener entre sus campos un campo de sub-negocio que haga referencia al negocio %{business_name}"
          cant_delete_sub_content: Este campo de contenido es obligatorio, el último registro no se puede eliminaro

        dependent_field_rule:
          field_required: "Error de validación: %{field_label} debe ser completado."
          validation: "Error de validación: %{field_label} debe ser %{field_operator} \"%{field_value}\"."
          must_belong_to_a_step_before_field: "El campo: \"%{field_label}\" de %{step_order}ª escenario: \"%{step_name}\" debe pertenecer a una etapa anterior al campo: \"%{parent_field_label}\" de %{parent_step_order}ª escenario: \"%{parent_step_name}\""
          must_belong_to_a_same_step_field: "El campo: \"%{field_label}\" de %{step_order}ª escenario: \"%{step_name}\" debe pertenecer a mesma etapa que al campo: \"%{parent_field_label}\" de %{parent_step_order}ª escenario: \"%{parent_step_name}\""
          cannot_be_multiple: "El campo de filtro de condición no puede ser de tipo múltiple, carga de archivos, enlace, negocio secundario o referencia múltiple"
          cannot_be_updated: "El campo a modificar no puede ser de tipo múltiple, link, negocio secundario o referencia múltiple"

        dependent_reference_field:
          field:
            must_be_reference: "El campo %{etiqueta_de_campo} debe ser de tipo referencia"
          parent_field:
            must_be_reference: "El campo %{field_label} debe ser de tipo referencia"
            invalid_parent_field_reference_field: "El 'Campo de valor' registrado en el campo de referencia de lo negocio '%{field_reference_business_name}' para el negocio '%{parent_field_reference_business_name}' debe ser igual al campo '%{parent_field_reference_value_field_label}' del negocio '%{parent_field_reference_business_name}'."

        field:
          attributes:
            order:
              not_a_number: La orden debe ser un número
              numericality:
                only_integer: El orden debe ser un entero
                greater_than_or_equal_to: El orden debe ser un número mayor o igual a %{count}

              blank: La orden debe ser llenada

            options:
              blank: Las opciones deben ser rellenadas
              present: Las opciones sólo pueden proporcionarse cuando el campo es una lista
              invalid_json: Las opciones proporcionadas no son válidas

            reference_field_id:
              present: Sólo debe proporcionarse una referencia cuando el campo es del tipo "referencia".

            reference_business_id:
              present: Sólo debe proporcionarse una referencia cuando el campo es del tipo "referencia".

            template:
              required: Seleccione una plantilla

          cannot_reference_itself: "El campo de referencia no puede ser el propio campo"
          subbusiness_cannot_reference_itself: "El campo de sub negocios debe hacer referencia a un sub negocio válido"
          duplicate_subbusiness_reference: "Ya existe otro campo registrado que hace referencia a esta subnegocio"
          max_subbusiness_reached: "Ya se ha alcanzado el número máximo de sub negocios"
          unallowed_duplicates: "No se permite la duplicación de opciones"
          unallowed_update: "No es posible cambiar el tipo de un campo ya rellenado en un formulario"
          same_label_with_different_type_with_steps: "Ya existe al menos un campo con el mismo nombre y diferente tipo en los pasos %{steps}"
          same_label_with_different_type: "Ya existe al menos un campo con el mismo nombre y diferente tipo en pantilla"
          dependent_field_rule: "No es posible eliminar el campo porque se está utilizando en alguna regla comercial."

        field_validation:
          attributes:
            operator:
              taken: Esta validación ya existe para el mismo operador
              number_of_characters: "Este operador no está permitido para la validación del número de caracteres."
              content: "Este operador no está permitido para la validación del contenido."
              extension: "Este operador no está permitido para la validación de la extensión del archivo."
              file_size: "Este operador no está permitido para la validación del tamaño del archivo."
              regex: "Este operador no está permitido para la validación regex."
            field_id:
              no_validations_allowed: "El tipo de campo no permite validaciones"
            type:
              unallowed_validation_for_upload: "Esta validación sólo está permitida para los campos de tipo upload"
              unallowed_validation_for_text: "Esta validación sólo se permite para los campos de tipo texto"
            data:
              valid_regex: "La regex %{data} no es una expresión regular válida"

        step:
          attributes:
            description:
              blank: La descripción debe ser rellenada

            business:
              required: Seleccione un negocio

            order:
              not_a_number: La orden debe ser un número
              numericality:
                only_integer: La orden debe ser un número entero
                greater_than_or_equal_to: El orden debe ser un número mayor o igual a %{count}

              blank: El pedido debe ser atendido

            step_for_revision_ids:
              not_valid: 'Los siguientes ID no son válidos para los pasos de revisión: %{ids}'

          cannot_have_inactive_steps: "Lo negocio no puede quedarse sin pasos activos"
          error_moving: "Error al desplazarse, el bloqueo del paso se registra con un paso que debe quedar antes"
          repeated_order: "Error al guardar, ¡el pedido se repite!"
          cannot_add_more_steps: "No se puede añadir más de un paso a una subnegocio"
          only_steps_after: "No se permite agregar para reapertura pasos anteriores al actual"
          cannot_add_not_self: "No se permite agregarse para ser reabierto"
          informed_user_has_no_access: "El usuario informado no tiene acceso a este paso"

        step_template:
          attributes:
            step:
              required: Seleccione un paso

            template:
              required: Seleccione una plantilla

            template_id:
              taken: La plantilla ya ha sido añadida a este paso

            order:
              not_a_number: La orden debe ser un número
              numericality:
                only_integer: La orden debe ser un número entero
                greater_than_or_equal_to: El orden debe ser un número mayor o igual a %{count}

              blank: El pedido debe ser atendido

          repeated_subbusiness: "Ya hay otro negocio que hace referencia a esta plantilla con sub negocios."

        template:
          already_taken: "Esta plantilla se utiliza en lo(s) negocio(s): %{business_name}"

        translation:
          actable_uniqueness: "Ya existe una traducción para este idioma"

        user:
          attributes:
            block_menus:
              unpermitted_value: El menú ingresado no está permitido
            password:
              blank: Hay que rellenar la contraseña
              too_short:
                one: 'es muy corto (mínimo: 1 carácter)'
                other: 'es demasiado corto (mínimo: %{count} caracteres)'

        step_permission:
          attributes:
            user:
              at_least_user_or_department: Se debe rellenar el usuario o el departamento
              only_user_or_department: Sólo se debe rellenar Usuario o Departamento

    models:
      administrator:
        one: Administrador
        many: Administradores

      answer:
        one: Formulario
        many: Formularios

      business:
        one: Negocios
        many: Negocios

      business_group:
        one: Grupo de Negocios
        many: Grupos de Negocios

      business_header:
        one: Encabezado
        many: Encabezados

      company:
        one: Empresa
        many: Empresas

      content:
        one: Contenido
        many: Contenidos

      data_replacement:
        one: Reemplazo de datos
        many: Reemplazo de datos

      field:
        one: Campo
        many: Campos

      search:
        one: Búsqueda
        many: Búsquedas

      step:
        one: Escenario
        many: Escenarios

      step_template:
        one: Plantilla de pasos
        many: Plantillas de pasos

      template:
        one: Plantilla
        many: Plantillas

      user:
        one: Usuario
        many: Usuarios

      department:
        one: Departamento
        many: Departamentos

      step_permission:
        one: Permiso de paso
        many: Permisos de paso

      field_validation:
        one: Validación de campo
        many: Validaciones de campo

      notification:
        one: Notificación
        many: Notificaciones

      answer_processing: Procesamiento de la respuesta
      bulk_saving_answer: Respuesta masiva de ahorro
      content_value: Valor del contenido
      favorite: Favoritos
      field_option: Opción de campo
      theme: Tema
      troubleshooting: Solución de problemas
      alteration_processing: Tramitación de enmiendas
      dependent_field_rule: Regla del campo dependiente
      dependent_reference_field: Campo de referencia dependiente
      habtm_key_fields: Campos clave de Habtm
      old_password: Contraseña antigua
      active_storage/attachment: Almacenamiento / fijación activa
      active_storage/blob: Almacenamiento / blob activo
      active_storage/variant_record: Variante de almacenamiento activo/registro
      bulk_destroying_content: Eliminación masiva
      content_destroying: Eliminación de contenidos

    attributes:
      administrator:
        confirmation_sent_at: confirmación enviada en
        confirmation_token: ficha de confirmación
        confirmed_at: confirmado en
        current_sign_in_at: el inicio de sesión actual en
        current_sign_in_ip: IP de acceso actual
        email: correo electrónico
        encrypted_password: contraseña encriptada
        image: imagen
        last_sign_in_at: última entrada en
        last_sign_in_ip: Última IP de acceso
        name: nombre
        nickname: apellido
        provider: proveedor
        remember_created_at: recuerdan creados en
        reset_password_sent_at: se ha enviado la contraseña de restablecimiento
        reset_password_token: restablecer contraseña token
        tokens: tokens
        uid: uid
        unconfirmed_email: correo electrónico no confirmado
        allow_password_change: Permitir el cambio de contraseña
        approved: Aprobado
        deleted_at: Borrado en

      answer:
        values: valores
        content_id: contenido
        content: contenido
        order: pedir
        status: estado
        status_list:
          pending: colgante
          done: completado
          waiting_authorization: a la espera de la aprobación
          rejected: rechazado
          changing: en cambio
          under_review: en revisión

        step_id: escenario
        step: escenario
        user_id: usuario
        user: usuario
        data: datos
        deleted_at: Borrado en
        description: Descripción
        name: nombre
        concluded_at: Fecha de finalización
        validation_url: URL de validación
        verification_url: URL de verificación
        authorized_at: Autorizado en
        authorizer: Autorizado por
        requires_authorization: Requiere aprobación
        available_at: Disponible en
        filled_at: Rellenado
        first_fill_at: Primero rellena
        required_authorization: Autorización requerida
        content_values: Valores del contenido
        position: Posición
        answer_processings: Procesos de respuesta
        created_by: :activerecord.models.created_by

      business:
        business_group: grupo de negocios
        business_group_id: grupo de negocios
        description: descripción
        help_url: Ayuda URL
        name: nombre
        notification: notificación
        sub_business: Sub negocio
        type: tipo
        type_list:
          everyone: público
          individual: persona física
          legal: persona jurídica
          city_hall: ayuntamiento
          offshore: offshore
          foreign: extranjero
          refund: reembolso profesional
          raw_material: materia prima
          finished_good: producto terminado
          resale_material: material de reventa

        active_steps: pasos activos
        deleted_at: borrado
        steps: pasos
        contents: Contenido
        key_fields: campos clave
        show_on_dashboard: Mostrar en el panel
        sub_business: Negocio secundario
        show_bulk_alteration: Mostrar el cambio de masa
        show_bulk_insert: Mostrar la inserción de la masa
        bulk_insert_on_first_step_validates_pk: Validar la duplicidad de la clave primaria en el paso 1 del registro masivo
        fields: Campos

      business_group:
        description: descripción
        name: nombre
        businesses: empresas
        deleted_at: borrado

      business_header:
        business: negocio
        business_id: negocio
        field: campo
        field_id: campo
        step: escenario
        step_id: escenario

      company:
        name: nombre
        subdomain: subdominio
        api_key: Clave API
        background_content_type: Tipo de contenido de fondo
        background_file_name: Nombre del archivo de fondo
        background_file_size: Tamaño del archivo de fondo
        background_updated_at: Antecedentes actualizados en
        internal_logo_content_type: Logotipo interno Tipo de contenido
        internal_logo_file_name: Nombre del archivo del logotipo interno
        internal_logo_file_size: Tamaño del archivo de logotipo interno
        internal_logo_updated_at: Logotipo interno actualizado en
        favicon_content_type: Favicon Tipo de contenido
        favicon_file_name: Nombre del archivo del favicon
        favicon_file_size: Tamaño del archivo de favicon
        favicon_updated_at: Favicon actualizado en
        logo_content_type: Tipo de contenido del logotipo
        logo_file_name: Nombre del archivo del logotipo
        logo_file_size: Tamaño del archivo del logotipo
        logo_updated_at: Logotipo actualizado en
        theme: Tema
        expire_password_after_in_days: La contraseña caduca a los pocos días
        use_elasticsearch: Utilizar elasticsearch
        allowed_sites: Sitios permitidos
        auth_domain: Dominio de autenticación
        background_image_attachment: :activerecord.models.background_image_attachment
        background_image_blob: :activerecord.models.background_image_blob
        enable_email_and_password_login: Habilitar el inicio de sesión y la contraseña del correo electrónico
        enable_google_oauth: Habilitar google oauth
        enable_microsoft_oauth: Habilitar microsoft oauth
        logo_image_attachment: :activerecord.models.logo_image_attachment
        logo_image_blob: :activerecord.models.logo_image_blob
        internal_logo_image_attachment: :activerecord.models.internal_logo_image_attachment
        internal_logo_image_blob: :activerecord.models.internal_logo_image_blob
        favicon_image_attachment: :activerecord.models.favicon_image_attachment
        favicon_image_blob: :activerecord.models.favicon_image_blob

      content:
        business_id: negocio
        business: negocio
        answers: respuestas
        deleted_at: borrado
        name: nombre
        status: estado
        status_list:
          pending: colgante
          doing: en curso
          done: completado
          waiting_authorization: a la espera de la aprobación
          rejected: rechazado
          changing: en cambio
          under_review: en revisión

        steps: pasos
        draft: esbozar
        concluded_at: Terminado en
        content_values: Valores del contenido
        created_by: Creado por
        parent: Registro de padres
        current_answer: :activerecord.models.current_answer
        keywords: Palabras clave
        note: Nota

      data_replacement:
        replacement: Reemplazar con
        text: Texto

      field:
        char_max_limit: límite máximo de caracteres
        label: etiqueta
        order: pedir
        required: obligatorio
        size: tamaño
        reference_sub_business_id: Negocio secundario
        size_list:
          small: pequeño
          normal: medio
          big: gran

        template: plantilla
        template_id: plantilla
        tooltip: tooltip
        type: tipo
        reference_business_id: Negocio
        reference_field_id: campo de visualización
        reference_field: campo de visualización
        type_list:
          text: texto
          integer: todo
          text_area: texto largo
          dropdown: lista
          date: fecha
          decimal: decimal
          telephone: teléfono
          reference: referencia
          multiple: texto múltiple

        show_on_list: mostrar en la lista
        show_on_form: mostrar en la forma
        deleted_at: borrado
        options: opciones
        reference_business: negocio
        validations: validaciones
        reference_value_field: Campo de valor
        enabled: activo
        visible: visible
        field_options: opciones de campo
        reference_sub_business: negocio secundario
        default_value: valor por defecto
        text_transformation: transformación de texto
        text_transformation_list:
          uppercase: mayúscula
          lowercase: minúscula
          capitalize: capitalizar

      search:
        advanced: avanzado
        description: descripción
        field_names: campos para mostrar
        query: query
        title: título
        business_id: negocio
        user_id: usuario

      step:
        name: nombre
        description: descripción
        business: negocio
        business_id: negocio
        order: pedir
        deleted_at: borrado
        step_permissions: permisos de paso
        step_templates: modelos paso a paso
        templates: modelos
        verification_url: URL de verificación
        validation_url: URL de validación
        requires_authorization: Requiere aprobación
        authorizers: Autorizadores
        dependent_reference_fields: Campos de referencia dependientes
        fields: Campos
        success_message: Mensaje de éxito

      step_template:
        step: escenario
        step_id: escenario
        template: plantilla
        template_id: plantilla
        order: pedir

      template:
        description: descripción
        name: nombre
        active_fields: campos activos
        deleted_at: borrado
        fields: campos
        step_templates: modelos paso a paso
        steps: pasos
        deleted_fields: campos excluidos

      translation:
        attribute_name: nombre del atributo
        language: idioma
        translated_text: texto traducido
        type_list:
          portuguese: portugués
          english: inglés
          spanish: español

      user:
        confirmation_sent_at: confirmación enviada en
        confirmation_token: token de confirmación
        confirmed_at: confirmado en
        current_sign_in_at: el inicio de sesión actual en
        current_sign_in_ip: IP de acceso actual
        email: correo electrónico
        encrypted_password: contraseña encriptada
        image: imagen
        last_sign_in_at: última entrada en
        last_sign_in_ip: Última IP de acceso
        name: nombre
        nickname: apellido
        provider: proveedor
        password: contraseña
        remember_created_at: recuerdan creados en
        reset_password_sent_at: se ha enviado la contraseña de restablecimiento
        reset_password_token: restablecer contraseña token
        tokens: tokens
        uid: uid
        unconfirmed_email: correo electrónico no confirmado
        deleted_at: borrado
        department: :activerecord.models.department
        authorizations: Autorizaciones
        department_authorizations: Autorizaciones departamentales
        department_permissions: Permisos de los departamentos
        permissions: Permisos
        coordinator: Coordinador
        limited: Limitado
        old_passwords: Contraseñas antiguas
        password_changed_at: La contraseña ha sido cambiada en
        allow_password_change: Permitir el cambio de contraseña
        approved: Aprobado

      department:
        name: nombre
        deleted_at: borrado
        users: usuarios
        authorizations: Autorizaciones
        permissions: Permisos
        limited: Limitado

      step_permission:
        department: departamento
        step: escenario
        user: usuario
        scope: Ámbito

      field_validation:
        data: datos
        deleted_at: borrado
        field: campo
        operator: operador
        operator_list:
          less_than: Menos de
          equal_to: Igual a
          greater_than: Mayor que
          contains: Contiene
          not_contains: No contiene

        type: tipo
        type_list:
          number_of_characters: Número de caracteres
          content: Contenido
          extension: Extensión del archivo
          file_size: Tamaño del archivo

        error_message: Mensaje de error

      answer_processing:
        bulk_saving_answer: Respuesta masiva Ahorro
        data: Datos
        processing_errors: Errores de procesamiento
        status: Estado
        data_with_verification_url_response: Datos con respuesta de url de verificación

      bulk_saving_answer:
        answer_processings: en proceso
        end_at: Termina en
        origin: Fuente
        start_at: Comienza en
        status: Estado
        step: Escenario
        user: Usuario
        alteration_processing: Tramitación de enmiendas
        business: Negocio

      content_value:
        answer: Respuesta
        content: Contenido
        field: Campo
        value: Valor

      favorite:
        business: Negocio
        user: Usuario

      field_option:
        content: Contenido
        field: Campo
        label: Etiqueta
        order: Pida
        value: Valor

      theme:
        background_color: Color de fondo
        button_color: Color de los botones
        menu_color: Color del menú
        menu_focus_color: Color de enfoque del menú
        name: Nombre
        text_color: Color del texto

      troubleshooting:
        data: Datos
        duration_in_seconds: Duración en segundos
        end_at: Termina en
        external: Exterior
        response_code: Código de respuesta
        start_at: Comienza en
        subdomain: Subdominio
        url: URL

      alteration_processing:
        alterations: Enmiendas
        approvals: Homologaciones
        bulk_action: Acción
        bulk_saving_answer: Agrupación de enmiendas
        criterions: Criterios
        processing_errors: Errores de procesamiento
        status: Estado
        successes: Éxitos
        total_alterations: Total de modificaciones
        validation_url: Url de validación
        verification_url: Url de verificación

      dependent_field_rule:
        description: Descripción
        rule_type: Tipo
        condition_operator: Condición de operador
        condition_value: Valor de la condición
        field: Campo
        field_required: Campo obligatorio
        field_value: Valor del campo
        parent_field: Campo dependiente
        parent_step: Paso dependiente
        step: Escenario
        rule_type_list:
          dependent: 'Llenado'
          validation: 'Validación'
          dynamic_dependent: 'Dependencia dinámica'
          access_control: 'Controle de acceso'
        condition_operator_list: &operator_list
          equals: Igual a
          lower: Menos de
          greater: Mayor que
          less_than: Menos de
          greater_than: Mayor que
          contains: Contiene
          not_contains: No contiene
          regex_match: Coincidencias Regex
          regex_not_match: El Regex no coincide
          not_equal: Diferentes
        field_operator_list:
          <<: *operator_list

      dependent_reference_field:
        field: Campo
        parent_field: Campo dependiente
        parent_step: Paso dependiente
        step: Escenario

      habtm_key_fields:
        key_field: :activerecord.models.key_field
        left_side: :activerecord.models.left_side

      old_password:
        encrypted_password: Contraseña encriptada
        password_archivable: :activerecord.models.password_archivable
        password_archivable_type: Tipo de contraseña archivable
        password_salt: Sal de la contraseña

      active_storage/attachment:
        blob: :activerecord.models.blob
        name: Nombre
        record: :activerecord.models.record
        record_type: Tipo de registro

      active_storage/blob:
        attachments: Anexos
        byte_size: Tamaño del byte
        checksum: Checksum
        content_type: Tipo de contenido
        filename: Nombre del archivo
        key: Clave
        metadata: Metadatos
        preview_image_attachment: :activerecord.models.preview_image_attachment
        preview_image_blob: :activerecord.models.preview_image_blob
        service_name: Nombre del servicio
        variant_records: Registros de variantes

      active_storage/variant_record:
        blob: :activerecord.models.blob
        image_attachment: :activerecord.models.image_attachment
        image_blob: :activerecord.models.image_blob
        variation_digest: Resumen de la variación

      bulk_destroying_content:
        business_name: Nombre de la empresa
        company: :activerecord.models.company
        content_destroyings: Destrucción de contenidos
        end_at: Termina en
        start_at: Comienza en
        status: Estado

      content_destroying:
        bulk_destroying_content: :activerecord.models.bulk_destroying_content
        processing_errors: Errores de procesamiento
        status: Estado

      show_on_list_field:
        field: Campo
        field_id: Campo

      notification:
        message: Mensaje
        title: Título
        user_id: Usuario
        user: Usuario

  datatables:
    answer_version:
      ip_not_obtained: 'IP NO OBTENIDO'

  services:
    answer:
      answer_not_found: 'Respuesta no encontrada.'
      cannot_set_status_to_changing: "No se puede establecer el estado en: cambiando. El paso no se ha completado."
      content_discarded: 'No se puede establecer el estado en: cambiando. El contenido ha sido eliminado.'
      review_request:
        message: '%{date} - Solicitación de revisión del paso %{step}: usuario %{user}'
        note: 'Observación: %{note}'
      step_reviewed:
        message: '%{date} - Paso %{step} revisado: usuario %{user}'
        note: 'Observación: %{note}'
