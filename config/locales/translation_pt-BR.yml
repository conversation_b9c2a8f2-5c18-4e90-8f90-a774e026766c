pt-BR:
  locale:
    pt-BR_enum: 0
    en_enum: 1
    es_enum:  2

  authorization_mailer:
    notification_mailer:
      click_here: Clique aqui para acessá-lo

  activemodel:
    errors:
      models:
        dashboard_searcher:
          attributes:
            business_id:
              blank: O negócio deve ser informado

            business_group_id:
              blank: O grupo de negócio deve ser informado

            days_elapsed:
              blank: O número de dias em trânsito deve ser informado
              not_a_number: O número de dias em trânsito não é um número
              not_an_integer: O número de dias em trânsito deve ser um número inteiro
              greater_than_or_equal_to: O número de dias em trânsito deve ser maior ou igual a %{count}

            days_elapsed_search:
              blank: O filtro do número de dias em trânsito deve ser informado
              inclusion: O filtro do número de dias em trânsito informado não é válido
        field:
          default_value:
            match_type: O valor padrão informado não condiz com o tipo do campo.

  activerecord:
    errors:
      messages:
        record_invalid: "A validação falhou: %{errors}"
        restrict_dependent_destroy:
          has_one: Não é possível excluir o registro pois existe um %{record} dependente
          has_many: Não é possível excluir o registro pois existem %{record} dependentes
        password_recovery: "Esse link para recuperação de senha já foi utilizado. Por favor solicite um novo."
        forbidden: "Você não possui permissão para acessar esse recurso"
        forbidden_to_view_record: "Você não tem permissão para acessar este registro"
        not_found: "Registro não encontrado"
        cant_delete_search:
          one: "Essa pesquisa não pode ser deletada pois está presente na estatística '%{title}'"
          many: "Essa pesquisa não pode ser deletada pois está presente nas estatísticas '%{title}'"

      services:
        answer:
          step_is_not_awaiting_approval: "A etapa '%{step_name}' não está aguardando aprovação"
          step_requires_approval_but_no_authorizer: "A etapa requer aprovação mas não existe nenhum usuário habilitado à autorizá-la"
          user_cannot_approve_this_step: "Esse usuário não pode aprovar essa etapa"
          cannot_review_from_this_step: "Não é possível revisar à partir desta etapa"
          step_does_not_belong_to_the_business: "A etapa não pertence ao negócio informado"
          user_must_be_present: "O id de usuário deve estar presente para a etapa de id %{step_id}"
          user_unauthorized_to_update_step: "Usuário não tem permissão para alterar esta etapa."
          answer_not_found: "Resposta não encontrada."
          cannot_set_status_to_changing: "Não é possível definir o status para: em alteração. A etapa não foi concluída."
          content_discarded: "Não é possível definir o status para: em alteração. O conteúdo foi excluído."


        bulk_answer:
          no_updated_data: "Não há dados a serem atualizados"
          alteration_processing: "Selecione uma alteração antes de prosseguir"

        content:
          invalid_steps: "As etapas com ids %{invalid_steps} são inválidas."
          step_is_not_available_for_filling: "A etapa %{step_name} foi ignorada pois ela não está disponível para preenchimento"
          tag_not_found_for_step: "A etapa %{step_name} foi ignorada pois não foi encontrado uma tag <step> correspondente no XML enviado"
          business_not_fount: "Não foi possível encontrar o négocio %{guid}"
          invalid_xml: "O documento XML enviado é inválido"
          error_saving_xml_content: "Não foi possível criar/atualizar o conteúdo à partir do xml %{content_xml}: %{errors}"
          could_not_update_step: "Não foi possível atualizar a etapa %{step_name} do conteúdo %{content_id}: %{errors}"
          subbusiness_saving: "Erro ao salvar o subnegócio : %{errors}"
          steps_to_change_ids_should_be_array: Atributo steps_to_change_ids deve ser um array
          step_id_should_be_string: "'%{step_id}' inválido, deve ser uma string."
          step_id_should_be_a_guid: "'%{step_id}' inválido, deve ser um guid."

        elastic_searcher:
          contents_must_belong_to_same_business: "Os contents devem pertencer ao mesmo negócio"
          business_without_field_marked_to_show: "Esse negócio não tem nenhum campo marcado para visualizar na listagem. Por favor selecione na tela anterior os campos que deseja visualizar"
          too_many_fields: "Muitos campos para serem processados! Por favor filtre os campos a serem exibidos."
          invalid_attribute: "Atributo inválido"
          index_not_found: "Índice do Elasticsearch não encontrado"
          invalid_request: "Requisição inválida para o Elasticsearch"
          generic_error: "Erro no Elasticsearch"
          empty_scope:
            one: "O id '%{id}' não pertence a nenhum negócio ativo ou não existe"
            many: "Os ids '%{id}' não pertencem a nenhum negócio ativo ou não existem"

        field:
          cannot_add_new: "Esse campo não permite a adição de novos itens"

        twilio:
          chat_disabled: 'Chat desabilitado'

        validation_url:
          comunication_error: "Não foi possível comunicar com a URL de validação %{validation_url} - Código: %{code}"
          simple_comunication_error: "Não foi possível comunicar com a URL de validação %{validation_url} - Mensagem: %{message}"
          timeout_error: "Timeout na comunicação com a URL de validação %{validation_url}"
          invalid_response: "Resposta inválida da URL de validação configurada na etapa"
          invalid_return: "A URL de validação da etapa, não trouxe um retorno válido."

        verification_url:
          timeout_error: "Timeout na comunicação com a URL de validação %{verification_url}"

      validators:
        invalid_date: "O valor '%{value}' não é uma data válida para o campo %{field}"
        invalid_decimal: "O valor '%{value}' não é um valor válido para o campo %{field}"
        invalid_decimal_separator: "O valor '%{value}' não é um valor válido para o campo %{field} (o separador decimal deve ser '.')"
        invalid_integer: "O valor '%{value}' não é um inteiro válido para o campo %{field}"
        invalid_option: "O valor %{value} não está dentre as opções válidas para o campo %{field}"
        invalid_link_format: "O valor %{value} não é válido para o campo %{field}. Ele deve ter o formato {\"url\": \"\", \"label\": \"\"}"
        invalid_link_json: "O valor %{value} não é válido para o campo %{field}. Ele deve ser um JSON."
        invalid_multiple: "O valor %{value} não é um array para o campo %{field}"
        invalid_reference: "O valor %{value} não é válido para o campo %{field}"
        invalid_required: "O campo %{field} deve ser preenchido"
        invalid_file: "O arquivo %{filename} não é permitido. Arquivos %{invalid_files} não são permitidos."
        invalid_file_size: "O arquivo %{filename} é muito grande (%{size} MB). O tamanho máximo permitido é %{max_size} MB."
        update_disabled: "O valor do campo %{label} não pode ser alterado"
        invalid_ip: "O IP %{ip} não é válido."

      workers:
        foreign_key_constraint_violation: "Violação da restrição de chave estrangeira nos campos: %{fields_labels} dos negócios: %{businesses_names}."
        unconfigured_primary_key: "A chave primária para o negócio %{business_name} não foi configurada."
        record_with_same_keys: "Já existe um cadastro com as mesmas chaves: %{keys}."
        no_record_found: 'Não foi encontrado nenhum registro com a(s) chave(s) informada(s).'
        no_parent_record_found: 'Não foi encontrado nenhum registro pai com a(s) chave(s) informada(s).'
        webhook_request_faild: 'Ocorreu um erro de comunicação com a url informada para webhook'

      models:
        alteration_processing:
          attributes:
            alterations:
              blank: Ao menos uma alteração deve ser adicionada.
              required: Ao menos uma alteração deve ser adicionada.

            total_alterations:
              greater_than: Para serem válidos, os critérios devem alterar ao menos um registro

        administrator:
          attributes:
            password:
              blank: A senha deve ser preenchida

        answer:
          attributes:
            authorized_at:
              blank: A data de aprovação deve ser informada

            authorizer:
              blank: O autorizador deve ser informado
              cannot_approve: Esse usuário não pode aprovar essa etapa

            available_at:
              blank: A data de disponibilização deve ser informada
              update_unallowed: Não é permitido atualizar a data de disponibilização
              step_is_not_yet_available: Essa etapa ainda não está disponível para preenchimento no registro informado

            content:
              required: O conteúdo deve ser informado

            concluded_at:
              blank: A data de conclusão deve ser informada

            filled_at:
              blank: A data de preenchimento deve ser informada

            first_fill_at:
              blank: A data do primeiro preenchimento deve ser informada

            step:
              required: A etapa deve ser informada
              taken: Já existe um preenchimento dessa etapa para esse formulário

            user:
              required: O usuário deve ser informado

            data:
              invalid_json: Os valores preenchidos são inválidos

          duplicate_key_field_value: "Violação de campo(s) chave(s) \"%{pk_labels}\", já existe um registro com mesmas chaves: %{duplicate_pk_labels}"

        answer_processing:
          duplicate_key_field_value: "Violação de campo(s) chave(s) \"%{pk_labels}\", não é possível inserir valor duplicado"

        business:
          attributes:
            business_group:
              required: Selecione um grupo de negócio
            sub_business:
              cannot_be_updated: não pode ser alterado

        business_header:
          cannot_be_multiple: não pode ser uma referência múltipla
          items_exceeded: Número de itens excedido

        company:
          invalid_open_id_config: Configuração do OpenID inválida, por favor revise os dados.
          attributes:
            block_menus:
              unpermitted_value: Menu informado não é permitido

        content:
          attributes:
            business:
              inactive: Esse negócio está inativo
              required: O negócio deve ser informado

          duplicate_key_field_value: "Violação de campo(s) chave(s) \"%{pk_labels}\", não é possível inserir valor duplicado"
          invalid_business: "Id do negócio inválido: O négocio pai deve ter dentre seus campos um campo de subnegócio referenciando o negócio %{business_name}"
          cant_delete_sub_content: O campo desse content é requerido, o último registro não pode ser excluído

        dependent_field_rule:
          field_required: "Erro de validação: %{field_label} deve ser preenchido."
          validation: "Erro de validação: %{field_label} deve ser %{field_operator} \"%{field_value}\"."
          must_belong_to_a_step_before_field: "O campo: \"%{field_label}\" da %{step_order}ª etapa: \"%{step_name}\" deve pertencer a uma etapa anterior ao campo: \"%{parent_field_label}\" da %{parent_step_order}ª etapa: \"%{parent_step_name}\""
          must_belong_to_a_same_step_field: "O campo: \"%{field_label}\" da %{step_order}ª etapa: \"%{step_name}\" deve pertencer à mesma etapa do campo: \"%{parent_field_label}\" da %{parent_step_order}ª etapa: \"%{parent_step_name}\""
          cannot_be_multiple: "O campo de filtro da condição não pode ser do tipo múltiplo, upload de arquivo, link, subnegócio ou referência múltipla"
          cannot_be_updated: "O campo a ser alterado não pode ser do tipo múltiplo, link, subnegócio ou referência múltipla"

        dependent_reference_field:
          field:
            must_be_reference: "O campo %{field_label} deve ser do tipo referência"
          parent_field:
            must_be_reference: "O campo %{field_label} deve ser do tipo referência"
            invalid_parent_field_reference_field: "O 'Campo de valor' cadastrado no campo de referência do negócio '%{field_reference_business_name}' para o negócio '%{parent_field_reference_business_name}' deve ser igual ao campo '%{parent_field_reference_value_field_label}' do negócio '%{parent_field_reference_business_name}'."

        field:
          attributes:
            order:
              not_a_number: A ordem deve ser um número
              numericality:
                only_integer: A ordem deve ser um número inteiro
                greater_than_or_equal_to: A ordem deve ser um número maior ou igual à %{count}

              blank: A ordem deve ser preenchida

            options:
              blank: As opções devem ser preenchidas
              present: As opções só podem ser fornecidas quando o campo é uma lista
              invalid_json: As opções fornecidas são inválidas

            reference_field_id:
              present: Só deve ser fornecido uma referência quando o campo for do tipo 'referência'

            reference_business_id:
              present: Só deve ser fornecido uma referência quando o campo for do tipo 'referência'

            template:
              required: Selecione um template

          cannot_reference_itself: "O campo de referência não pode ser o próprio campo"
          subbusiness_cannot_reference_itself: "O campo de sub negócio deve referenciar um subnegócio válido"
          duplicate_subbusiness_reference: "Já existe outro campo cadastrado referenciando este subnegócio"
          max_subbusiness_reached: "O número máximo de sub negócios já foi atingido"
          unallowed_duplicates: "Não são permitidas opções duplicadas"
          unallowed_update: "Não é possível alterar o tipo de um campo já preenchido em um formulário"
          same_label_with_different_type_with_steps: "Já existe ao menos um campo com o mesmo nome e tipo diferente na(s) etapa(s) %{steps}"
          same_label_with_different_type: "Já existe ao menos um campo com o mesmo nome e tipo diferente no template"
          dependent_field_rule: "Não é possível excluir o campo, pois ele está sendo utilizados em alguma regra de negócio."

        field_validation:
          attributes:
            operator:
              taken: Já existe uma validação desse tipo para o mesmo operador
              number_of_characters: "Esse operador não é permitido para a validação de número de caracteres."
              content: "Esse operador não é permitido para a validação de conteúdo."
              extension: "Esse operador não é permitido para a validação de extensão de arquivo."
              file_size: "Esse operador não é permitido para a validação de tamanho de arquivo."
              regex: "Esse operador não é permitido para a validação de regex."
            field_id:
              no_validations_allowed: "O tipo do campo não permite validações"
            type:
              unallowed_validation_for_upload: "Essa validação só é permitida para campo do tipo upload"
              unallowed_validation_for_text: "Essa validação só é permitida para campo do tipo texto"
            data:
              valid_regex: "A regex %{data} não é uma expressão regular válida"

        step:
          attributes:
            description:
              blank: A descrição deve ser preenchida

            business:
              required: Selecione um negócio

            order:
              not_a_number: A ordem deve ser um número
              numericality:
                only_integer: A ordem deve ser um número inteiro
                greater_than_or_equal_to: A ordem deve ser um número maior ou igual à %{count}

              blank: A ordem deve ser preenchida

            step_for_revision_ids:
              not_valid: 'Os seguintes IDs não são validos para etapas de revisão: %{ids}'

          cannot_have_inactive_steps: "O negócio não pode ficar sem etapas ativas"
          error_moving: "Erro ao mover, o bloqueio de etapa está cadastrado com uma etapa que deve ficar antes"
          repeated_order: "Erro ao salvar, a ordem está repetida!"
          cannot_add_more_steps: "Não é possível adicionar mais de um step a um subnegócio"
          only_steps_after: "Não é permitido adicionar etapas anteriores a essa para serem reabertas"
          cannot_add_not_self: "Não é permitido adicionar a própria etapa para ser reaberta"
          informed_user_has_no_access: "O usuário informado não tem acesso a esta etapa"

        step_template:
          attributes:
            step:
              required: Selecione uma etapa

            template:
              required: Selecione um template

            template_id:
              taken: O template já foi adicionado à essa etapa

            order:
              not_a_number: A ordem deve ser um número
              numericality:
                only_integer: A ordem deve ser um número inteiro
                greater_than_or_equal_to: A ordem deve ser um número maior ou igual à %{count}

              blank: A ordem deve ser preenchida

          repeated_subbusiness: "Já existe outro negócio referenciando este template com subnegócio."

        template:
          already_taken: "Esse template está em uso no(s) negócio(s): %{business_name}"

        translation:
          actable_uniqueness: "Já existe uma tradução para esse idioma"

        user:
          attributes:
            block_menus:
              unpermitted_value: Menu informado não é permitido
            password:
              blank: A senha deve ser preenchida
              too_short:
                one: 'é muito curta (mínimo: 1 caracter)'
                other: 'é muito curta (mínimo: %{count} caracteres)'

        step_permission:
          attributes:
            user:
              at_least_user_or_department: Usuário ou Departamento deve ser preenchido
              only_user_or_department: Somente Usuário ou Departamento deve ser preenchido

    models:
      administrator:
        one: Administrador
        many: Administradores

      answer:
        one: Formulário
        many: Formulários

      business:
        one: Negócio
        many: Negócios

      business_group:
        one: Grupo de negócio
        many: Grupos de negócio

      business_header:
        one: Cabeçalho
        many: Cabeçalhos

      company:
        one: Empresa
        many: Empresas

      content:
        one: Conteúdo
        many: Conteúdos

      data_replacement:
        one: Substituição de dados
        many: Substituições de dados

      field:
        one: Campo
        many: Campos

      search:
        one: Pesquisa
        many: Pesquisas

      step:
        one: Etapa
        many: Etapas

      step_template:
        one: Template da etapa
        many: Templates da etapa

      template:
        one: Template
        many: Templates

      user:
        one: Usuário
        many: Usuários

      department:
        one: Departamento
        many: Departamentos

      step_permission:
        one: Permissão da etapa
        many: Permissões da etapa

      field_validation:
        one: Validação do campo
        many: Validações do campo

      notification:
        one: Notificação
        many: Notificações

      answer_processing: Processamento de resposta
      bulk_saving_answer: Salvamento de resposta em massa
      content_value: Valor do conteúdo
      favorite: Favorito
      field_option: Opção de campo
      theme: Tema
      troubleshooting: Solução de problemas
      alteration_processing: Processamento de alterações
      dependent_field_rule: Regra de campo dependente
      dependent_reference_field: Campo de referência dependente
      habtm_key_fields: Campos-chave do Habtm
      old_password: Senha Antiga
      active_storage/attachment: Armazenamento / anexo ativo
      active_storage/blob: Armazenamento / blob ativo
      active_storage/variant_record: Armazenamento ativo / registro variante
      bulk_destroying_content: Deleção em massa
      content_destroying: Deleção de conteúdo

    attributes:
      administrator:
        confirmation_sent_at: confirmação enviada em
        confirmation_token: token de confirmação
        confirmed_at: confirmado em
        current_sign_in_at: login atual em
        current_sign_in_ip: IP do login atual
        email: email
        encrypted_password: senha criptografada
        image: imagem
        last_sign_in_at: último login em
        last_sign_in_ip: IP do último login
        name: nome
        nickname: apelido
        provider: fornecedor
        remember_created_at: lembre-se criado em
        reset_password_sent_at: redefinir senha enviada em
        reset_password_token: redefinir token de senha
        tokens: tokens
        uid: uid
        unconfirmed_email: email não confirmado
        allow_password_change: Permitir alteração de senha
        approved: Aprovado
        deleted_at: Excluído em

      answer:
        values: valores
        content_id: conteúdo
        content: conteúdo
        order: ordem
        status: status
        status_list:
          pending: pendente
          done: concluído
          waiting_authorization: aguardando aprovação
          rejected: reprovada
          changing: em alteração
          under_review: em revisão

        step_id: etapa
        step: etapa
        user_id: usuário
        user: usuário
        data: dados
        deleted_at: Excluído em
        description: Descrição
        name: nome
        concluded_at: Data de conclusão
        validation_url: URL de validação
        verification_url: URL de verificação
        authorized_at: Autorizado em
        authorizer: Autorizado por
        requires_authorization: Requer aprovação
        available_at: Disponível em
        filled_at: Preenchido em
        first_fill_at: Primeiro preenchimento em
        required_authorization: Autorização Requerida
        content_values: Valores de conteúdo
        position: Posição
        answer_processings: Processos de resposta
        created_by: :activerecord.models.created_by

      business:
        business_group: grupo de negócio
        business_group_id: grupo de negócio
        description: descrição
        help_url: URL de ajuda
        name: nome
        notification: notificação
        sub_business: subnegocio
        type: tipo
        type_list:
          everyone: público
          individual: pessoa física
          legal: pessoa jurídica
          city_hall: prefeitura
          offshore: offshore
          foreign: estrangeira
          refund: profissional reembolso
          raw_material: matéria prima
          finished_good: produto acabado
          resale_material: material de revenda

        active_steps: etapas ativas
        deleted_at: excluído em
        steps: passos
        contents: Conteúdos
        key_fields: campos chave
        show_on_dashboard: Mostrar no painel
        sub_business: Sub negócio
        show_bulk_alteration: Mostrar alteração em massa
        show_bulk_insert: Mostrar inserção em massa
        bulk_insert_on_first_step_validates_pk: Validar duplicidade chave primária na etapa 1 do cadastro em massa
        fields: Campos

      business_group:
        description: descrição
        name: nome
        businesses: empresas
        deleted_at: excluído em

      business_header:
        business: negócio
        business_id: negócio
        field: campo
        field_id: campo
        step: etapa
        step_id: etapa

      company:
        name: nome
        subdomain: subdomínio
        api_key: chave da API
        background_content_type: Tipo de conteúdo de fundo
        background_file_name: Nome do arquivo de plano de fundo
        background_file_size: Tamanho do arquivo de fundo
        background_updated_at: Plano de fundo atualizado em
        internal_logo_content_type: Tipo de conteúdo do logotipo interno
        internal_logo_file_name: Nome do arquivo do logotipo interno
        internal_logo_file_size: Tamanho do arquivo do logotipo interno
        internal_logo_updated_at: Logotipo interno atualizado em
        favicon_content_type: Tipo de conteúdo do favicon
        favicon_file_name: Nome do arquivo do favicon
        favicon_file_size: Tamanho do arquivo do favicon
        favicon_updated_at: Favicon atualizado em
        logo_content_type: Tipo de conteúdo do logotipo
        logo_file_name: Nome do arquivo de logotipo
        logo_file_size: Tamanho do arquivo do logotipo
        logo_updated_at: Logo atualizado em
        theme: Tema
        expire_password_after_in_days: Expira a senha após alguns dias
        use_elasticsearch: Use elasticsearch
        allowed_sites: Sites permitidos
        auth_domain: Domínio de autenticação
        default_department_id: Departamento padrão
        background_image_attachment: :activerecord.models.background_image_attachment
        background_image_blob: :activerecord.models.background_image_blob
        enable_email_and_password_login: Habilitar login de e-mail e senha
        enable_google_oauth: Ativar google oauth
        enable_microsoft_oauth: Ativar microsoft oauth
        logo_image_attachment: :activerecord.models.logo_image_attachment
        logo_image_blob: :activerecord.models.logo_image_blob
        internal_logo_image_attachment: :activerecord.models.internal_logo_image_attachment
        internal_logo_image_blob: :activerecord.models.internal_logo_image_blob
        favicon_image_attachment: :activerecord.models.favicon_image_attachment
        favicon_image_blob: :activerecord.models.favicon_image_blob

      content:
        business_id: negócio
        business: negócio
        answers: respostas
        deleted_at: excluído em
        name: nome
        status: status
        status_list:
          pending: pendente
          doing: em andamento
          done: concluído
          waiting_authorization: aguardando aprovação
          rejected: reprovada
          changing: em alteração
          under_review: em revisão

        steps: passos
        draft: esboço
        concluded_at: Concluído em
        content_values: Valores de conteúdo
        created_by: Criado por
        parent: Registro pai
        current_answer: :activerecord.models.current_answer
        keywords: Palavras-chave
        note: Nota

      data_replacement:
        replacement: Substituir por
        text: Texto

      field:
        char_max_limit: limite máximo de caracteres
        label: label
        order: ordem
        required: obrigatório
        size: tamanho
        reference_sub_business_id: Subnegócio
        size_list:
          small: pequeno
          normal: médio
          big: grande

        template: template
        template_id: template
        tooltip: tooltip
        type: tipo
        reference_business_id: Negócio
        reference_field_id: campo para exibição
        reference_field: campo para exibição
        type_list:
          text: texto
          integer: inteiro
          text_area: texto longo
          dropdown: lista
          date: data
          decimal: decimal
          telephone: telefone
          reference: referência
          multiple: texto múltiplo

        show_on_list: exibir na listagem
        show_on_form: exibir no formulário
        deleted_at: excluído em
        options: opções
        reference_business: business
        validations: validações
        reference_value_field: Campo de valor
        enabled: ativo
        visible: visível
        field_options: opções de campo
        reference_sub_business: sub business
        default_value: valor padrão
        text_transformation: transformação de texto
        text_transformation_list:
          uppercase: maiúsculo
          lowercase: minúsculo
          capitalize: capitalizar

      search:
        advanced: avançado
        description: descrição
        field_names: campos a serem exibidos
        query: query
        title: título
        business_id: negócio
        user_id: usuário

      step:
        name: nome
        description: descrição
        business: negócio
        business_id: negócio
        order: ordem
        deleted_at: excluído em
        step_permissions: permissões de etapa
        step_templates: modelos passo a passo
        templates: modelos
        verification_url: URL de verificação
        validation_url: URL de validação
        requires_authorization: Requer aprovação
        authorizers: Autorizadores
        dependent_reference_fields: Campos de referência dependentes
        fields: Campos
        success_message: Mensagem de sucesso

      step_template:
        step: etapa
        step_id: etapa
        template: template
        template_id: template
        order: ordem

      template:
        description: descrição
        name: nome
        active_fields: campos ativos
        deleted_at: excluído em
        fields: campos
        step_templates: modelos passo a passo
        steps: passos
        deleted_fields: campos excluídos

      translation:
        attribute_name: nome do atributo
        language: idioma
        translated_text: texto traduzido
        type_list:
          portuguese: português
          english: inglês
          spanish: espanhol

      user:
        confirmation_sent_at: confirmação enviada em
        confirmation_token: token de confirmação
        confirmed_at: confirmado em
        current_sign_in_at: login atual em
        current_sign_in_ip: IP do login atual
        email: email
        encrypted_password: senha criptografada
        image: imagem
        last_sign_in_at: último login em
        last_sign_in_ip: IP do último login
        name: nome
        nickname: apelido
        provider: fornecedor
        password: senha
        remember_created_at: lembre-se criado em
        reset_password_sent_at: redefinir senha enviada em
        reset_password_token: redefinir token de senha
        tokens: tokens
        uid: uid
        unconfirmed_email: email não confirmado
        deleted_at: excluído em
        department: :activerecord.models.department
        authorizations: Autorizações
        department_authorizations: Autorizações de departamento
        department_permissions: Permissões do departamento
        permissions: Permissões
        coordinator: Coordenador
        limited: Limitado
        old_passwords: Senhas antigas
        password_changed_at: Senha alterada em
        allow_password_change: Permitir alteração de senha
        approved: Aprovado

      department:
        name: nome
        deleted_at: excluído em
        users: usuários
        authorizations: Autorizações
        permissions: Permissões
        limited: Limitado

      step_permission:
        department: departamento
        step: etapa
        user: usuário
        scope: Escopo

      field_validation:
        data: dados
        deleted_at: excluído em
        field: campo
        operator: operador
        operator_list:
          less_than: Menor que
          equal_to: Igual à
          greater_than: Maior que
          contains: Contém
          not_contains: Não contém

        type: tipo
        type_list:
          number_of_characters: Número de caracteres
          content: Conteúdo
          extension: Extensão de arquivo
          file_size: Tamanho de arquivo

        error_message: Mensagem de erro

      answer_processing:
        bulk_saving_answer: Salvamento de resposta em massa
        data: Dados
        processing_errors: Erros de processamento
        status: Status
        data_with_verification_url_response: Dados com resposta de url de verificação

      bulk_saving_answer:
        answer_processings: em processamento
        end_at: Termina em
        origin: Origem
        start_at: Começa ás
        status: Status
        step: Etapa
        user: Usuário
        alteration_processing: Processamento de alterações
        business: Negócio

      content_value:
        answer: Resposta
        content: Conteúdo
        field: Campo
        value: Valor

      favorite:
        business: Negócio
        user: Usuário

      field_option:
        content: Conteúdo
        field: Campo
        label: Rótulo
        order: Ordem
        value: Valor

      theme:
        background_color: Cor de fundo
        button_color: Cor do botao
        menu_color: Cor do menu
        menu_focus_color: Cor do foco do menu
        name: Nome
        text_color: Cor do texto

      troubleshooting:
        data: Dados
        duration_in_seconds: Duração em segundos
        end_at: Termina em
        external: Externo
        response_code: Código de resposta
        start_at: Começa ás
        subdomain: Subdomínio
        url: URL

      alteration_processing:
        alterations: Alterações
        approvals: Aprovações
        bulk_action: Ação
        bulk_saving_answer: Agrupamento de alterações
        criterions: Critérios
        processing_errors: Erros de processamento
        status: Status
        successes: Sucessos
        total_alterations: Total de alterações
        validation_url: Url de validação
        verification_url: Url de verificação

      dependent_field_rule:
        description: Descrição
        rule_type: Tipo
        condition_operator: Operador de condição
        condition_value: Valor de condição
        field: Campo
        field_required: Campo requerido
        field_value: Valor do campo
        parent_field: Campo dependente
        parent_step: Etapa dependente
        step: Etapa
        rule_type_list:
          dependent: 'Preenchimento'
          validation: 'Validação'
          dynamic_dependent: 'Validação dinâmica'
          access_control: 'Controle de acesso'
        condition_operator_list: &operator_list
          equals: Igual a
          lower: Menor que
          greater: Maior que
          less_than: Menor que
          greater_than: Maior que
          contains: Contém
          not_contains: Não contém
          regex_match: Regex corresponde
          regex_not_match: Regex não corresponde
          not_equal: Diferente
        field_operator_list:
          <<: *operator_list

      dependent_reference_field:
        field: Campo
        parent_field: Campo dependente
        parent_step: Etapa dependente
        step: Etapa

      habtm_key_fields:
        key_field: :activerecord.models.key_field
        left_side: :activerecord.models.left_side

      old_password:
        encrypted_password: Senha criptografada
        password_archivable: :activerecord.models.password_archivable
        password_archivable_type: Tipo de senha arquivável
        password_salt: Sal de senha

      active_storage/attachment:
        blob: :activerecord.models.blob
        name: Nome
        record: :activerecord.models.record
        record_type: Tipo de registro

      active_storage/blob:
        attachments: Anexos
        byte_size: Tamanho do byte
        checksum: Checksum
        content_type: Tipo de conteúdo
        filename: Nome do arquivo
        key: Chave
        metadata: Metadados
        preview_image_attachment: :activerecord.models.preview_image_attachment
        preview_image_blob: :activerecord.models.preview_image_blob
        service_name: Nome do Serviço
        variant_records: Registros de variantes

      active_storage/variant_record:
        blob: :activerecord.models.blob
        image_attachment: :activerecord.models.image_attachment
        image_blob: :activerecord.models.image_blob
        variation_digest: Resumo de variação

      bulk_destroying_content:
        business_name: Nome da empresa
        company: :activerecord.models.company
        content_destroyings: Destruição de conteúdo
        end_at: Termina em
        start_at: Começa ás
        status: Status

      content_destroying:
        bulk_destroying_content: :activerecord.models.bulk_destroying_content
        processing_errors: Erros de processamento
        status: Status

      show_on_list_field:
        field: Campo
        field_id: Campo

      notification:
        message: Mensagem
        title: Título
        user_id: Usuário
        user: Usuário

      administrator:
        password: senha

  datatables:
    answer_version:
      ip_not_obtained: 'IP NÃO OBTIDO'

  services:
    answer:
      answer_not_found: 'Resposta não encontrada.'
      cannot_set_status_to_changing: "Não é possível definir o status para: em alteração. A etapa não foi concluída."
      content_discarded: "Não é possível definir o status para: em alteração. O conteúdo foi excluído."
      review_request:
        message: '%{date} - Pedido de revisão etapa %{step} pelo usuário %{user}'
        note: 'Observação: %{note}'
      step_reviewed:
        message: '%{date} - Etapa %{step} revisada pelo usuário %{user}'
        note: 'Observação: %{note}'
