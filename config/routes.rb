# == Route Map
#
#                                             Prefix Verb     URI Pattern                                                                                       Controller#Action
#                                  letter_opener_web          /letter_opener                                                                                    LetterOpenerWeb::Engine
#                                                e2e POST     /e2e(.:format)                                                                                    e2e#update
#                                        sidekiq_web          /sidekiq                                                                                          Sidekiq::Web
#                                        healthcheck GET      /api/healthcheck(.:format)                                                                        healthchecks#show {:format=>"json"}
#                                   new_user_session GET      /api/v1/auth/sign_in(.:format)                                                                    overrides/sessions#new {:format=>"json"}
#                                       user_session POST     /api/v1/auth/sign_in(.:format)                                                                    overrides/sessions#create {:format=>"json"}
#                               destroy_user_session DELETE   /api/v1/auth/sign_out(.:format)                                                                   overrides/sessions#destroy {:format=>"json"}
#                                  new_user_password GET      /api/v1/auth/password/new(.:format)                                                               overrides/passwords#new {:format=>"json"}
#                                 edit_user_password GET      /api/v1/auth/password/edit(.:format)                                                              overrides/passwords#edit {:format=>"json"}
#                                      user_password PATCH    /api/v1/auth/password(.:format)                                                                   overrides/passwords#update {:format=>"json"}
#                                                    PUT      /api/v1/auth/password(.:format)                                                                   overrides/passwords#update {:format=>"json"}
#                                                    POST     /api/v1/auth/password(.:format)                                                                   overrides/passwords#create {:format=>"json"}
#                           cancel_user_registration GET      /api/v1/auth/cancel(.:format)                                                                     overrides/registrations#cancel {:format=>"json"}
#                              new_user_registration GET      /api/v1/auth/sign_up(.:format)                                                                    overrides/registrations#new {:format=>"json"}
#                             edit_user_registration GET      /api/v1/auth/edit(.:format)                                                                       overrides/registrations#edit {:format=>"json"}
#                                  user_registration PATCH    /api/v1/auth(.:format)                                                                            overrides/registrations#update {:format=>"json"}
#                                                    PUT      /api/v1/auth(.:format)                                                                            overrides/registrations#update {:format=>"json"}
#                                                    DELETE   /api/v1/auth(.:format)                                                                            overrides/registrations#destroy {:format=>"json"}
#                                                    POST     /api/v1/auth(.:format)                                                                            overrides/registrations#create {:format=>"json"}
#                              new_user_confirmation GET      /api/v1/auth/confirmation/new(.:format)                                                           overrides/confirmations#new {:format=>"json"}
#                                  user_confirmation GET      /api/v1/auth/confirmation(.:format)                                                               overrides/confirmations#show {:format=>"json"}
#                                                    POST     /api/v1/auth/confirmation(.:format)                                                               overrides/confirmations#create {:format=>"json"}
#                                    new_user_unlock GET      /api/v1/auth/unlock/new(.:format)                                                                 overrides/unlocks#new {:format=>"json"}
#                                        user_unlock GET      /api/v1/auth/unlock(.:format)                                                                     overrides/unlocks#show {:format=>"json"}
#                                                    POST     /api/v1/auth/unlock(.:format)                                                                     overrides/unlocks#create {:format=>"json"}
#                              user_password_expired GET      /api/v1/auth/password_expired(.:format)                                                           devise/password_expired#show {:format=>"json"}
#                                                    PATCH    /api/v1/auth/password_expired(.:format)                                                           devise/password_expired#update {:format=>"json"}
#                                                    PUT      /api/v1/auth/password_expired(.:format)                                                           devise/password_expired#update {:format=>"json"}
#                         api_v1_auth_validate_token GET      /api/v1/auth/validate_token(.:format)                                                             devise_token_auth/token_validations#validate_token
#                                api_v1_auth_failure GET      /api/v1/auth/failure(.:format)                                                                    overrides/omniauth_callbacks#omniauth_failure
#                                                    GET      /api/v1/auth/:provider/callback(.:format)                                                         overrides/omniauth_callbacks#omniauth_success
#                                                    GET|POST /omniauth/:provider/callback(.:format)                                                            overrides/omniauth_callbacks#redirect_callbacks
#                                   omniauth_failure GET|POST /omniauth/failure(.:format)                                                                       overrides/omniauth_callbacks#omniauth_failure
#                                                    GET|POST /api/v1/auth/:provider(.:format)                                                                  redirect(307)
#                          new_administrator_session GET      /api/v1/administrator_auth/sign_in(.:format)                                                      devise_token_auth/sessions#new {:format=>"json"}
#                              administrator_session POST     /api/v1/administrator_auth/sign_in(.:format)                                                      devise_token_auth/sessions#create {:format=>"json"}
#                      destroy_administrator_session DELETE   /api/v1/administrator_auth/sign_out(.:format)                                                     devise_token_auth/sessions#destroy {:format=>"json"}
#                         new_administrator_password GET      /api/v1/administrator_auth/password/new(.:format)                                                 devise_token_auth/passwords#new {:format=>"json"}
#                        edit_administrator_password GET      /api/v1/administrator_auth/password/edit(.:format)                                                devise_token_auth/passwords#edit {:format=>"json"}
#                             administrator_password PATCH    /api/v1/administrator_auth/password(.:format)                                                     devise_token_auth/passwords#update {:format=>"json"}
#                                                    PUT      /api/v1/administrator_auth/password(.:format)                                                     devise_token_auth/passwords#update {:format=>"json"}
#                                                    POST     /api/v1/administrator_auth/password(.:format)                                                     devise_token_auth/passwords#create {:format=>"json"}
#           api_v1_administrator_auth_validate_token GET      /api/v1/administrator_auth/validate_token(.:format)                                               devise_token_auth/token_validations#validate_token
#                  api_v1_administrator_auth_failure GET      /api/v1/administrator_auth/failure(.:format)                                                      overrides/omniauth_callbacks#omniauth_failure
#                                                    GET      /api/v1/administrator_auth/:provider/callback(.:format)                                           overrides/omniauth_callbacks#omniauth_success
#                                                    GET|POST /omniauth/:provider/callback(.:format)                                                            overrides/omniauth_callbacks#redirect_callbacks
#                                                    GET|POST /omniauth/failure(.:format)                                                                       overrides/omniauth_callbacks#omniauth_failure
#                                                    GET|POST /api/v1/administrator_auth/:provider(.:format)                                                    redirect(307)
#                                          dashboard GET      /api/v1/dashboard(.:format)                                                                       dashboard#index {:format=>"json"}
#                        dashboard_duplicated_fields GET      /api/v1/dashboard/duplicated_fields(.:format)                                                     dashboard#duplicated_fields {:format=>"json"}
#                                             report GET      /api/v1/report(.:format)                                                                          dashboard#report {:format=>"json"}
#                               summary_auditorships GET      /api/v1/auditorships/summary(.:format)                                                            auditorships#summary {:format=>"json"}
#                                       auditorships GET      /api/v1/auditorships(.:format)                                                                    auditorships#index {:format=>"json"}
#                         welcome_video_watched_user PUT      /api/v1/users/:id/welcome_video_watched(.:format)                                                 users#welcome_video_watched {:format=>"json"}
#                                          lock_user PUT      /api/v1/users/:id/lock(.:format)                                                                  users#lock {:format=>"json"}
#                                        unlock_user PUT      /api/v1/users/:id/unlock(.:format)                                                                users#unlock {:format=>"json"}
#                                notifications_users GET      /api/v1/users/notifications(.:format)                                                             users#notifications {:format=>"json"}
#                                  top_answers_users GET      /api/v1/users/top_answers(.:format)                                                               users#top_answers {:format=>"json"}
#                        token_to_confirm_step_users POST     /api/v1/users/token_to_confirm_step(.:format)                                                     users#token_to_confirm_step {:format=>"json"}
#                                      activate_user PATCH    /api/v1/users/:id/activate(.:format)                                                              users#activate {:format=>"json"}
#                                              users GET      /api/v1/users(.:format)                                                                           users#index {:format=>"json"}
#                                                    POST     /api/v1/users(.:format)                                                                           users#create {:format=>"json"}
#                                               user GET      /api/v1/users/:id(.:format)                                                                       users#show {:format=>"json"}
#                                                    PATCH    /api/v1/users/:id(.:format)                                                                       users#update {:format=>"json"}
#                                                    PUT      /api/v1/users/:id(.:format)                                                                       users#update {:format=>"json"}
#                                                    DELETE   /api/v1/users/:id(.:format)                                                                       users#destroy {:format=>"json"}
#                             activate_administrator PATCH    /api/v1/administrators/:id/activate(.:format)                                                     administrators#activate {:format=>"json"}
#                                     administrators GET      /api/v1/administrators(.:format)                                                                  administrators#index {:format=>"json"}
#                                                    POST     /api/v1/administrators(.:format)                                                                  administrators#create {:format=>"json"}
#                                      administrator GET      /api/v1/administrators/:id(.:format)                                                              administrators#show {:format=>"json"}
#                                                    PATCH    /api/v1/administrators/:id(.:format)                                                              administrators#update {:format=>"json"}
#                                                    PUT      /api/v1/administrators/:id(.:format)                                                              administrators#update {:format=>"json"}
#                                                    DELETE   /api/v1/administrators/:id(.:format)                                                              administrators#destroy {:format=>"json"}
#                            activate_business_group PATCH    /api/v1/business_groups/:id/activate(.:format)                                                    business_groups#activate {:format=>"json"}
#                                    business_groups GET      /api/v1/business_groups(.:format)                                                                 business_groups#index {:format=>"json"}
#                                                    POST     /api/v1/business_groups(.:format)                                                                 business_groups#create {:format=>"json"}
#                                     business_group GET      /api/v1/business_groups/:id(.:format)                                                             business_groups#show {:format=>"json"}
#                                                    PATCH    /api/v1/business_groups/:id(.:format)                                                             business_groups#update {:format=>"json"}
#                                                    PUT      /api/v1/business_groups/:id(.:format)                                                             business_groups#update {:format=>"json"}
#                                                    DELETE   /api/v1/business_groups/:id(.:format)                                                             business_groups#destroy {:format=>"json"}
#                                search_search_index GET      /api/v1/search/search(.:format)                                                                   search#search {:format=>"json"}
#                           build_query_search_index POST     /api/v1/search/build_query(.:format)                                                              search#build_query {:format=>"json"}
#                                       search_index GET      /api/v1/search(.:format)                                                                          search#index {:format=>"json"}
#                                                    POST     /api/v1/search(.:format)                                                                          search#create {:format=>"json"}
#                                             search GET      /api/v1/search/:id(.:format)                                                                      search#show {:format=>"json"}
#                                                    PATCH    /api/v1/search/:id(.:format)                                                                      search#update {:format=>"json"}
#                                                    PUT      /api/v1/search/:id(.:format)                                                                      search#update {:format=>"json"}
#                                                    DELETE   /api/v1/search/:id(.:format)                                                                      search#destroy {:format=>"json"}
#                              dependent_field_rules GET      /api/v1/dependent_field_rules(.:format)                                                           dependent_field_rules#index {:format=>"json"}
#                                                    POST     /api/v1/dependent_field_rules(.:format)                                                           dependent_field_rules#create {:format=>"json"}
#                               dependent_field_rule GET      /api/v1/dependent_field_rules/:id(.:format)                                                       dependent_field_rules#show {:format=>"json"}
#                                                    PATCH    /api/v1/dependent_field_rules/:id(.:format)                                                       dependent_field_rules#update {:format=>"json"}
#                                                    PUT      /api/v1/dependent_field_rules/:id(.:format)                                                       dependent_field_rules#update {:format=>"json"}
#                                                    DELETE   /api/v1/dependent_field_rules/:id(.:format)                                                       dependent_field_rules#destroy {:format=>"json"}
#                             validation_field_rules GET      /api/v1/validation_field_rules(.:format)                                                          validation_field_rules#index {:format=>"json"}
#                                                    POST     /api/v1/validation_field_rules(.:format)                                                          validation_field_rules#create {:format=>"json"}
#                              validation_field_rule GET      /api/v1/validation_field_rules/:id(.:format)                                                      validation_field_rules#show {:format=>"json"}
#                                                    PATCH    /api/v1/validation_field_rules/:id(.:format)                                                      validation_field_rules#update {:format=>"json"}
#                                                    PUT      /api/v1/validation_field_rules/:id(.:format)                                                      validation_field_rules#update {:format=>"json"}
#                                                    DELETE   /api/v1/validation_field_rules/:id(.:format)                                                      validation_field_rules#destroy {:format=>"json"}
#                     field_stats_data_profile_index GET      /api/v1/data_profile/field_stats(.:format)                                                        data_profile#field_stats {:format=>"json"}
#                      similarity_data_profile_index GET      /api/v1/data_profile/similarity(.:format)                                                         data_profile#similarity {:format=>"json"}
#                                 data_profile_index GET      /api/v1/data_profile(.:format)                                                                    data_profile#index {:format=>"json"}
#                                                    GET      /api/v1/companies/:subdomain/detail(.:format)                                                     companies#detail {:format=>"json"}
#                                                    GET      /api/v1/companies/:subdomain/current(.:format)                                                    companies#current {:format=>"json"}
#                                                    GET      /api/v1/companies/:subdomain/get_name_by_subdomain(.:format)                                      companies#get_name_by_subdomain {:format=>"json"}
#                             update_theme_companies PATCH    /api/v1/companies/update_theme(.:format)                                                          companies#update_theme {:format=>"json"}
#                       update_allowed_ips_companies PATCH    /api/v1/companies/update_allowed_ips(.:format)                                                    companies#update_allowed_ips {:format=>"json"}
#                    update_token_lifespan_companies PATCH    /api/v1/companies/update_token_lifespan(.:format)                                                 companies#update_token_lifespan {:format=>"json"}
#                  update_data_replacement_companies PATCH    /api/v1/companies/update_data_replacement(.:format)                                               companies#update_data_replacement {:format=>"json"}
#                          business_groups_companies GET      /api/v1/companies/business_groups(.:format)                                                       companies#business_groups {:format=>"json"}
#                            find_business_companies GET      /api/v1/companies/find_business(.:format)                                                         companies#find_business {:format=>"json"}
#                          content_columns_companies GET      /api/v1/companies/content_columns(.:format)                                                       companies#content_columns {:format=>"json"}
#                        content_datatable_companies GET      /api/v1/companies/content_datatable(.:format)                                                     companies#content_datatable {:format=>"datatable"}
#                 update_elasticsearch_index_company PUT      /api/v1/companies/:id/update_elasticsearch_index(.:format)                                        companies#update_elasticsearch_index {:format=>"json"}
#                                          companies GET      /api/v1/companies(.:format)                                                                       companies#index {:format=>"json"}
#                                                    POST     /api/v1/companies(.:format)                                                                       companies#create {:format=>"json"}
#                                            company GET      /api/v1/companies/:id(.:format)                                                                   companies#show {:format=>"json"}
#                                                    PATCH    /api/v1/companies/:id(.:format)                                                                   companies#update {:format=>"json"}
#                                                    PUT      /api/v1/companies/:id(.:format)                                                                   companies#update {:format=>"json"}
#                                                    DELETE   /api/v1/companies/:id(.:format)                                                                   companies#destroy {:format=>"json"}
#                                verify_token_answer GET      /api/v1/answers/:id/verify_token(.:format)                                                        answers#verify_token {:format=>"json"}
#                                    fields_business GET      /api/v1/businesses/:id/fields(.:format)                                                           businesses#fields {:format=>"json"}
#           bulk_create_business_show_on_list_fields POST     /api/v1/businesses/:business_id/show_on_list_fields/bulk_create(.:format)                         show_on_list_fields#bulk_create {:format=>"json"}
#                       business_show_on_list_fields GET      /api/v1/businesses/:business_id/show_on_list_fields(.:format)                                     show_on_list_fields#index {:format=>"json"}
#                                                    POST     /api/v1/businesses/:business_id/show_on_list_fields(.:format)                                     show_on_list_fields#create {:format=>"json"}
#                        business_show_on_list_field PATCH    /api/v1/businesses/:business_id/show_on_list_fields/:id(.:format)                                 show_on_list_fields#update {:format=>"json"}
#                                                    PUT      /api/v1/businesses/:business_id/show_on_list_fields/:id(.:format)                                 show_on_list_fields#update {:format=>"json"}
#                                                    DELETE   /api/v1/businesses/:business_id/show_on_list_fields/:id(.:format)                                 show_on_list_fields#destroy {:format=>"json"}
#                             activate_business_step PATCH    /api/v1/businesses/:business_id/steps/:id/activate(.:format)                                      steps#activate {:format=>"json"}
#                                     business_steps GET      /api/v1/businesses/:business_id/steps(.:format)                                                   steps#index {:format=>"json"}
#                                                    POST     /api/v1/businesses/:business_id/steps(.:format)                                                   steps#create {:format=>"json"}
#                                      business_step GET      /api/v1/businesses/:business_id/steps/:id(.:format)                                               steps#show {:format=>"json"}
#                                                    PATCH    /api/v1/businesses/:business_id/steps/:id(.:format)                                               steps#update {:format=>"json"}
#                                                    PUT      /api/v1/businesses/:business_id/steps/:id(.:format)                                               steps#update {:format=>"json"}
#                                                    DELETE   /api/v1/businesses/:business_id/steps/:id(.:format)                                               steps#destroy {:format=>"json"}
#                                  business_contents POST     /api/v1/businesses/:business_id/contents(.:format)                                                contents#create {:format=>"json"}
#                                                    POST     /api/v1/businesses/:business_id/contents(.:format)                                                contents#index {:format=>"datatable"}
#                                                    GET      /api/v1/businesses/:business_id/contents(.:format)                                                contents#index {:format=>"json"}
#                                  activate_business PATCH    /api/v1/businesses/:id/activate(.:format)                                                         businesses#activate {:format=>"json"}
#                                         businesses GET      /api/v1/businesses(.:format)                                                                      businesses#index {:format=>"json"}
#                                                    POST     /api/v1/businesses(.:format)                                                                      businesses#create {:format=>"json"}
#                                           business GET      /api/v1/businesses/:id(.:format)                                                                  businesses#show {:format=>"json"}
#                                                    PATCH    /api/v1/businesses/:id(.:format)                                                                  businesses#update {:format=>"json"}
#                                                    PUT      /api/v1/businesses/:id(.:format)                                                                  businesses#update {:format=>"json"}
#                                                    DELETE   /api/v1/businesses/:id(.:format)                                                                  businesses#destroy {:format=>"json"}
#                            activate_template_field PATCH    /api/v1/templates/:template_id/fields/:id/activate(.:format)                                      fields#activate {:format=>"json"}
#                                    template_fields GET      /api/v1/templates/:template_id/fields(.:format)                                                   fields#index {:format=>"json"}
#                                                    POST     /api/v1/templates/:template_id/fields(.:format)                                                   fields#create {:format=>"json"}
#                                     template_field GET      /api/v1/templates/:template_id/fields/:id(.:format)                                               fields#show {:format=>"json"}
#                                                    PATCH    /api/v1/templates/:template_id/fields/:id(.:format)                                               fields#update {:format=>"json"}
#                                                    PUT      /api/v1/templates/:template_id/fields/:id(.:format)                                               fields#update {:format=>"json"}
#                                                    DELETE   /api/v1/templates/:template_id/fields/:id(.:format)                                               fields#destroy {:format=>"json"}
#                                  activate_template PATCH    /api/v1/templates/:id/activate(.:format)                                                          templates#activate {:format=>"json"}
#                                          templates GET      /api/v1/templates(.:format)                                                                       templates#index {:format=>"json"}
#                                                    POST     /api/v1/templates(.:format)                                                                       templates#create {:format=>"json"}
#                                           template GET      /api/v1/templates/:id(.:format)                                                                   templates#show {:format=>"json"}
#                                                    PATCH    /api/v1/templates/:id(.:format)                                                                   templates#update {:format=>"json"}
#                                                    PUT      /api/v1/templates/:id(.:format)                                                                   templates#update {:format=>"json"}
#                                                    DELETE   /api/v1/templates/:id(.:format)                                                                   templates#destroy {:format=>"json"}
#                            available_options_field GET      /api/v1/fields/:id/available_options(.:format)                                                    fields#available_options {:format=>"json"}
#                                     activate_field PATCH    /api/v1/fields/:id/activate(.:format)                                                             fields#activate {:format=>"json"}
#                                   add_option_field POST     /api/v1/fields/:id/add_option(.:format)                                                           fields#add_option {:format=>"json"}
#                      pk_fields_for_business_fields GET      /api/v1/fields/pk_fields_for_business(.:format)                                                   fields#pk_fields_for_business {:format=>"json"}
#                            field_field_validations GET      /api/v1/fields/:field_id/field_validations(.:format)                                              field_validations#index {:format=>"json"}
#                                                    POST     /api/v1/fields/:field_id/field_validations(.:format)                                              field_validations#create {:format=>"json"}
#                                   field_validation DELETE   /api/v1/field_validations/:id(.:format)                                                           field_validations#destroy {:format=>"json"}
#                                             fields GET      /api/v1/fields(.:format)                                                                          fields#index {:format=>"json"}
#                                                    POST     /api/v1/fields(.:format)                                                                          fields#create {:format=>"json"}
#                                              field GET      /api/v1/fields/:id(.:format)                                                                      fields#show {:format=>"json"}
#                                                    PATCH    /api/v1/fields/:id(.:format)                                                                      fields#update {:format=>"json"}
#                                                    PUT      /api/v1/fields/:id(.:format)                                                                      fields#update {:format=>"json"}
#                                                    DELETE   /api/v1/fields/:id(.:format)                                                                      fields#destroy {:format=>"json"}
#                                              menus GET      /api/v1/menus(.:format)                                                                           menus#index {:format=>"json"}
#                          migrate_data_translations POST     /api/v1/translations/migrate_data(.:format)                                                       translations#migrate_data {:format=>"json"}
#                                       translations GET      /api/v1/translations(.:format)                                                                    translations#index {:format=>"json"}
#                                                    POST     /api/v1/translations(.:format)                                                                    translations#create {:format=>"json"}
#                                        translation DELETE   /api/v1/translations/:id(.:format)                                                                translations#destroy {:format=>"json"}
#                                order_step_template PATCH    /api/v1/step_templates/:id/order(.:format)                                                        step_templates#order {:format=>"json"}
#                                     step_templates POST     /api/v1/step_templates(.:format)                                                                  step_templates#create {:format=>"json"}
#                                      step_template DELETE   /api/v1/step_templates/:id(.:format)                                                              step_templates#destroy {:format=>"json"}
#                  for_current_user_step_permissions GET      /api/v1/step_permissions/for_current_user(.:format)                                               step_permissions#for_current_user {:format=>"json"}
#                                   step_permissions GET      /api/v1/step_permissions(.:format)                                                                step_permissions#index {:format=>"json"}
#                                                    POST     /api/v1/step_permissions(.:format)                                                                step_permissions#create {:format=>"json"}
#                                    step_permission DELETE   /api/v1/step_permissions/:id(.:format)                                                            step_permissions#destroy {:format=>"json"}
#                                        departments GET      /api/v1/departments(.:format)                                                                     departments#index {:format=>"json"}
#                                                    POST     /api/v1/departments(.:format)                                                                     departments#create {:format=>"json"}
#                                         department GET      /api/v1/departments/:id(.:format)                                                                 departments#show {:format=>"json"}
#                                                    PATCH    /api/v1/departments/:id(.:format)                                                                 departments#update {:format=>"json"}
#                                                    PUT      /api/v1/departments/:id(.:format)                                                                 departments#update {:format=>"json"}
#                                   troubleshootings GET      /api/v1/troubleshootings(.:format)                                                                troubleshootings#index {:format=>"json"}
#                         dependent_reference_fields GET      /api/v1/dependent_reference_fields(.:format)                                                      dependent_reference_fields#index {:format=>"json"}
#                                                    POST     /api/v1/dependent_reference_fields(.:format)                                                      dependent_reference_fields#create {:format=>"json"}
#                          dependent_reference_field GET      /api/v1/dependent_reference_fields/:id(.:format)                                                  dependent_reference_fields#show {:format=>"json"}
#                                                    PATCH    /api/v1/dependent_reference_fields/:id(.:format)                                                  dependent_reference_fields#update {:format=>"json"}
#                                                    PUT      /api/v1/dependent_reference_fields/:id(.:format)                                                  dependent_reference_fields#update {:format=>"json"}
#                                                    DELETE   /api/v1/dependent_reference_fields/:id(.:format)                                                  dependent_reference_fields#destroy {:format=>"json"}
#                                    answer_versions GET      /api/v1/answer_versions(.:format)                                                                 answer_versions#index {:format=>"json"}
#                bulk_alteration_bulk_saving_answers POST     /api/v1/bulk_saving_answers/bulk_alteration(.:format)                                             bulk_saving_answers#bulk_alteration {:format=>"json"}
#        bulk_alteration_preview_bulk_saving_answers POST     /api/v1/bulk_saving_answers/bulk_alteration_preview(.:format)                                     bulk_saving_answers#bulk_alteration_preview {:format=>"json"}
#                                bulk_saving_answers POST     /api/v1/bulk_saving_answers(.:format)                                                             bulk_saving_answers#create {:format=>"json"}
#                                                    POST     /api/v1/bulk_saving_answers(.:format)                                                             bulk_saving_answers#index {:format=>"datatable"}
#                           fill_bulk_saving_answers POST     /api/v1/bulk_saving_answers/fill(.:format)                                                        bulk_saving_answers#fill {:format=>"json"}
#                 process_orphans_bulk_saving_answer PUT      /api/v1/bulk_saving_answers/:id/process_orphans(.:format)                                         bulk_saving_answers#process_orphans {:format=>"json"}
#                                                    GET      /api/v1/bulk_saving_answers(.:format)                                                             bulk_saving_answers#index {:format=>"json"}
#                                 bulk_saving_answer GET      /api/v1/bulk_saving_answers/:id(.:format)                                                         bulk_saving_answers#show {:format=>"json"}
#                                                    PATCH    /api/v1/bulk_saving_answers/:id(.:format)                                                         bulk_saving_answers#update {:format=>"json"}
#                                                    PUT      /api/v1/bulk_saving_answers/:id(.:format)                                                         bulk_saving_answers#update {:format=>"json"}
#      destroy_all_contents_bulk_destroying_contents DELETE   /api/v1/bulk_destroying_contents/destroy_all_contents(.:format)                                   bulk_destroying_contents#destroy_all_contents {:format=>"json"}
# destroy_selected_contents_bulk_destroying_contents DELETE   /api/v1/bulk_destroying_contents/destroy_selected_contents(.:format)                              bulk_destroying_contents#destroy_selected_contents {:format=>"json"}
#            process_orphans_bulk_destroying_content PUT      /api/v1/bulk_destroying_contents/:id/process_orphans(.:format)                                    bulk_destroying_contents#process_orphans {:format=>"json"}
#                           bulk_destroying_contents GET      /api/v1/bulk_destroying_contents(.:format)                                                        bulk_destroying_contents#index {:format=>"json"}
#                                                    POST     /api/v1/bulk_destroying_contents(.:format)                                                        bulk_destroying_contents#create {:format=>"json"}
#                            bulk_destroying_content GET      /api/v1/bulk_destroying_contents/:id(.:format)                                                    bulk_destroying_contents#show {:format=>"json"}
#                                                    PATCH    /api/v1/bulk_destroying_contents/:id(.:format)                                                    bulk_destroying_contents#update {:format=>"json"}
#                                                    PUT      /api/v1/bulk_destroying_contents/:id(.:format)                                                    bulk_destroying_contents#update {:format=>"json"}
#                                  as_menu_favorites GET      /api/v1/favorites/as_menu(.:format)                                                               favorites#as_menu {:format=>"json"}
#                                          favorites GET      /api/v1/favorites(.:format)                                                                       favorites#index {:format=>"json"}
#                                                    POST     /api/v1/favorites(.:format)                                                                       favorites#create {:format=>"json"}
#                                           favorite DELETE   /api/v1/favorites/:id(.:format)                                                                   favorites#destroy {:format=>"json"}
#                                       style_themes GET      /api/v1/themes/style(.:format)                                                                    themes#style {:format=>"json"}
#                                             themes GET      /api/v1/themes(.:format)                                                                          themes#index {:format=>"json"}
#                  field_with_value_business_headers GET      /api/v1/business_headers/field_with_value(.:format)                                               business_headers#field_with_value {:format=>"json"}
#                                   business_headers GET      /api/v1/business_headers(.:format)                                                                business_headers#index {:format=>"json"}
#                                                    POST     /api/v1/business_headers(.:format)                                                                business_headers#create {:format=>"json"}
#                                    business_header DELETE   /api/v1/business_headers/:id(.:format)                                                            business_headers#destroy {:format=>"json"}
#                        show_on_list_values_content GET      /api/v1/contents/:id/show_on_list_values(.:format)                                                contents#show_on_list_values {:format=>"json"}
#                        show_on_form_values_content GET      /api/v1/contents/:id/show_on_form_values(.:format)                                                contents#show_on_form_values {:format=>"json"}
#                         show_modifications_content GET      /api/v1/contents/:id/show_modifications(.:format)                                                 contents#show_modifications {:format=>"json"}
#                                       note_content GET      /api/v1/contents/:id/note(.:format)                                                               contents#note {:format=>"json"}
#                           reference_detail_content GET      /api/v1/contents/:id/reference_detail(.:format)                                                   contents#reference_detail {:format=>"json"}
#                                    restore_content PATCH    /api/v1/contents/:id/restore(.:format)                                                            contents#restore {:format=>"json"}
#                                 all_values_content GET      /api/v1/contents/:id/all_values(.:format)                                                         contents#all_values {:format=>"json"}
#                             summary_values_content GET      /api/v1/contents/:id/summary_values(.:format)                                                     contents#summary_values {:format=>"json"}
#                           authorize_content_answer PATCH    /api/v1/contents/:content_id/answers/:id/authorize(.:format)                                      answers#authorize {:format=>"json"}
#                              reject_content_answer PATCH    /api/v1/contents/:content_id/answers/:id/reject(.:format)                                         answers#reject {:format=>"json"}
#                            revision_content_answer PATCH    /api/v1/contents/:content_id/answers/:id/revision(.:format)                                       answers#revision {:format=>"json"}
#                            validate_content_answer POST     /api/v1/contents/:content_id/answers/:id/validate(.:format)                                       answers#validate {:format=>"json"}
#          validate_dynamic_dependent_content_answer POST     /api/v1/contents/:content_id/answers/:id/validate_dynamic_dependent(.:format)                     answers#validate_dynamic_dependent {:format=>"json"}
#                                     content_answer GET      /api/v1/contents/:content_id/answers/:id(.:format)                                                answers#show {:format=>"json"}
#                                                    PATCH    /api/v1/contents/:content_id/answers/:id(.:format)                                                answers#update {:format=>"json"}
#                                                    PUT      /api/v1/contents/:content_id/answers/:id(.:format)                                                answers#update {:format=>"json"}
#                                           contents POST     /api/v1/contents(.:format)                                                                        contents#create {:format=>"json"}
#                                            content GET      /api/v1/contents/:id(.:format)                                                                    contents#show {:format=>"json"}
#                                                    PATCH    /api/v1/contents/:id(.:format)                                                                    contents#update {:format=>"json"}
#                                                    PUT      /api/v1/contents/:id(.:format)                                                                    contents#update {:format=>"json"}
#                                                    DELETE   /api/v1/contents/:id(.:format)                                                                    contents#destroy {:format=>"json"}
#                                  read_notification POST     /api/v1/notifications/:id/read(.:format)                                                          notifications#read {:format=>"json"}
#                                      notifications GET      /api/v1/notifications(.:format)                                                                   notifications#index {:format=>"json"}
#                                         login_chat POST     /api/v1/chat/login(.:format)                                                                      chats#login {:format=>"json"}
#                                         users_chat GET      /api/v1/chat/users(.:format)                                                                      chats#users {:format=>"json"}
#                                         statistics GET      /api/v1/statistics(.:format)                                                                      statistics#index {:format=>"json"}
#                                                    POST     /api/v1/statistics(.:format)                                                                      statistics#create {:format=>"json"}
#                                          statistic GET      /api/v1/statistics/:id(.:format)                                                                  statistics#show {:format=>"json"}
#                                                    PATCH    /api/v1/statistics/:id(.:format)                                                                  statistics#update {:format=>"json"}
#                                                    PUT      /api/v1/statistics/:id(.:format)                                                                  statistics#update {:format=>"json"}
#                                                    DELETE   /api/v1/statistics/:id(.:format)                                                                  statistics#destroy {:format=>"json"}
#                                            widgets POST     /api/v1/widgets(.:format)                                                                         widgets#create {:format=>"json"}
#                                             widget GET      /api/v1/widgets/:id(.:format)                                                                     widgets#show {:format=>"json"}
#                                                    PATCH    /api/v1/widgets/:id(.:format)                                                                     widgets#update {:format=>"json"}
#                                                    PUT      /api/v1/widgets/:id(.:format)                                                                     widgets#update {:format=>"json"}
#                                                    DELETE   /api/v1/widgets/:id(.:format)                                                                     widgets#destroy {:format=>"json"}
#                                           contacts GET      /api/v1/contacts(.:format)                                                                        contacts#index {:format=>"json"}
#                                                    POST     /api/v1/contacts(.:format)                                                                        contacts#create {:format=>"json"}
#                                            contact GET      /api/v1/contacts/:id(.:format)                                                                    contacts#show {:format=>"json"}
#                                                    PATCH    /api/v1/contacts/:id(.:format)                                                                    contacts#update {:format=>"json"}
#                                                    PUT      /api/v1/contacts/:id(.:format)                                                                    contacts#update {:format=>"json"}
#                                                    DELETE   /api/v1/contacts/:id(.:format)                                                                    contacts#destroy {:format=>"json"}
#                                                    GET      /external/contents(.:format)                                                                      external/contents#index {:format=>"xml"}
#                                       sub_contents GET      /external/sub_contents(.:format)                                                                  external/sub_contents#index {:format=>"xml"}
#                                                    POST     /external/businesses(.:format)                                                                    external/businesses#create {:format=>"xml"}
#                                                    PATCH    /external/businesses/:id(.:format)                                                                external/businesses#update {:format=>"xml"}
#                                                    PUT      /external/businesses/:id(.:format)                                                                external/businesses#update {:format=>"xml"}
#                                                    DELETE   /external/businesses/:id(.:format)                                                                external/businesses#destroy {:format=>"xml"}
#                                     sub_businesses POST     /external/sub_businesses(.:format)                                                                external/sub_businesses#create {:format=>"xml"}
#                                       sub_business PATCH    /external/sub_businesses/:id(.:format)                                                            external/sub_businesses#update {:format=>"xml"}
#                                                    PUT      /external/sub_businesses/:id(.:format)                                                            external/sub_businesses#update {:format=>"xml"}
#                                                    DELETE   /external/sub_businesses/:id(.:format)                                                            external/sub_businesses#destroy {:format=>"xml"}
#                                                    POST     /external/users(.:format)                                                                         external/users#create {:format=>"xml"}
#                                                    PATCH    /external/users/:id(.:format)                                                                     external/users#update {:format=>"xml"}
#                                                    PUT      /external/users/:id(.:format)                                                                     external/users#update {:format=>"xml"}
#                                                    DELETE   /external/users/:id(.:format)                                                                     external/users#destroy {:format=>"xml"}
#                                                    POST     /external/administrators(.:format)                                                                external/administrators#create {:format=>"xml"}
#                                                    DELETE   /external/administrators/:id(.:format)                                                            external/administrators#destroy {:format=>"xml"}
#                                                    POST     /external/notifications(.:format)                                                                 external/notifications#create {:format=>/json/}
#                                       notification GET      /external/notifications/:id(.:format)                                                             external/notifications#show {:format=>/json/}
#                                                    PATCH    /external/notifications/:id(.:format)                                                             external/notifications#update {:format=>/json/}
#                                                    PUT      /external/notifications/:id(.:format)                                                             external/notifications#update {:format=>/json/}
#                                                    DELETE   /external/notifications/:id(.:format)                                                             external/notifications#destroy {:format=>/json/}
#                                   authorize_answer PATCH    /external/answers/:id/authorize(.:format)                                                         external/answers#authorize {:format=>"xml"}
#                                      reject_answer PATCH    /external/answers/:id/reject(.:format)                                                            external/answers#reject {:format=>"xml"}
#                                 rails_service_blob GET      /rails/active_storage/blobs/redirect/:signed_id/*filename(.:format)                               active_storage/blobs/redirect#show
#                           rails_service_blob_proxy GET      /rails/active_storage/blobs/proxy/:signed_id/*filename(.:format)                                  active_storage/blobs/proxy#show
#                                                    GET      /rails/active_storage/blobs/:signed_id/*filename(.:format)                                        active_storage/blobs/redirect#show
#                          rails_blob_representation GET      /rails/active_storage/representations/redirect/:signed_blob_id/:variation_key/*filename(.:format) active_storage/representations/redirect#show
#                    rails_blob_representation_proxy GET      /rails/active_storage/representations/proxy/:signed_blob_id/:variation_key/*filename(.:format)    active_storage/representations/proxy#show
#                                                    GET      /rails/active_storage/representations/:signed_blob_id/:variation_key/*filename(.:format)          active_storage/representations/redirect#show
#                                 rails_disk_service GET      /rails/active_storage/disk/:encoded_key/*filename(.:format)                                       active_storage/disk#show
#                          update_rails_disk_service PUT      /rails/active_storage/disk/:encoded_token(.:format)                                               active_storage/disk#update
#                               rails_direct_uploads POST     /rails/active_storage/direct_uploads(.:format)                                                    active_storage/direct_uploads#create
#
# Routes for LetterOpenerWeb::Engine:
# clear_letters DELETE /clear(.:format)                 letter_opener_web/letters#clear
# delete_letter DELETE /:id(.:format)                   letter_opener_web/letters#destroy
#       letters GET    /                                letter_opener_web/letters#index
#        letter GET    /:id(/:style)(.:format)          letter_opener_web/letters#show
#               GET    /:id/attachments/:file(.:format) letter_opener_web/letters#attachment

require 'sidekiq_unique_jobs/web'
require 'sidekiq/bulk'
require 'sidekiq-scheduler/web'

Rails.application.routes.draw do
  concern :activate do
    member do
      patch :activate
    end
  end

  mount LetterOpenerWeb::Engine, at: '/letter_opener' if Rails.env.development?

  if Rails.env.production? || Rails.env.staging? || Rails.env.academy? || Rails.env.uat?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(username), ::Digest::SHA256.hexdigest('4mdg-admin')) &
        ActiveSupport::SecurityUtils.secure_compare(::Digest::SHA256.hexdigest(password), ::Digest::SHA256.hexdigest('#EXB3864~6Vw!>'))
    end
  end

  mount Sidekiq::Web => '/sidekiq'

  scope :api, defaults: { format: 'json' } do
    resource :healthcheck, only: :show do
      get '/database', to: 'healthchecks#database_check'
    end

    get '/send_issue_to_sentry', to: 'healthchecks#send_issue_to_sentry'

    scope :v1 do
      mount_devise_token_auth_for 'User', at: 'auth', controllers: {
        confirmations: 'overrides/confirmations',
        sessions: 'overrides/sessions',
        passwords: 'overrides/passwords',
        omniauth_callbacks: 'overrides/omniauth_callbacks',
        registrations: 'overrides/registrations',
        unlocks: 'overrides/unlocks'
      }

      mount_devise_token_auth_for 'Administrator', at: 'administrator_auth', skip: %i[registrations], controllers: {
        omniauth_callbacks: 'overrides/omniauth_callbacks'
      }

      get :dashboard, to: 'dashboard#index'
      get 'dashboard/duplicated_fields', to: 'dashboard#duplicated_fields'
      get :report, to: 'dashboard#report'

      resources :auditorships, only: %i[index] do
        get :summary, on: :collection
      end

      resources :users, concerns: :activate do
        member do
          put :welcome_video_watched
          put :lock
          put :unlock
        end

        collection do
          get :notifications
          get :top_answers
          post :token_to_confirm_step
        end
      end

      resources :administrators, concerns: :activate
      resources :business_groups, concerns: :activate
      resources :search do
        collection do
          get :search
          post :build_query
        end
      end
      resources :dependent_field_rules
      resources :validation_field_rules

      resources :data_profile, only: :index do
        collection do
          get '/field_stats', to: 'data_profile#field_stats'
          get '/similarity', to: 'data_profile#similarity'
        end
      end

      resources :companies, only: %i[index create update show destroy] do
        collection do
          get ':subdomain/detail', to: 'companies#detail' # public endpoint
          get ':subdomain/current', to: 'companies#current' # requires credentials
          get ':subdomain/get_name_by_subdomain', to: 'companies#get_name_by_subdomain' # requires credentials

          patch :update_theme
          patch :update_allowed_ips
          patch :update_token_lifespan
          patch :update_data_replacement

          get :business_groups
          get :find_business
          get :content_columns
          get :content_datatable, constraints: { format: 'datatable' }
        end

        member do
          put :update_elasticsearch_index
          delete :remove_attachment
        end
      end

      resources :answers, only: %i[verify_token] do
        member do
          get :verify_token
        end
      end

      resources :businesses, concerns: :activate do
        member do
          get :fields
          get :export_all_steps_models
        end

        resources :show_on_list_fields, only: %i[index create update destroy] do
          collection do
            post :bulk_create
          end
        end
        resources :steps, concerns: :activate

        resources :contents, only: %i[index] do
          collection do
            post '/', constraints: { format: 'json' }, to: 'contents#create'
            post '/', constraints: { format: 'datatable' }, to: 'contents#index'
          end
        end
      end

      resources :templates, concerns: :activate do
        resources :fields, concerns: :activate
      end

      resources :fields do
        member do
          get :available_options
          patch :activate
          post :add_option
        end

        collection do
          get :pk_fields_for_business
        end

        resources :field_validations, shallow: true, only: %i[index create destroy]
      end

      resources :menus, only: :index

      resources :translations, only: %i[index create destroy] do
        collection do
          post :migrate_data
        end
      end

      resources :step_templates, only: %i[create destroy] do
        member do
          patch :order
        end
      end

      resources :step_permissions, only: %i[create destroy index] do
        collection do
          get :for_current_user
        end
      end

      resources :departments, only: %i[create index show update]
      resources :troubleshootings, only: :index
      resources :dependent_reference_fields
      resources :answer_versions, only: :index

      resources :bulk_saving_answers, only: %i[index show update] do
        collection do
          post :bulk_alteration
          post :bulk_alteration_preview
          post '/', constraints: { format: 'json' }, to: 'bulk_saving_answers#create'
          post '/', constraints: { format: 'datatable' }, to: 'bulk_saving_answers#index'
          post :fill
        end

        member do
          put :process_orphans
        end
      end

      resources :bulk_destroying_contents, only: %i[create index show update] do
        collection do
          delete :destroy_all_contents
          delete :destroy_selected_contents
        end

        member do
          put :process_orphans
        end
      end

      resources :favorites, only: %i[create destroy index] do
        collection do
          get :as_menu
        end
      end

      resources :themes, only: :index do
        collection do
          get :style
        end
      end

      resources :business_headers, only: %i[create destroy index] do
        collection do
          get :field_with_value
        end
      end

      resources :contents, only: %i[show destroy update create] do
        member do
          get :show_on_list_values
          get :show_on_form_values
          get :show_modifications
          get :note
          get :reference_detail

          patch :restore
          get :all_values
          get :summary_values
        end

        resources :answers, only: %i[update] do
          member do
            patch :authorize
            patch :reject
            patch :revision

            post :show, to: 'answers#show'
            post :validate
            post :validate_dynamic_dependent
          end
        end
      end

      resources :notifications, only: %i[index] do
        member do
          post 'read'
        end
      end

      resource :chat, only: %i[] do
        collection do
          post :login
          get :users
        end
      end

      resources :statistics
      resources :widgets, only: %i[show create update destroy]
      resources :contacts, only: %i[create index show update destroy]
    end
  end

  scope :external, defaults: { format: 'json' } do
    scope :v2 do
      resources :administrators, only: %i[index show create update], controller: 'external/v2/administrators'
      resources :users, only: %i[create destroy index show], controller: 'external/v2/users'
      resources :departments, only: %i[index show create update destroy], controller: 'external/v2/departments'
      put 'users', controller: 'external/v2/users', action: :update

      resources :businesses, concerns: :activate do
        resources :contents, only: %i[create update show], controller: 'external/v2/contents'
        put 'contents', controller: 'external/v2/contents', action: :upsert

        post "/contents/show", to: 'external/v2/contents#index'
      end
    end
  end

  scope :external, defaults: { format: 'xml' } do
    resources :companies, only: %i[create], controller: 'external/companies', defaults: { format: 'json' }

    resources :contents, only: %i[index], controller: 'external/contents' do
      resources :steps, only: [] do
        patch :changing, on: :member, to: 'external/answers#changing', defaults: { format: 'json' }

        patch :authorize, on: :member, to: 'external/answers#authorize', defaults: { format: 'json' }
        patch :reject, on: :member, to: 'external/answers#reject', defaults: { format: 'json' }
      end
    end

    resources :sub_contents, only: :index, controller: 'external/sub_contents'
    resources :businesses, only: %i[create update destroy], controller: 'external/businesses'
    resources :sub_businesses, only: %i[create update destroy], controller: 'external/sub_businesses'
    resources :users, only: %i[create destroy update], controller: 'external/users'
    resources :administrators, only: %i[create destroy], controller: 'external/administrators'
    resources :notifications, only: %i[show create update destroy], controller: 'external/notifications', format: 'json'
  end
end
