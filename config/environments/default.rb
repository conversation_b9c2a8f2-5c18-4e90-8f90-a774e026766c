Rails.application.configure do
  # Enable caching and eager load to increase performance.
  config.cache_classes = true
  config.eager_load = true

  # Disable full error reports to friendly error messages.
  config.consider_all_requests_local = false

  # Disable serving static files, because it's handled by S3.
  config.public_file_server.enabled = false

  # Disable forcing SSL, because it wil be handled by the load balancer.
  config.force_ssl = false

  # Set cache store to Redis.
  config.cache_store = :redis_cache_store, { url: Rails.application.credentials.REDIS_URL }

  # Set up Sidekiq.
  config.active_job.queue_adapter = :sidekiq

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation cannot be found).
  config.i18n.fallbacks = true

  # Store uploaded files on AWS S3.
  config.active_storage.service = :amazon

  # Do not dump schema after migrations.
  config.active_record.dump_schema_after_migration = false

  # Set up action controller.
  config.action_controller.perform_caching = true

  # Do not enable deprecation warnings in live environment.
  config.active_support.deprecation = nil

  # Set up logging for CloudWatch.
  config.logger = ActiveSupport::Logger.new('/proc/1/fd/1') # Docker default stdout
  config.log_level = :error
  config.active_record.logger = nil
  config.active_record.verbose_query_logs = false

  # Set up mailer.
  config.action_mailer.perform_caching = false
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = false
  config.action_mailer.default charset: 'utf-8'

  config.action_mailer.smtp_settings = {
    address: Rails.application.credentials.aws_ses_address,
    authentication: :login,
    port: 587,
    ssh: true,
    openssl_verify_mode: 'none',
    enable_starttls_auto: true,
    user_name: Rails.application.credentials.aws_ses_user_name,
    password: Rails.application.credentials.aws_ses_password
  }
end
Rails.application.routes.default_url_options[:protocol] = 'https'
