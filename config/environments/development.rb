Rails.application.configure do
  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  config.file_watcher = ActiveSupport::EventedFileUpdateChecker

  config.asset_host = 'http://lvh.me:4200'

  # Don't cache and eager load for development, to make development easier by hot reloading.
  config.cache_classes = false
  config.eager_load = false

  # Show full error reports in request responses to make it easier to debug.
  config.consider_all_requests_local = true

  # Disable serving static files, because it's handled by the front-end.
  config.public_file_server.enabled = false

  # Disable forcing SSL on development.
  config.force_ssl = false

  # Set cache store to Redis.
  config.cache_store = :redis_cache_store, { url: Rails.application.credentials.REDIS_URL }

  # Set up Sidekiq.
  config.active_job.queue_adapter = :sidekiq

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation cannot be found).
  config.i18n.fallbacks = true

  # Store uploaded files on the local file system.
  config.active_storage.service = :local

  # Dump schema after migrations for commiting to Git.
  config.active_record.dump_schema_after_migration = true

  # Set up action controller.
  config.action_controller.perform_caching = true

  # Print deprecation warnings to the Rails logger.
  config.active_support.deprecation = :log

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Set up logging for Docker.
  config.logger = ActiveSupport::Logger.new('/proc/1/fd/1') # Docker default stdout
  config.log_level = :debug
  config.active_record.logger = config.logger
  config.active_record.verbose_query_logs = true

  # Set up mailer.
  config.action_mailer.perform_caching = false
  config.action_mailer.default_url_options = { host: 'www.lvh.me:3000' }
  config.action_mailer.delivery_method = :letter_opener
  config.action_mailer.perform_deliveries = true
  config.action_mailer.raise_delivery_errors = false
  config.action_mailer.default charset: 'utf-8'

  # Enable bullet to detect N+1 queries.
  config.after_initialize do
    Bullet.enable = false
    Bullet.rails_logger = false
  end

  config.action_cable.allowed_request_origins = ['http://*', 'https://*', '*']
  config.action_cable.disable_request_forgery_protection = true

  # Remove host authorization header for development.
  config.hosts = []
end
Rails.application.routes.default_url_options[:host] = 'lvh.me'
Rails.application.routes.default_url_options[:port] = '3000'
