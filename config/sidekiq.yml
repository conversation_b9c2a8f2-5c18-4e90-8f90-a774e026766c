:concurrency: 10

development:
  :verbose: true
  :concurrency: 10
sandbox:
  :verbose: true
  :concurrency: 10
uat:
  :concurrency: 10
staging:
  :concurrency: 10
production:
  :concurrency: 10
academy:
  :concurrency: 10


:queues:
  - [answers_webhook, 8]
  - [update_content_keywords, 7]
  - [statistics, 6]
  - [save_answer_high_priority_worker, 5]
  - [bulk_alteration_high_priority_worker, 5]
  - [save_answer_low_priority_worker, 4]
  - [bulk_alteration_low_priority_worker, 4]
  - [save_answer_staff_high_priority_worker, 3]
  - [bulk_alteration_staff_high_priority_worker, 3]
  - [save_answer_staff_low_priority_worker, 2]
  - [bulk_alteration_staff_low_priority_worker, 2]
  - default
  - elasticsearch_priority
  - elasticsearch
  - records_deletion
  - record_delete

#cron parser: https://github.com/floraison/fugit
:scheduler:
  :schedule:
    check_not_indexed_contents:
      every: 60 minutes
      class: CheckNotIndexedContentWorker
    delete_unused_drafts:
      cron: "00 20 * * *" # every day at 20h
      class: DeleteUnusedDraftsWorker
    delete_old_troubleshootings:
      cron: "00 01 * * *" # every day at 01h
      class: DeleteOldTroubleshootingsWorker
    update_cloudwatch_queue_statistics:
      every: 2 minutes
      class: UpdateCloudwatchQueueStatisticsWorker
    reprocess_failed_bulk_saving_answers:
      every: 5 minutes
      class: ReprocessBulkSavingAnswersWorker
