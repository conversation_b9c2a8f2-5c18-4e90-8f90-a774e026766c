services:
  db:
    image: "postgres:14-alpine"
    env_file: .env.production
    shm_size: 128m
    ports:
      - "5433:5432"
    networks:
      - fourmdg-net
    mem_limit: 500m
    cpus: 0.5
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: .docker/dev/entrypoint_web.sh
    # If you need to run the server using a self signed certificate (to serve HTTPS requests), uncomment the line below
    # command: /bin/sh -c "rm -f /app/tmp/pids/server.pid && bundle exec rails s -p 3000 -b 'ssl://0.0.0.0?key=keys/localhost.key&cert=keys/localhost.crt'"
    env_file: .env.production
    ports:
      - "3000:3000"
    stdin_open: true
    tty: true
    mem_limit: 3g
    cpus: 4
    networks:
      - fourmdg-net
    depends_on:
      - redis
      - db

  redis:
    image: redis:6.2-alpine
    env_file: .env.production
    mem_limit: 500m
    cpus: 0.5
    ports:
      - "6380:6379"
    networks:
      - fourmdg-net

networks:
  fourmdg-net:
    driver: bridge
    external: false