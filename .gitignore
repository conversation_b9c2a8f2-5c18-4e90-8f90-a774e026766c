# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

/coverage/*
/coverage/.resultset.json
!/coverage/.last_run.json
# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

.byebug_history
/storage/
/backups/
coverage/.resultset.json
*.tar.gz
*.dump
.tool-versions

*.session.sql
config/secrets.yml
/config/credentials/*.key
