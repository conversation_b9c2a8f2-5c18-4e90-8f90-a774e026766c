# frozen_string_literal: true

module Elasticsearch
  module Configuration
    class << self
      def client
        @client = build_client
      end

      def reset_client!
        @client = nil
      end

      private

      def build_client
        ::Elasticsearch::Client.new(client_options) do |faraday|
          configure_faraday(faraday)
        end
      end

      def client_options
        {
          url: elasticsearch_host,
          log: Rails.env.development? || Rails.env.test?
        }
      end

      def configure_faraday(faraday)
        faraday.use :gzip
        faraday.request :gzip
        faraday.adapter Faraday.default_adapter
        faraday.basic_auth(elasticsearch_user, elasticsearch_password)
        faraday.ssl.verify = false if Rails.env.development? || Rails.env.test?
      end

      def elasticsearch_host
        Rails.application.credentials[:elasticsearch_host]
      end

      def elasticsearch_user
        Rails.application.credentials[:elasticsearch_user]
      end

      def elasticsearch_password
        Rails.application.credentials[:elasticsearch_password]
      end
    end
  end
end
