class ContentDatatable < AjaxDatatablesRails::ActiveRecord
  include ShowOnListFieldHelper
  ORDER_OPTIONS = %w[asc desc].freeze
  ALLOWED_COLUMNS = Content.column_names.freeze

  private

  def view_columns
    {}
  end

  def business_id
    @business_id ||= options[:business_id]
  end

  def business
    @business ||= Business.find(business_id)
  end

  def current_user
    @current_user ||= options[:current_user]
  end

  def last_segment_of_uuid(uuid)
    uuid.split('-').last
  end

  def field_key(step_id, field_id)
    "#{step_id}:#{field_id}"
  end

  def field_key_sql_alias(field_key)
    step_id = field_key.split(':').first
    field_id = field_key.split(':').second

    "#{last_segment_of_uuid(step_id)}:#{last_segment_of_uuid(field_id)}"
  end

  def default_fields(
    id: nil,
    parent_id: nil,
    status: nil,
    current_step: nil,
    current_step_status: nil,
    created_at: nil,
    created_by_name: nil,
    updated_at: nil,
    updated_by_name: nil,
    note: nil
  )
    {
      id: id,
      parent_id: parent_id,
      status: status,
      current_step: current_step,
      current_step_status: current_step_status,
      created_at: created_at,
      created_by_name: created_by_name,
      updated_at: updated_at,
      updated_by_name: updated_by_name,
      note: note,
    }
  end

  def data
    default_fields_select = default_fields(
      id: :id,
      parent_id: :parent_id,
      status: :status,
      current_step: 'current_answer_step.name current_step',
      current_step_status: 'current_answer.status current_step_status',
      created_at: :created_at,
      created_by_name: 'created_by.name created_by_name',
      updated_at: 'last_updated_answer.updated_at',
      updated_by_name: 'updated_by.name updated_by_name',
      note: :note
    ).values
    contents = records
      .joins('left join answers current_answer on current_answer.id = contents.current_answer_id')
      .joins('left join steps current_answer_step on current_answer_step.id = current_answer.step_id')
      .joins(
        %{left join answers last_updated_answer on last_updated_answer.id = (
          select id
          from answers a
          where a.content_id = contents.id
          order by a.updated_at desc
          limit 1
        )}
      )
      .joins('left join users updated_by on updated_by.id = current_answer.user_id')
      .select(*default_fields_select)

    processed_contents_ids = []
    contents.map do |content|
      next if processed_contents_ids.include?(content['id'])
      new_content = default_fields(
        id: content['id'],
        parent_id: content['parent_id'],
        status: content['status'],
        current_step: content.finished_status? ? '' : content['current_step'],
        current_step_status: Answer.statuses.key(content['current_step_status']),
        created_at: content.created_at&.iso8601,
        created_by_name: content['created_by_name'],
        updated_at: content.updated_at&.iso8601,
        updated_by_name: content['updated_by_name'],
        note: content['note']
      )

      values = get_content_values(content)
      if is_sub_business? && !is_sub_business_inside_content? && content.parent.present?
        values.merge!(get_content_values(content.parent))
      end

      display_fields.each do |column|
        step_id = column[:"step_id"]
        field_id = column[:"field_id"]
        field_type = Field.types.key(column[:"field_type"])
        field_options = column[:"field_options"]
        values[step_id] ||= {}
        raw_value = values.dig(step_id, field_id)

        if field_type == 'reference' && raw_value
          field_option = FieldOption.with_active_contents.find_by(field_id: field_id, value: raw_value)
          parsed_value = field_option.try(:label)
        elsif field_type == 'multiple_reference'
          parsed_value = FieldOption.with_active_contents.where(
            field_id: field_id, value: raw_value
          ).distinct.pluck(:label).join(' | ')
        elsif field_type == 'multiple' && raw_value.is_a?(Array)
          parsed_value = raw_value.join(' | ')
        elsif field_type == 'upload'
          parsed_value = raw_value.present? ? raw_value : ''
        elsif field_type == 'link'
          if raw_value.blank?
            parsed_value = ''
          else
            parsed_value = raw_value.is_a?(Hash) ? raw_value.to_json : raw_value
          end
        elsif field_type == 'dropdown' && raw_value
          selected_option = field_options.detect { |option| option['value'] == raw_value }
          parsed_value = selected_option.present? ? selected_option['label'] : ''
        else
          parsed_value = raw_value
        end

        new_content[field_key(step_id, field_id)] = parsed_value
      end

      processed_contents_ids << content['id']
      new_content
    end.compact
  end

  def get_content_values(content)
    content.answers.each_with_object({}) do |answer, values|
      values[answer.step_id] = (answer.values || {})
    end
  end

  def sort_records(contents)
    contents_table = Content.arel_table

    params[:order].each_key do |index|
      order_index = params[:order][index][:column]
      field_name = params[:columns][order_index][:data]
      sort_direction = params[:order][index][:dir]
      nulls_order = sort_direction == 'asc' ? 'nulls first' : 'nulls last'

      is_default_field = default_fields.keys.include?(field_name.to_sym)
      is_data_field = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/ =~ field_name

      if is_default_field
        if ALLOWED_COLUMNS.include?(field_name) && ORDER_OPTIONS.include?(sort_direction)
          contents = contents.order("#{field_name} #{sort_direction}")
        end
      elsif is_data_field
        field_id = field_name.split(':').last
        field = Field.find(field_id)

        cv_alias = "cv#{index}"
        cv_table = Arel::Table.new(:content_values).alias(cv_alias)

        join_condition = cv_table[:content_id].eq(contents_table[:id])
                         .and(cv_table[:field_id].eq(field_id))

        cv_join = contents_table.join(cv_table, Arel::Nodes::OuterJoin)
                                .on(join_condition).join_sources

        contents = contents.joins(cv_join)

        if field.reference_business_id
          fo_alias = "fo#{index}"
          fo_table = Arel::Table.new(:field_options).alias(fo_alias)

          fo_join_condition = fo_table[:field_id].eq(field_id)
                                .and(fo_table[:value].eq(cv_table[:value]))

          fo_join = contents_table.join(fo_table, Arel::Nodes::OuterJoin)
                                  .on(fo_join_condition).join_sources

          contents = contents.joins(fo_join)

          fo_contents_alias = "fo_contents#{index}"
          fo_contents_table = Arel::Table.new(:contents).alias(fo_contents_alias)

          fo_contents_join_condition = fo_table[:content_id].eq(fo_contents_table[:id])
                                         .and(fo_contents_table[:deleted_at].eq(nil))

          fo_contents_join = contents_table.join(fo_contents_table, Arel::Nodes::OuterJoin)
                                           .on(fo_contents_join_condition).join_sources

          contents = contents.joins(fo_contents_join)

          contents = contents.order("#{fo_alias}.label #{sort_direction} #{nulls_order}")
        else
          contents = contents.order("#{cv_alias}.value #{sort_direction} #{nulls_order}")
        end
      end
    end

    contents
  end

  def filter_records(contents)
    contents = filter_by_search(contents) if is_search_present?
    contents = filter_by_statuses(contents) if is_statuses_present?

    contents
  end

  def get_raw_records
    return @contents if @already_ran

    @contents = Content.unscoped
                       .where(business_id: business_id, draft: false)
                       .joins('left join users created_by on created_by.id = contents.created_by_id')

    only_actives? ? filter_by_active : filter_by_inactive
    filter_by_limited_user if is_user? && is_limited_user?
    filter_by_limited_department if is_user? && is_limited_department?
    filter_by_access_control if is_user?
    if is_sub_business_inside_content?
      filter_by_sub_business_inside_content
    else
      filter_by_business_listing
    end

    @already_ran = true
    @contents
  end

  def display_fields(include_parent_keys = true)
    fields(business_id, include_parent_keys)
  end

  def only_actives?
    options[:only_actives] != 'false'
  end

  def is_user?
    current_user.present?
  end

  def filter_by_active
    @contents = @contents.where(deleted_at: nil)
  end

  def filter_by_inactive
    @contents = @contents.where.not(deleted_at: nil)
  end

  def is_limited_user?
    current_user.limited?
  end

  def filter_by_limited_user
    @contents = @contents.where(created_by_id: current_user.id)
  end

  def is_limited_department?
    current_user.departments.any?(&:limited?)
  end

  def filter_by_limited_department
    @contents = @contents.where(created_by_id: User.joins(:departments).where(departments: { id: current_user.departments.pluck(:department_id) }).pluck(:id))
  end

  def is_statuses_present?
    @options[:statuses].present?
  end

  def filter_by_statuses(contents)
    contents = contents.where(status: @options[:statuses])
  end

  def is_search_present?
    datatable.search.value.present?
  end

  def filter_by_search(contents)
    filter_clause = 'contents.keywords ilike :query or created_by.name ilike :query or contents.id::text ilike :query'

    contents.where(filter_clause, query: "%#{datatable.search.value}%")
  end

  def is_sub_business?
    business.sub_business?
  end

  def is_sub_business_inside_content?
    params[:parent_id].present?
  end

  def filter_by_sub_business_inside_content
    @contents = @contents.where(parent_id: params[:parent_id])
  end

  def filter_by_business_listing
    @contents = @contents
      .joins('left join contents parent on contents.parent_id = parent.id')
      .where(parent: { deleted_at: nil })
      .where('parent.draft = false or parent.draft is null')
  end

  def access_control_service
    @access_control_service ||= AccessControlService.new(business_id, current_user, @contents)
  end

  def filter_by_access_control
    @contents = access_control_service.run
  end
end
