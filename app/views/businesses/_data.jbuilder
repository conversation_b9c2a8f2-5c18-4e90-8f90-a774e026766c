json.extract! business, :id, :description, :business_group_id, :help_url, :show_on_list_created_at, :skip_webhook,
              :show_on_list_updated_at, :show_on_list_updated_by_name, :show_on_list_created_by_name, :webhook_url, :integrate_elastic

json.apply_field_rule business.apply_field_rule?
json.apply_field_validation_rule business.apply_field_validation_rule?
json.enable_validation_web_service business.enable_validation_web_service?
json.enable_verification_web_service business.enable_verification_web_service?
json.bulk_insert_on_first_step_validates_pk business.bulk_insert_on_first_step_validates_pk?
json.fill_default_field_value business.fill_default_field_value?
json.notification business.notification?
json.show_bulk_alteration business.show_bulk_alteration?
json.show_bulk_insert business.show_bulk_insert?
json.show_on_dashboard business.show_on_dashboard?
json.show_on_top_answers business.show_on_top_answers?
json.sub_business business.sub_business?
json.validate_fields business.validate_fields?
json.validate_required_fields business.validate_required_fields?
json.allow_to_skip_validations_when_bulking business.allow_to_skip_validations_when_bulking?
json.who_can_delete_contents business.who_can_delete_contents_before_type_cast
json.icon business.icon

json.name business.translated_attribute('name', skip_translation?, @company_enable_internationalization)
json.deleted business.discarded?
