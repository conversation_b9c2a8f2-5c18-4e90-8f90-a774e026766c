if record
  json.extract! record, :id, :name, :subdomain, :page_title, :expire_password_after_in_days, :auth_domain, :allowed_sites, :welcome_video_url, :default_department_id,
                :allowed_ips, :token_life_span_in_minutes, :limit_user_on_signup, :bypass_approval, :block_menus,
                :smtp_address, :smtp_custom, :smtp_from, :smtp_password, :smtp_port, :smtp_starttls, :smtp_username, :custom_openid_name

  json.use_elasticsearch record.use_elasticsearch?
  json.enable_google_oauth record.enable_google_oauth?
  json.enable_microsoft_oauth record.enable_microsoft_oauth?
  json.enable_open_id record.enable_open_id?
  json.disable_tips record.disable_tips?
  json.enable_internationalization record.enable_internationalization?
  json.enable_email_and_password_login record.enable_email_and_password_login?
  json.enable_signup record.enable_signup?
  json.chat_enabled record.chat_enabled?
  json.chatbot_enabled record.chatbot_enabled?
  json.contact_us_enabled record.contact_us_enabled?
  json.restrict_access_by_ip record.restrict_access_by_ip?
  json.not_validate_auth_domain_openid record.not_validate_auth_domain_openid?

  json.open_id_config record.open_id_config
end
