if @record
  json.extract! @record, :embed_welcome_video, :custom_openid_name

  json.enable_google_oauth @record.enable_google_oauth?
  json.enable_microsoft_oauth @record.enable_microsoft_oauth?
  json.disable_tips @record.disable_tips?
  json.enable_open_id @record.enable_open_id?
  json.enable_email_and_password_login @record.enable_email_and_password_login?
  json.enable_internationalization @record.enable_internationalization?
  json.use_elasticsearch @record.use_elasticsearch?
  json.enable_signup @record.enable_signup?
  json.bypass_approval @record.bypass_approval?
  json.not_validate_auth_domain_openid @record.not_validate_auth_domain_openid?

  if user_signed_in?
    json.chat_enabled @record.chat_enabled?
    json.chatbot_enabled @record.chatbot_enabled?
    json.contact_us_enabled @record.contact_us_enabled?
  elsif administrator_signed_in?
    json.chat_enabled @record.chat_enabled?
    json.chatbot_enabled @record.chatbot_enabled?
    json.contact_us_enabled @record.contact_us_enabled?
    json.restrict_access_by_ip @record.restrict_access_by_ip?
    json.limit_user_on_signup @record.limit_user_on_signup?
    json.open_id_config @record.open_id_config
    json.token_life_span_in_minutes @record.token_life_span_in_minutes
    json.allowed_ips @record.allowed_ips
  end

  json.theme_id @record.theme_id
  json.logo_url Rails.application.routes.url_helpers.rails_blob_url(@record.logo_image.blob) if @record.logo_image.attached?
  json.background_url Rails.application.routes.url_helpers.rails_blob_url(@record.background_image.blob) if @record.background_image.attached?
  json.internal_logo_url @record.internal_logo_image.attached? ? Rails.application.routes.url_helpers.rails_blob_url(@record.internal_logo_image.blob) : Rails.application.credentials.default_internal_logo_image
  json.favicon_url @record.favicon_image.attached? ? Rails.application.routes.url_helpers.rails_blob_url(@record.favicon_image.blob) : Rails.application.credentials.default_favicon_image
  json.custom_openid_logo_url @record.custom_openid_logo.attached? ? Rails.application.routes.url_helpers.rails_blob_url(@record.custom_openid_logo.blob) : nil

  json.data_replacements @record.data_replacements do |data_replacement|
    json.extract! data_replacement, :text, :replacement
  end
end
