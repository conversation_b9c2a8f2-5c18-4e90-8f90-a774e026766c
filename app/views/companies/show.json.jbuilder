json.partial! 'companies/data', record: @record

if @record
  json.extract! @record, :api_key

  json.logo do
    json.partial! 'attachments/data', record: @record.logo_image
  end

  json.background do
    json.partial! 'attachments/data', record: @record.background_image
  end

  json.internal_logo do
    if @record.internal_logo_image.attached?
      json.url Rails.application.routes.url_helpers.rails_blob_url(@record.internal_logo_image.blob)
    else
      json.url Rails.application.credentials.default_internal_logo_image
    end
  end

  json.favicon do
    if @record.favicon_image.attached?
      json.url Rails.application.routes.url_helpers.rails_blob_url(@record.favicon_image.blob)
    else
      json.url Rails.application.credentials.default_favicon_image
    end
  end

  json.custom_openid_logo do
    json.partial! 'attachments/data', record: @record.custom_openid_logo
  end

  json.data_replacements @record.data_replacements do |data_replacement|
    json.extract! data_replacement, :text, :replacement
  end
end
