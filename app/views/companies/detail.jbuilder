if @record
  json.extract! @record, :embed_welcome_video, :custom_openid_name

  json.page_title @record.page_title
  json.enable_google_oauth @record.enable_google_oauth?
  json.enable_microsoft_oauth @record.enable_microsoft_oauth?
  json.enable_open_id @record.enable_open_id?
  json.disable_tips @record.disable_tips?
  json.enable_email_and_password_login @record.enable_email_and_password_login?
  json.enable_internationalization @record.enable_internationalization?
  json.use_elasticsearch @record.use_elasticsearch?
  json.enable_signup @record.enable_signup?
  json.bypass_approval @record.bypass_approval?
  json.token_life_span_in_minutes @record.token_life_span_in_minutes
  json.not_validate_auth_domain_openid @record.not_validate_auth_domain_openid?

  json.theme do
    json.id @record&.theme&.id
    json.name @record&.theme&.name
    json.text_color @record&.theme&.text_color
    json.background_color @record&.theme&.background_color
    json.button_color @record&.theme&.button_color
    json.menu_color @record&.theme&.menu_color
    json.menu_focus_color @record&.theme&.menu_focus_color
  end
  json.logo_url Rails.application.routes.url_helpers.rails_blob_url(@record.logo_image.blob) if @record.logo_image.attached?
  json.background_url Rails.application.routes.url_helpers.rails_blob_url(@record.background_image.blob) if @record.background_image.attached?
  json.internal_logo_url @record.internal_logo_image.attached? ? Rails.application.routes.url_helpers.rails_blob_url(@record.internal_logo_image.blob) : Rails.application.credentials.default_internal_logo_image
  json.favicon_url @record.favicon_image.attached? ? Rails.application.routes.url_helpers.rails_blob_url(@record.favicon_image.blob) : Rails.application.credentials.default_favicon_image
  json.custom_openid_logo_url @record.custom_openid_logo.attached? ? Rails.application.routes.url_helpers.rails_blob_url(@record.custom_openid_logo.blob) : nil

  json.chat_enabled @record.chat_enabled?
  json.chatbot_enabled @record.chatbot_enabled?
  json.contact_us_enabled @record.contact_us_enabled?

  json.data_replacements @record.data_replacements do |data_replacement|
    json.extract! data_replacement, :text, :replacement
  end
else
  json.internal_logo_url Rails.application.credentials.default_internal_logo_image
  json.favicon_url Rails.application.credentials.default_favicon_image
end
