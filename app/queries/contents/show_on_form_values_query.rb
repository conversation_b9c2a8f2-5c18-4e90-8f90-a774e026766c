module Contents
  class ShowOnFormValuesQuery < ApplicationQuery
    def initialize(content_id, user_id)
      @relation = Content
      @content_id = content_id
      @user_id = user_id
    end

    def call
      query
    end

    private

    attr_reader :relation, :content_id, :user_id

    def query
      relation
        .includes(answers: { step:  [:step_templates] })
        .joins(
          <<-SQL.squish
            JOIN steps ON steps.business_id = contents.business_id
            JOIN step_templates ON step_templates.step_id = steps.id
            JOIN fields ON fields.template_id = step_templates.template_id
          SQL
        )
        .joins('LEFT JOIN answers ON answers.step_id = steps.id AND answers.content_id = contents.id')
        .where(contents: { id: content_id })
        .where(fields: { deleted_at: nil, show_on_form: true })
        .where(
          <<-SQL.squish,
            EXISTS (
              SELECT 1 FROM step_permissions sp
              WHERE sp.step_id = steps.id
              AND sp.user_id = ?
            )
            OR EXISTS (
              SELECT 1 FROM step_permissions sp2
              INNER JOIN departments_users du ON sp2.department_id = du.department_id
              WHERE sp2.step_id = steps.id
              AND du.user_id = ?
            )
          SQL
          user_id, user_id
        )
        .select('
          steps.order as step_order,
          fields.id AS field_id,
          fields.type AS field_type,
          fields.order as field_order,
          answers.id AS answer_id,
          answers.data ->> \'values\' AS field_value,
          step_templates.order as step_template_order
        ')
        .order('step_order ASC, step_template_order ASC, field_order ASC')
    end
  end
end
