class S3ProviderService
  def create(key:, file:)
    response = fog_directory.files.create(
      key: key,
      body: file,
      public: true
    )
    response.public_url
  end

  def list_files(prefix)
    fog_directory.files.all(prefix:)
  end

  private

  def fog_directory
    @connection ||= Fog::AWS::Storage.new(
      region: Rails.application.credentials.aws_region,
      aws_access_key_id: Rails.application.credentials[:S3_ACCESS_KEY_ID],
      aws_secret_access_key: Rails.application.credentials[:S3_SECRET_ACCESS_KEY]
    )

    @fog_directory ||= @connection.directories.get(bucket_name) 
  end
  
  def bucket_name
    @bucket_name ||= Rails.application.credentials.s3_directory
  end
end