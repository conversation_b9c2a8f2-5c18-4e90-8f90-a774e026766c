# frozen_string_literal: true

require_relative 'base_service'
require_relative 'client_service'
require_relative 'transformers/content_transformer'

module Elasticsearch
  class DocumentService < BaseService
    def initialize
      @client_service = ClientService.new
      @content_transformer = Transformers::ContentTransformer.new
    end

    def index_document(content)
      validate_content!(content)

      document = content_transformer.transform(content)

      client_service.index_document(
        content.business_id,
        content.id,
        document
      )
    end

    def index_documents(content_ids)
      validate_content_ids!(content_ids)

      scope = build_content_scope(content_ids)
      validate_scope!(scope, content_ids)

      documents = build_bulk_documents(scope)

      client_service.bulk_index(documents)
    end

    def delete_document(content_id)
      content = Content.with_discarded.find_by_id(content_id)

      if content.blank?
        delete_orphaned_document(content_id)
      else
        client_service.delete_document(content.business_id, content_id)
      end
    end

    private

    attr_reader :client_service, :content_transformer

    def validate_content_ids!(content_ids)
      raise ServiceError, 'Content IDs are required' if content_ids.blank?
    end

    def build_content_scope(content_ids)
      Content.unscoped
             .joins(:business)
             .where(id: content_ids, draft: false, business: { deleted_at: nil })
    end

    def validate_scope!(scope, content_ids)
      validate_single_business!(scope)
      validate_scope_not_empty!(scope, content_ids)
    end

    def validate_single_business!(scope)
      business_count = scope.group(:business_id).count.keys.size

      if business_count > 1
        raise ServiceError, I18n.t(
          'elastic_searcher.contents_must_belong_to_same_business',
          scope: 'activerecord.errors.services'
        )
      end
    end

    def validate_scope_not_empty!(scope, content_ids)
      return unless scope.blank?

      translation_key = content_ids.length == 1 ? 'one' : 'many'
      raise ServiceError, I18n.t(
        "elastic_searcher.empty_scope.#{translation_key}",
        scope: 'activerecord.errors.services',
        id: content_ids.join(' | ')
      )
    end

    def build_bulk_documents(scope)
      documents = { index: nil, body: [] }

      scope.select(:id, :name, :note, :business_id, :status, :deletion_reason, :deleted_at, :parent_id, :created_at)
           .find_each do |content|

        documents[:index] ||= index_name(content.business_id)
        documents[:body] << {
          index: {
            _id: content.id,
            data: content_transformer.transform(content)
          }
        }
      end

      documents
    end

    def delete_orphaned_document(content_id)
      query = { query: { match: { CONTENT_ID => content_id } } }
      client_service.delete_by_query(wildcard_index_name, query)
    end
  end
end
