class AdministratorService < ApplicationService
  service_for :administrator

  def update(id)
    record = Administrator.find(id)

    raise AdministratorUpdateNameError if record.provider != 'email' && @parameters[:name]

    return unless @errors.empty?

    record.assign_attributes(@parameters)

    save_record(record)
  rescue AdministratorUpdateNameError => e
    @success = false
    @errors << e.error_message
  rescue ActiveRecord::RecordNotFound => e
    @success = false
    @errors = e.message
  rescue ActiveRecord::RecordNotUnique => e
    @success = false
    
    record_params = Administrator.find_by(email: @parameters[:email])
    if record.email == record_params.email && record_params.id != record.id
      @errors << I18n.t("api.v2.administrators.errors.email_already_exists")
    else
      @errors = e.message
    end
  end

  def create
    record = model.new(@parameters)

    save_record(record)
  rescue ActiveRecord::RecordNotUnique => e
    @success = false
    @errors = I18n.t("api.v2.administrators.errors.email_already_exists")
  end
end
