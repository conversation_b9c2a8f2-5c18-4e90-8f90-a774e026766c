class BusinessRulesRunner < BaseRulesRunner
  attr_accessor :content_values

  def initialize(answer, context, current_user = nil, changed_fields_ids = [], parents = {})
    @current_user = current_user
    @answer = answer
    @parents = parents
    @content_values = get_content_values

    @context = context

    @first_changed_value_field_id = changed_fields_ids&.first
    @all_changed_value_fields_ids = changed_fields_ids
    @last_run_changed_value_fields_ids = changed_fields_ids

    @all_possible_rules = this_step_business_rules.dependent.to_a
    @all_rules = Set.new(get_rules)
    @matched_rules = []

    run
  end

  def field_changes
    matched_actions.each_with_object({}) do |action, result|
      field_id = action['field'].split(':')[1]
      old_value = result[field_id] || {}
      result[field_id] = old_value.merge(build_matched_field_attributes(action))
    end
  end

  def field_changes_with_unmatched_actions
    result = {}
    fields = Field.where(id: all_possible_fields_ids).select(:id, :required, :visible, :enabled, :template_id)
    fields.each do |field|
      next if result[field.id]
      result[field.id] = build_unmatched_field_attributes(field)
    end

    field_changes.each do |field_id, attributes|
      old_value = result[field_id] || {}
      result[field_id] = old_value.merge(attributes)
    end

    result
  end

  private

  def is_field_clicked?(field_id)
    field_id == @first_changed_value_field_id
  end

  def restriction_satisfied?(restrictions)
    return true if restrictions.blank?

    user_permissions = @current_user&.permissions&.pluck(:id) || []

    restrictions.any? do |restriction|
      if restriction['class'] == 'User'
        restriction['id'] == @current_user.id
      else
        user_permissions.include?(restriction['id'])
      end
    end
  end


  def build_matched_field_attributes(rule_action)
    {
      "value" => rule_action['value'].blank? ? nil : rule_action['value'],
      "visible" => determine_visibility(rule_action),
      "enabled" => determine_enabled_state(rule_action),
      "required" => determine_required_state(rule_action),
      "rule_matched" => true
    }.compact
  end

  def build_unmatched_field_attributes(field)
    {
      "visible" => field.visible,
      "enabled" => field.enabled,
      "required" => field.required,
      "template_id" => field.template_id,
      "answer_id" => @answer.id,
      "rule_matched" => false
    }
  end

  def determine_visibility(rule_action)
    return unless rule_action.key?('field_hidden') || rule_action.key?('field_visible')
    rule_action.key?('field_hidden') ? !rule_action['field_hidden'] : rule_action['field_visible']
  end

  def determine_enabled_state(rule_action)
    !rule_action['field_disabled'] if rule_action.key?('field_disabled')
  end

  def determine_required_state(rule_action)
    rule_action['field_required'] if rule_action.key?('field_required')
  end

  def run
    @last_run_changed_value_fields_ids = []
    @any_value_changed = false

    unmatched_rules.each do |rule|
      if rule_match?(rule)
        apply(rule.rule_actions)
        @matched_rules << rule
      end
    end

    if @any_value_changed
      update_rules
      run
    end
  end

  def extract_actions(rules)
    rules.each_with_object([]) do |rule, actions|
      rule.rule_actions.each do |rule_action|
        next if is_field_clicked? rule_action['field'].split(':').second
        next unless restriction_satisfied?(rule_action['restrictions'])

        actions << rule_action if rule_action['field'].split(':')[0] == @answer.step_id
      end
    end
  end

  def all_actions
    extract_actions(@all_rules)
  end

  def matched_actions
    extract_actions(@matched_rules)
  end

  def all_possible_fields_ids
    all_actions.map { |action| action['field'].split(':')[1] }.uniq
  end

  # Applies the actions of a given rule by updating the matched actions
  # and content values.
  #
  # Iterates over each action in the rule, extracting the field key and
  # storing the action in the matched actions hash. If the action has a
  # value, it updates the content values hash with the new value.
  #
  # @param [Rule] rule The rule whose actions are to be applied.
  def apply(rule_actions)
    user_permissions = @current_user&.permissions&.pluck(:id) || []

    rule_actions.each do |rule_action|
      step_id = rule_action['field'].split(':')[0]
      field_id = rule_action['field'].split(':')[1]
      field_key = rule_action['field']
      next if step_id != @answer.step_id

      next if is_field_clicked? field_id

      next unless restriction_satisfied? rule_action['restrictions']

      if rule_action['value'].present?
        @any_value_changed = true

        @content_values[field_key] = format_value(rule_action['value']) if rule_action['value'].present?

        @all_changed_value_fields_ids << field_id unless @all_changed_value_fields_ids.include?(field_id)
        @last_run_changed_value_fields_ids << field_id unless @last_run_changed_value_fields_ids.include?(field_id)
      end
    end
  end

  def update_rules
    get_dynamic_rules.each do |rule|
      @all_rules << rule unless @matched_rules.include?(rule)
    end
  end

  def get_input_rules
    filter_restrictions(this_step_business_rules.dependent.to_set, @current_user)
  end

  def get_dynamic_rules
    rules_to_run = Set.new(filter_rules_by_conditions(@all_possible_rules, mount_field_key(@last_run_changed_value_fields_ids)))
    @current_possible_changed_fields_keys = Set.new

    loop do
      new_affected_rules = filter_rules_by_actions(@all_possible_rules, possible_changed_fields!(rules_to_run))
      break unless rules_to_run != (rules_to_run + new_affected_rules)
      rules_to_run += new_affected_rules
    end

    filter_restrictions(rules_to_run, @current_user)
  end

  def possible_changed_fields!(rules)
    rules.each do |rule|
      rule.rule_actions.each do |rule_action|
        action_field_key = rule_action['field']
        action_step_id = action_field_key.split(':')[0]
        next if action_step_id != @answer.step_id
        @current_possible_changed_fields_keys << action_field_key
      end
    end
    @current_possible_changed_fields_keys
  end

  def filter_rules_by_conditions(rules, keys)
    rules.select do |rule|
      keys.any? { |key| rule.rules.to_json.include?(key) }
    end
  end

  def filter_rules_by_actions(rules, keys)
    rules.select do |rule|
      keys.any? { |key| rule.rule_actions.to_json.include?(key) }
    end
  end

  def mount_field_key(fields_ids)
    fields_ids.map { |id| "#{@answer.step_id}:#{id}" }
  end
end
