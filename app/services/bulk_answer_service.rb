class BulkAnswerService < ApplicationService
  service_for :bulk_saving_answer

  CONTENT_ID_FIELD = 'content_id'.freeze
  PARENT_CONTENT_ID_FIELD = 'parent_content'.freeze
  MAXIMUM_RECORDS_FOR_STAFF_USER = 501
  MAXIMUM_RECORDS_FOR_USER = 201

  CONTAINS_FILTER = 'contains'.freeze
  NOT_CONTAINS_FILTER = 'notContains'.freeze
  EQUALS_FILTER = 'equals'.freeze
  NOT_EQUAL_FILTER = 'notEqual'.freeze
  STARTS_WITH_FILTER = 'startsWith'.freeze
  ENDS_WITH_FILTER = 'endsWith'.freeze
  GREATER_THAN_FILTER = 'greaterThan'.freeze
  GREATER_THAN_OR_EQUAL_FILTER = 'greaterThanOrEqual'.freeze
  LESS_THAN_FILTER = 'lessThan'.freeze
  LESS_THAN_OR_EQUAL_FILTER = 'lessThanOrEqual'.freeze
  REGEX_FILTER = 'regex'.freeze

  FILTERS = [CONTAINS_FILTER, NOT_CONTAINS_FILTER, EQUALS_FILTER, NOT_EQUAL_FILTER, STARTS_WITH_FILTER, ENDS_WITH_FILTER, GREATER_THAN_FILTER, GREATER_THAN_OR_EQUAL_FILTER, LESS_THAN_FILTER, LESS_THAN_OR_EQUAL_FILTER, REGEX_FILTER]

  def process_orphans(id)
    bulk_saving_answer = model.find id

    bulk_saving_answer.reload.answer_processings.created.pluck(:id).map do |answer_processing_id|
      save_answer_worker_class(bulk_saving_answer).perform_async Apartment::Tenant.current, answer_processing_id
    end

    bulk_saving_answer.reload.answer_processings.processing.pluck(:id).map do |answer_processing_id|
      save_answer_worker_class(bulk_saving_answer).perform_async Apartment::Tenant.current, answer_processing_id, true
    end

    @success = true
  end

  def validate_pks(business, answer_param)
    key_field_ids = business.key_field_ids + (business.parent.try(:key_field_ids) || [])

    return true if key_field_ids.empty?

    filtered = answer_param.map { |answer| answer.slice(*key_field_ids) }
    repeateds = filtered.tally.select { |_key, count| count > 1 }.keys

    raise I18n.t('answer_processing.duplicate_key_field_value', pk_labels: pk_labels(business, repeateds.first).join('|'), scope: 'activerecord.errors.models') if repeateds.present?
  end

  def pk_labels(business, data)
    business.key_fields.order('businesses_fields.created_at').map { |field| "#{field.label}: #{data[field.id]}" }
  end

  def fetch_answer_param
    @parameters.delete(:answers).reject { |answer_param| answer_param.blank? || answer_param.values.all?(&:blank?) }
  end

  def create
    ActiveRecord::Base.transaction do
      answer_params = fetch_answer_param

      record = model.new(@parameters.merge(start_at: Time.zone.now))

      validate_pks(record.step.business, answer_params)

      save_record(record)
      save_processing(record, answer_params) if success?
    end

    schedule_worker(record)
  rescue StandardError => e
    @errors = [e.message]
    @success = false
  end

  def update(id)
    ActiveRecord::Base.transaction do
      record = model.find id

      raise I18n.t('bulk_answer.no_updated_data', scope: 'activerecord.errors.services') unless record.answer_processings.exists?(status: %i[created failed])

      answer_params = fetch_answer_param

      validate_pks(record.step.business, answer_params)

      record.processing!

      update_processing(record, answer_params)
      save_record(record)
    end

    schedule_worker(record)
  rescue StandardError => e
    @errors = [e.message]
    @success = false
  end

  def schedule_worker(record)
    save_answer_worker_class(record).push_bulk(record.reload.answer_processings.created.pluck(:id)) do |processing_id|
      [Apartment::Tenant.current, processing_id]
    end
  end

  def bulk_alteration
    results = filter_by_criterions(@parameters[:business_id], @parameters[:criterions])

    if results.count == 0
      raise StandardError, "A validação falhou: Total de alterações. Para serem válidos, os critérios devem alterar ao menos um registro."
    end

    raise StandardError, 'A validação falhou: Total de alterações. Para serem válidos, os critérios devem alterar ao menos um registro.' if results.count == 0

    record = model.new(start_at: Time.zone.now, origin: :bulk_alteration, business_id: @parameters[:business_id], user_id: @parameters[:user_id], ip: @parameters[:ip])

    save_record(record)

    alteration_processing(record) if @success

  rescue BulkActionNotNullError => e
    @errors = [e.error_message]
    @success = false
  rescue StandardError => e
    @errors ||= []
    @errors << e.message
    @success = false
  end

  def bulk_alteration_preview
    results = filter_by_criterions(@parameters[:business_id], @parameters[:criterions])
    results.count
  end

  def pre_filled
    answers = @parameters[:answers]
    step = Step.includes(:fields).find(@parameters[:step_id])
    lines_to_fill = @parameters[:lines_to_fill].to_i - 1

    @success = true

    default_field_values = step.default_field_values

    answers[0..lines_to_fill].map do |answer_params|
      default_field_values.merge(answer_params)
    end
  rescue StandardError => e
    @errors = [e.message]
    @success = false
  end

  def save_processing(bulk_saving_answer, parameters)
    parameters.each do |answer_param|
      next if answer_param.blank?

      bulk_saving_answer.answer_processings.create!(data: answer_param)
    end
  end

  def update_processing(bulk_saving_answer, parameters)
    parameters.each do |answer_param|
      next if answer_param.blank?

      processing = bulk_saving_answer.answer_processings.where(status: %i[created failed]).find(answer_param.delete(:id))
      processing.update!(data: answer_param, status: :created)
    end
  end

  def alteration_processing(bulk_saving_answer)
    params = @parameters.slice(:bulk_action, :criterions, :alterations, :approvals, :verification_url, :validation_url)
    alteration_processing = AlterationProcessing.new(params.merge(bulk_saving_answer_id: bulk_saving_answer.id))

    raise BulkActionNotNullError if alteration_processing.bulk_action.blank?

    alteration_processing.save

    contents = filter_by_criterions(bulk_saving_answer.business_id, alteration_processing.criterions)

    alter_answer_worker_class.push_bulk(contents.pluck(:id)) do |content_id|
      [Apartment::Tenant.current, alteration_processing.id, content_id, @parameters[:user_id]]
    end
  end

  def filter_by_criterions(business_id, criterions)
    contents = Content.not_draft.joins(:answers).where(business_id: business_id)

    criterions.each_with_index do |criterion, index|
      criterion = criterion.with_indifferent_access if criterion.class != ActionController::Parameters

      case criterion[:field_id]
      when CONTENT_ID_FIELD
        contents = contents.where(id: criterion[:value])
      when PARENT_CONTENT_ID_FIELD
        contents = contents.where(parent_id: criterion[:value])
      else
        field = Field.find_by(id: criterion[:field_id])

        next if field.blank?

        join_condition = ActiveRecord::Base.sanitize_sql_array([
          "INNER JOIN content_values content_value? ON (content_value?.content_id = contents.id AND content_value?.field_id = ?)",
          index, index, index, criterion[:field_id]
        ])

        contents = contents.joins(join_condition)

        sql_clause = sql_clause(field, index, criterion)

        contents = contents.where(sql_clause)
      end
    end

    contents.select(:id).distinct
  end

  private

  def sql_clause(field, index, criterion)

    return '1 != 1' if returns_empty_scope?(criterion, field)

    clause_base = "content_value#{index}.value"

    case criterion[:operator]
    when CONTAINS_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}::VARCHAR ILIKE ?", "%#{criterion[:value]}%"])
    when NOT_CONTAINS_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}::VARCHAR NOT ILIKE ?", "%#{criterion[:value]}%"])
    when STARTS_WITH_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}::VARCHAR ILIKE ?", "#{criterion[:value]}%"])
    when ENDS_WITH_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}::VARCHAR ILIKE ?", "%#{criterion[:value]}"])
    when EQUALS_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} = ?", Time.zone.parse(criterion[:value]).to_date]) if field.date? && criterion[:value].present?
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} = ?", criterion[:value]]) if (field.integer? || field.decimal?) && criterion[:value].present?

      sql_condition = if criterion[:value].blank?
        "#{clause_base} IS NULL OR #{clause_base} = ''"
      else
        "#{clause_base} = ?"
      end
      ActiveRecord::Base.sanitize_sql_array([sql_condition, criterion[:value]])
    when NOT_EQUAL_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} != ?", Time.zone.parse(criterion[:value]).to_date]) if field.date? && criterion[:value].present?
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} != ?", criterion[:value]]) if (field.integer? || field.decimal?) && criterion[:value].present?

      sql_condition = if criterion[:value].blank?
        "#{clause_base} IS NOT NULL AND #{clause_base} != ''"
      else
        "#{clause_base} != ?"
      end
      ActiveRecord::Base.sanitize_sql_array([sql_condition, criterion[:value]])
    when GREATER_THAN_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} > ?", Time.zone.parse(criterion[:value]).to_date]) if field.date?

      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} > ?", criterion[:value]]) if field.integer? || field.decimal?
    when GREATER_THAN_OR_EQUAL_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} >= ?", Time.zone.parse(criterion[:value]).to_date]) if field.date?

      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} >= ?", criterion[:value]]) if field.integer? || field.decimal?
    when LESS_THAN_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} < ?", Time.zone.parse(criterion[:value]).to_date]) if field.date?

      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} < ?", criterion[:value]]) if field.integer? || field.decimal?
    when LESS_THAN_OR_EQUAL_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} <= ?", Time.zone.parse(criterion[:value]).to_date]) if field.date?

      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}#{cast_operator_for_field(field, criterion)} <= ?", criterion[:value]]) if field.integer? || field.decimal?
    when REGEX_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{clause_base}::VARCHAR ~* ?", criterion[:value]])
    end
  end

  def cast_operator_for_field(field, criterion)
    return '::INTEGER' if field.integer?
    return '::FLOAT' if field.decimal?
    return '::DATE' if field.date?

    ''
  end

  def returns_empty_scope?(criterion, field)
    FILTERS.exclude?(criterion[:operator]) ||
    !criterion[:value].present? &&  [EQUALS_FILTER, NOT_EQUAL_FILTER].exclude?(criterion[:operator]) ||
    !(field.decimal? || field.integer? || field.date?) && [GREATER_THAN_FILTER, GREATER_THAN_OR_EQUAL_FILTER, LESS_THAN_FILTER, LESS_THAN_OR_EQUAL_FILTER].include?(criterion[:operator]) ||
    !valid_regexp?(criterion)
  end

  def valid_regexp?(criterion)
    if criterion[:operator] == REGEX_FILTER
      begin
        Regexp.new(criterion[:value])
      rescue RegexpError
        return false
      end
    end

    true
  end

  def save_answer_worker_class(bulk_saving_answers)
    jobs_to_process = bulk_saving_answers.answer_processings.count

    return SaveAnswerStaffHighPriorityWorker if fourmdg_user? && jobs_to_process < MAXIMUM_RECORDS_FOR_STAFF_USER
    return SaveAnswerStaffLowPriorityWorker if fourmdg_user?
    return SaveAnswerHighPriorityWorker if jobs_to_process < MAXIMUM_RECORDS_FOR_USER

    SaveAnswerLowPriorityWorker
  end

  def alter_answer_worker_class
    jobs_to_process = bulk_alteration_preview

    return BulkAlterationStaffHighPriorityWorker if fourmdg_user? && jobs_to_process < MAXIMUM_RECORDS_FOR_STAFF_USER
    return BulkAlterationStaffLowPriorityWorker if fourmdg_user?
    return BulkAlterationHighPriorityWorker if jobs_to_process < MAXIMUM_RECORDS_FOR_USER

    BulkAlterationLowPriorityWorker
  end

  def fourmdg_user?
    User.find_by(id: @parameters[:user_id]).try(:email).to_s.ends_with?('@4mdg.com.br')
  end

  def queue_size(queue, tenant)
    Sidekiq::Queue.new(queue).select { |job| job.args[0] == tenant }.count
  end
end
