module Aws
  class WafService
    attr_reader :company

    CLOUDFRONT = 'CLOUDFRONT'.freeze
    EXACTLY = 'EXACTLY'.freeze
    LOWERCASE = 'LOWERCASE'.freeze
    NONE = 'NONE'.freeze
    RULE_NAME = 'block-not-trusted-ips'.freeze
    STARTS_WITH = 'STARTS_WITH'.freeze

    MAX_RETRIES = 3
    WAIT_TIME = 0.5.second

    FORBIDDEN_PAGE_PATH = Rails.root.join('public/403.html')

    def initialize(company)
      @company = company
      @retries = 0
    end

    def update_allowed_ips
      return if @retries >= MAX_RETRIES
      if company.restrict_access_by_ip?
        company.waf_ip_set_id.blank? ? create_ip_set : update_ip_set

        company.waf_rule_group_id.blank? ? create_rule_group : update_rule_group

        company.reload
        update_web_acl_rules if company.waf_ip_set_id.present? && company.waf_rule_group_id.present?
      else
        company.waf_rule_group_id.blank? ? Rails.logger.warn("[WAFSERVICE] Company #{company.subdomain} does not have a rule group id...skipping deletion") : delete_rule_group

        company.waf_ip_set_id.blank? ? Rails.logger.warn("[WAFSERVICE] Company #{company.subdomain} does not have an ip set id...skipping deletion") : delete_ip_set
      end
    rescue Aws::WAFV2::Errors::WAFUnavailableEntityException
      @retries += 1

      Rails.logger.warn("[WAFSERVICE][Aws::WAFV2::Errors::WAFUnavailableEntityException] Retry: #{@retries}")

      update_allowed_ips
    rescue Aws::WAFV2::Errors::WAFNonexistentItemException => e
      company.update_columns(waf_rule_group_id: nil, waf_ip_set_id: nil)

      Rails.logger.error("[WAFSERVICE][Aws::WAFV2::Errors::WAFNonexistentItemException] #{e.message}")
    rescue StandardError => e
      Rails.logger.error("[WAFSERVICE][#{e.class}] #{e.message}")
    end

    private

    def wait_aws_cooldown
      sleep(WAIT_TIME)
    end

    def client
      @client ||= Aws::WAFV2::Client.new(region: Rails.application.credentials.aws_region, access_key_id: Rails.application.credentials['S3_ACCESS_KEY_ID'], secret_access_key: Rails.application.credentials['S3_SECRET_ACCESS_KEY'])
    end

    def subdomain_with_environment
      "#{company.subdomain}-#{Rails.env}"
    end

    def rule_host
      host = [company.subdomain]

      host << Rails.env unless Rails.env.production? || Rails.env.academy?
      host << (Rails.env.academy? ? 'mdmacademy.com.br' : '4mdg.com.br')

      host.join('.')
    end

    def tags
      [{ key: 'Stack', value: Rails.env }, { key: 'Name', value: "4mdg-#{Rails.env}" }]
    end

    def fetch_ip_set
      wait_aws_cooldown

      client.get_ip_set(name: subdomain_with_environment, scope: CLOUDFRONT, id: company.waf_ip_set_id)
    end

    def create_ip_set
      wait_aws_cooldown

      response = client.create_ip_set(
        name: subdomain_with_environment, scope: CLOUDFRONT,
        description: "IP set for #{company.subdomain}, #{Rails.env} environment",
        ip_address_version: "IPV4", addresses: company.allowed_ips_cidr.compact,
        tags: tags
      )

      company.update_column(:waf_ip_set_id, response.summary.id)
    end

    def update_ip_set
      response = fetch_ip_set

      wait_aws_cooldown

      client.update_ip_set(
        name: subdomain_with_environment, scope: CLOUDFRONT, id: company.waf_ip_set_id,
        addresses: company.allowed_ips_cidr.compact, lock_token: response.lock_token
      )
    end

    def delete_ip_set
      response = fetch_ip_set

      wait_aws_cooldown

      client.delete_ip_set(name: subdomain_with_environment, scope: CLOUDFRONT, id: company.waf_ip_set_id, lock_token: response.lock_token)

      company.update_column(:waf_ip_set_id, nil)
    end

    def rule_group_rules
      [
        {
          name: "#{RULE_NAME}-#{company.subdomain}-#{Rails.env}", priority: 0,
          statement: {
            and_statement: {
              statements: [
                {
                  byte_match_statement: {
                    search_string: rule_host,
                    field_to_match: { single_header: { name: 'host' } },
                    text_transformations: [{ priority: 0, type: NONE }],
                    positional_constraint: EXACTLY
                  }
                },
                { not_statement: { statement: { ip_set_reference_statement: { arn: company.waf_ip_set_arn } } } }
              ]
            }
          },
          action: { block: { custom_response: { response_code: 403, custom_response_body_key: "#{subdomain_with_environment}-response-key" } } },
          visibility_config: { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "#{RULE_NAME}-#{company.subdomain}-#{Rails.env}" }
        }
      ]
    end

    def rule_group_visibility_config
      { sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: "rule-group-#{company.subdomain}-#{Rails.env}" }
    end

    def fetch_rule_group
      wait_aws_cooldown

      client.get_rule_group(name: subdomain_with_environment, scope: CLOUDFRONT, id: company.waf_rule_group_id)
    end

    def create_rule_group
      wait_aws_cooldown

      response = client.create_rule_group(
        name: subdomain_with_environment, scope: CLOUDFRONT,
        capacity: 3, description: "Rule group for #{company.subdomain}, #{Rails.env} environment",
        rules: rule_group_rules, visibility_config: rule_group_visibility_config, tags: tags,
        custom_response_bodies: {
          "#{subdomain_with_environment}-response-key" => { content_type: "TEXT_HTML", content: Aws::WafService::FORBIDDEN_PAGE_PATH.read }
        }
      )

      company.update_column(:waf_rule_group_id, response.summary.id)
    end

    def update_rule_group
      response = fetch_rule_group

      wait_aws_cooldown

      client.update_rule_group(
        name: subdomain_with_environment, scope: CLOUDFRONT, id: company.waf_rule_group_id,
        rules: rule_group_rules, visibility_config: rule_group_visibility_config, lock_token: response.lock_token,
        custom_response_bodies: {
          "#{subdomain_with_environment}-response-key" => { content_type: "TEXT_HTML", content: Aws::WafService::FORBIDDEN_PAGE_PATH.read }
        }
      )
    end

    def delete_rule_group
      response = fetch_web_acl

      web_acl = response.web_acl

      rules = web_acl.rules

      index = rules.find_index { |rule| rule.name == subdomain_with_environment }

      if index
        rules.delete_at(index)

        rules = rules.map.with_index do |rule, index|
          rule.priority = index

          rule.to_h
        end
      end

      wait_aws_cooldown

      client.update_web_acl(
        name: Rails.application.credentials.waf_name, scope: CLOUDFRONT, id: Rails.application.credentials.waf_id,
        rules: rules, default_action: web_acl.default_action.to_h, visibility_config: web_acl.visibility_config.to_h,
        lock_token: response.lock_token
      )

      response = fetch_rule_group

      wait_aws_cooldown

      client.delete_rule_group(name: subdomain_with_environment, scope: CLOUDFRONT, id: company.waf_rule_group_id, lock_token: response.lock_token)

      company.update_column(:waf_rule_group_id, nil)
    end

    def configure_rules(rules)
      index = rules.find_index { |rule| rule.name == subdomain_with_environment }

      if index
        rule = rules[index]

        rules[index] = Aws::WAFV2::Types::Rule.new(
          name: rule.name, priority: rule.priority,
          statement: Aws::WAFV2::Types::Statement.new(rule_group_reference_statement: { arn: company.waf_rule_group_arn }),
          override_action: rule.override_action, visibility_config: rule.visibility_config
        )
      else
        rules.prepend(
          Aws::WAFV2::Types::Rule.new(
            name: subdomain_with_environment, priority: -1,
            statement: Aws::WAFV2::Types::Statement.new(rule_group_reference_statement: { arn: company.waf_rule_group_arn }),
            override_action: Aws::WAFV2::Types::OverrideAction.new(count: nil, none: Aws::WAFV2::Types::NoneAction.new),
            visibility_config: Aws::WAFV2::Types::VisibilityConfig.new(sampled_requests_enabled: true, cloud_watch_metrics_enabled: true, metric_name: subdomain_with_environment)
          )
        )
      end

      rules.map do |rule|
        rule.priority += 1 unless index.present?

        rule.to_h
      end
    end

    def fetch_web_acl
      wait_aws_cooldown

      client.get_web_acl(name: Rails.application.credentials.waf_name, scope: "CLOUDFRONT", id: Rails.application.credentials.waf_id)
    end

    def update_web_acl_rules
      response = fetch_web_acl

      web_acl = response.web_acl

      data = web_acl.to_h.except(:arn, :capacity, :managed_by_firewall_manager, :retrofitted_by_firewall_manager, :label_namespace, :description).merge(scope: 'CLOUDFRONT', lock_token: response.lock_token)

      data[:rules] = configure_rules(web_acl.rules)
      client.update_web_acl(data)
    end
  end
end
