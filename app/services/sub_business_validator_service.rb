class SubBusinessValidatorService
  attr_reader :errors

  def initialize
    # @errors it's an array of hashes, where each hash contains the content_id and the errors
    @errors = []
    @permission_validation_service = StepValidators::PermissionValidator.new
  end

  def validate(answer, current_user, parents = {})
    current_user = User.find_by(id: current_user)
    sub_business_ids = extract_sub_business_ids(answer)
    return if sub_business_ids.blank? || !answer.content.business.validate_fields

    sub_answers = fetch_sub_answers(answer, sub_business_ids)
    sub_answers.each { |sub_answer| validate_sub_answer(answer, sub_answer, parents, current_user) }
  end

  private

  def extract_sub_business_ids(answer)
    return nil unless answer&.step

    fields = answer.step.fields
                   .where(validate_sub_business_contents: true, type: :sub_business)
                   .pluck(:reference_sub_business_id)

    fields.presence
  end

  def fetch_sub_answers(answer, sub_business_ids)
    Answer.joins(:content).where(
      content: {
        business_id: sub_business_ids,
        parent_id: answer.content_id,
        draft: false,
        deleted_at: nil
      }
    )
  end

  def validate_sub_answer(answer, sub_answer, parents, current_user)
    sub_errors = []

    sub_errors.concat(@permission_validation_service.errors) unless @permission_validation_service.valid?(sub_answer.step_id, answer.user_id)

    business_validation_rules_runner = BusinessValidationRulesRunner.new(sub_answer, current_user, parents)

    sub_errors.concat(business_validation_rules_runner.errors) if business_validation_rules_runner.errors.present?

    sub_answer.current_user = current_user
    sub_errors.concat(sub_answer.errors.full_messages) unless sub_answer.valid?

    add_errors_if_present(sub_answer, sub_errors)
  end

  def add_errors_if_present(sub_answer, sub_errors)
    @errors << { content_id: sub_answer.content_id, errors: sub_errors } if sub_errors.present?
  end
end
