class AuditorshipService < ApplicationService
  def change_id_to_label_or_name_of_record(record)
    model_mappings = {
      'template_id' => Template,
      'step_id' => Step, 'parent_step_id' => Step, 'step_for_revision_id' => Step,
      'business_id' => Business, 'reference_business_id' => Business,
      'business_group_id' => BusinessGroup,
      'reference_value_field_id' => Field, 'reference_field_id' => Field, 'field_id' => Field, 'parent_field_id' => Field,
      'department_id' => Department
    }

    ids_to_fetch = Hash.new { |hash, key| hash[key] = [] }

    record.object_changes.each do |key, value|
      if model_mappings.key?(key)
        ids_to_fetch[model_mappings[key]] += value.compact
      end
    end

    loaded_records = {}
    ids_to_fetch.each do |model, ids|
      loaded_records[model] = model.where(id: ids.uniq).index_by(&:id)
    end

    record.object_changes.each do |key, value|
      if model_mappings.key?(key)
        record.object_changes[key] = value.map { |id| id.nil? ? nil : loaded_records[model_mappings[key]][id].try(:name) || loaded_records[model_mappings[key]][id].try(:label) }
      elsif ['type', 'operator'].include?(key)
        record.object_changes[key] = value.map { |type| type.nil? ? nil : I18n.t("auditorship.item_type.fieldvalidation.#{type}", default: type) }
      end
    end

    if record.item_type == 'DependentFieldRule'
      formated_rules = format_rules_auditorship(record)

      record.object_changes['rules'] = []
      record.object_changes['rule_actions'] = []

      record.object_changes['rules'] << formated_rules[:rules].first
      record.object_changes['rules'] << formated_rules[:rules].last

      record.object_changes['rule_actions'] << formated_rules[:rule_actions].first
      record.object_changes['rule_actions'] << formated_rules[:rule_actions].last
    end
  end

  def additional_information(record)
    return {} if record.nil?
    item_type = record.item_type
    item_record = item_type.constantize.where(id: record.item_id)&.first

    case item_type
    when 'DependentFieldRule', 'Step', 'BusinessHeader', 'ShowOnListField'
      type = 'business_id'
      business_id = item_record&.business_id || get_item_id_from_papertrail(record.item_id, type)
      business = Business.find(business_id) if business_id
      { business_name: business&.name }
    when 'DependentReferenceField'
      { business_name: item_record&.step&.business&.name }
    when 'FieldValidation'
      type = 'field_id'
      field_id = item_record&.id || get_item_id_from_papertrail(record.item_id, type)
      template_name = Field.includes(:template).find_by(id: field_id)&.template&.name if field_id
      { template_name: template_name }
    when 'StepPermission', 'StepTemplate'
      type = 'step_id'
      step_id = item_record&.step&.id || get_item_id_from_papertrail(record.item_id, type)
      step = Step.find(step_id) if step_id
      { business_name: step&.business&.name, step_name: step&.name }
    else
      {}
    end
  end

  def get_item_id_from_papertrail(item_id, type)
    version = PaperTrail::Version.where(item_id: item_id).where("object IS NOT NULL AND object_changes IS NOT NULL").first
    version&.object_changes[type]&.find {|v_id| v_id.present?} || version&.object[type]
  end

  def format_rules_auditorship(record)
    old_meaning_rules = ""
    old_meaning_rule_actions = ""

    meaning_rules = ""
    meaning_rule_actions = ""

    rule_type = DependentFieldRule.find_by(id: record.item_id)&.rule_type
    return if rule_type.nil?

    if record.object.present?
      old_meaning_rules += create_rules_message(record.object['rules'])
      old_meaning_rule_actions += create_rule_actions_message(record.object['rule_actions'], rule_type)
    end

    meaning_rules += create_rules_message(record.object_changes['rules']&.second)
    meaning_rule_actions += create_rule_actions_message(record.object_changes['rule_actions']&.second, rule_type)

    { rules: [old_meaning_rules, meaning_rules], rule_actions: [old_meaning_rule_actions, meaning_rule_actions] }
  end

  def create_rules_message(rules)
    message = ""
    return message if rules.blank?
    if rules.key?('rules')
      message += "(#{create_condition_set_message(rules)})"
    else
      message += create_condition_message(rules)
    end
  end

  def create_condition_set_message(condition_set)
    messages = []
    condition_set['rules'].each do |rule|
      messages << create_rules_message(rule)
    end
    messages.join(" #{I18n.t("auditorship.item_type.dependentfieldrule.rule.#{condition_set['condition']}")} ")
  end

  def create_condition_message(condition)
    field = Field.find(condition['field'].split(':').last)
    "#{field.label} #{I18n.t("auditorship.item_type.dependentfieldrule.rule.#{condition['operator']}")} #{condition['value']}"
  end

  def create_rule_actions_message(rule_actions, rule_type)
    messages = []
    rule_actions&.each do |rule_action|
      messages << create_rule_action_message(rule_action, rule_type)
    end
    messages.join(", ").strip
  end

  def create_rule_action_message(rule_action, rule_type)
    field = Field.find(rule_action['field'].split(':').last) if rule_action['field'].present?

    field_value = if rule_action['value'].is_a?(Array)
        rule_action['value'].map {|value| value['label']}
      else
        rule_action['value']
      end

    case rule_type
    when 'dependent'
      "#{field.label} #{I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.filled_with")} '#{field_value}' #{structural_modification(rule_action)}"
    when 'validation'
      "#{field.label} #{I18n.t("auditorship.item_type.dependentfieldrule.rule_action.validate.#{rule_action['operator']}")} '#{field_value}' #{I18n.t("auditorship.item_type.dependentfieldrule.rule_action.validate.with_error")} '#{rule_action['error_message']}'"
    when 'access_control'
      "#{rule_action['target']["class"].constantize.find(rule_action['target']['id'])&.name} #{rule_action['can_access'] ? I18n.t("auditorship.item_type.dependentfieldrule.rule_action.access_control.can_access") : I18n.t("auditorship.item_type.dependentfieldrule.rule_action.access_control.cannot_access")}"
    end
  end

  def structural_modification(rule_action)
    [
      rule_action['field_hidden'].present? ? (rule_action['field_hidden'] ? I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.hidden") : I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.visible")) : nil,
      rule_action['field_disabled'].present? ? (rule_action['field_disabled'] ? I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.disabled") : I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.enabled")) : nil,
      rule_action['field_required'].present? ? (rule_action['field_required'] ? I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.required") : I18n.t("auditorship.item_type.dependentfieldrule.rule_action.dependent.optional")) : nil
    ].compact.join(", ")
  end
end
