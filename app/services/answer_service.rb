require 'base64'

class AnswerService
  attr_reader :success, :errors, :record, :sub_business_errors

  def initialize(parameters = {})
    @new_answer_values = {}
    @remote_ip = parameters.delete('remote_ip')

    @required_fields = parameters.delete('required_fields')

    step_authorizer_email = parameters.delete('step_authorizer_email')

    @parents = parameters.delete('parents')

    @step_authorizer_id = User.find_by(email: step_authorizer_email)&.id

    @parameters = parameters.merge(step_authorizer_id: @step_authorizer_id)
    @origin = parameters.delete('origin')

    @current_user = parameters['user_id']

    @skip_business_validations = parameters.delete('skip_business_validations')
    @skip_field_validations = parameters.delete('skip_field_validations')
    @skip_external_url_validator = parameters.delete('skip_verification_url') || false
    @upload_base64_files = parameters.delete('upload_base64_files') || false

    @skip_mandatory_fields = parameters.delete('skip_mandatory_fields') || false
    @skip_business_rules = parameters.delete('skip_business_rules') || false
    @skip_validation_rules = parameters.delete('skip_validation_rules') || false
    @skip_webhook = parameters.delete('skip_webhook') || false
    @steps_to_change_ids = parameters.delete('steps_to_change_ids') || nil

    @errors = []
    @sub_business_errors = []
  end

  def validate(_answer_id)
    @success = true
  end

  def update(answer_id)
    ActiveRecord::Base.transaction do
      answer = Answer.find(answer_id)

      answer.filled_at = Time.zone.now
      answer.first_fill_at = Time.zone.now if answer.first_fill_at.blank?
      answer.user_id = @parameters['user_id'] || @parameters[:user_id]
      answer.created_by_id = @parameters['user_id'] || @parameters[:user_id] unless answer.created_by_id
      answer.last_update_ip = @remote_ip
      answer.step_authorizer_id = @step_authorizer_id
      answer.authorizer_token = nil
      answer.skip_field_validator = true
      answer.skip_external_url_validator = @skip_external_url_validator
      answer.skip_mandatory_fields = @skip_mandatory_fields
      answer.skip_webhook = @skip_webhook
      answer.origin = @origin
      answer.current_user = @current_user
      answer.steps_to_change_ids = @steps_to_change_ids

      valid_files?(answer)

      answer.data['values'] = missing_fields(answer, extract_values(answer))

      valid?(answer, @parameters['user_id'] || @parameters[:user_id])

      unless @skip_field_validations
        sub_business_validation_service = SubBusinessValidatorService.new

        sub_business_validation_service.validate(answer, @current_user, @parents)
        raise ActiveRecord::Rollback if sub_business_validation_service.errors.present? || !@errors.empty?
      end

      save_answer(answer)
    rescue StandardError => e
      @success = false

      if sub_business_validation_service&.errors.present?
        @sub_business_errors = sub_business_validation_service.errors
        @errors << 'Verifique o preenchimento dos sub negócios'
      end

      if answer&.errors.present?
        @errors = answer.errors.full_messages
      else
        @errors << e.message unless e.message == 'ActiveRecord::Rollback'
      end
    end
  end

  def valid?(answer, user_id)
    valid_field_rules = valid_field_rules?(answer)
    valid_rules = valid_rules?(answer)
    valid_permission = valid_permission?(answer, user_id)

    valid_field_rules && valid_rules && valid_permission
  end

  def authorize(answer_id)
    answer = Answer.waiting_authorization.find_by(id: answer_id, content_id: @parameters['content_id'])

    unless answer
      @success = false
      @errors << I18n.t('answer.step_is_not_awaiting_approval', scope: 'activerecord.errors.services', step_name: Answer.find_by(id: answer_id, content_id: @parameters['content_id']).step.name)
      return
    end

    unless answer.requires_authorization?
      @success = false
      @errors << I18n.t('answer.step_requires_approval_but_no_authorizer', scope: 'activerecord.errors.services')
      return
    end

    unless answer.step.step_permissions.approvement.for_user(@parameters['authorizer_id']).exists?
      @errors << I18n.t('answer.user_cannot_approve_this_step', scope: 'activerecord.errors.services')
      @success = false
      return
    end

    answer.authorizer_id = @parameters['authorizer_id']
    answer.last_update_ip = @remote_ip
    answer.skip_external_url_validator = true

    if answer.authorize!
      @record = answer.reload
      @success = true
    else
      @success = false
      @errors = answer.errors.full_messages
    end
  end

  def reject(answer_id, current_user)
    answer = Answer.waiting_authorization.find_by(id: answer_id, content_id: @parameters['content_id'])

    unless answer
      @errors << I18n.t('answer.step_is_not_awaiting_approval', scope: 'activerecord.errors.services', step_name: Answer.find_by(id: answer_id, content_id: @parameters['content_id']).step.name)
      return @success = false
    end

    unless answer.requires_authorization?
      @errors << I18n.t('answer.step_requires_approval_but_no_authorizer', scope: 'activerecord.errors.services')
      return @success = false
    end

    unless answer.step.step_permissions.approvement.for_user(current_user.id).exists?
      @errors << I18n.t('answer.user_cannot_approve_this_step', scope: 'activerecord.errors.services')
      @success = false
      return
    end

    answer.last_update_ip = @remote_ip

    if answer.reject!
      @record = answer.reload
      @success = true
    else
      @success = false
      @errors = answer.errors.full_messages
    end
  end

  def revision(answer_id)
    answer = Answer.find(answer_id)

    if answer.step.step_for_revision_ids.exclude?(@parameters['step_id'])
      @success = false
      @errors << I18n.t('answer.cannot_review_from_this_step', scope: 'activerecord.errors.services')

      return
    end

    user = User.find_by(id: @parameters['user_id'])

    reviewed_answer = Answer.find_by(step_id: @parameters['step_id'], content_id: @parameters['content_id'])

    reviewed_answer.review
    reviewed_answer.review_requested_by_id = user.id
    reviewed_answer.last_update_ip = @remote_ip

    if reviewed_answer.save
      note = I18n.t('services.answer.review_request.message', date: I18n.l(Time.zone.now, format: :default), step: reviewed_answer.step.name, user: user.name).to_s
      note << "\r\n#{I18n.t('services.answer.review_request.note', note: @parameters['notes'])}" if @parameters['notes'].present?

      content = reviewed_answer.content
      content.current_answer_id = reviewed_answer.id

      content.status = Content.statuses[:under_review]

      content.concluded_at = nil

      content.note = "#{content.note}\r\n#{note}"
      content.save

      emails = get_emails_from_users_and_departments(reviewed_answer.step)

      if answer.step.send_email_to_user_who_registered?
        StepRevisionMailer.notify_user_who_registered(reviewed_answer, note).deliver_now
        emails.delete(User.kept.find_by(id: reviewed_answer.created_by_id)&.email)
      end
      StepRevisionMailer.notify_all_with_access(reviewed_answer, note, emails:).deliver_now if answer.step.send_email_to_all_with_access?

      @record = reviewed_answer.reload
      @success = true
    else
      @success = false
      @errors = reviewed_answer.errors.full_messages
    end
  end

  def in_changing(content_id, step_id)
    answer = Answer.find_by(content_id:, step_id:)

    if answer.blank?
      @success = false
      @errors << I18n.t('answer.answer_not_found', scope: 'activerecord.errors.services')
      return
    end

    if answer.content&.discarded?
      @success = false
      @errors << I18n.t('answer.content_discarded', scope: 'activerecord.errors.services')
      return
    end

    unless answer.done?
      @success = false
      @errors << I18n.t('answer.cannot_set_status_to_changing', scope: 'activerecord.errors.services')
      return
    end

    if answer.in_change!
      @success = true
      @record = answer.reload
    else
      @success = false
      @errors = answer.errors.full_messages
    end
  end

  def authorize_by_content_and_step_id(content_id, step_id, user_id)
    answer = Answer.find_by(content_id:, step_id:)

    unless answer && answer.waiting_authorization?
      @success = false
      @errors << I18n.t('answer.step_is_not_awaiting_approval', scope: 'activerecord.errors.services', step_name: Answer.find_by(id: answer.id, content_id:).step.name)
      return
    end

    unless answer.requires_authorization?
      @success = false
      @errors << I18n.t('answer.step_requires_approval_but_no_authorizer', scope: 'activerecord.errors.services')
      return
    end

    unless answer.step.step_permissions.approvement.for_user(user_id).exists?
      @errors << I18n.t('answer.user_cannot_approve_this_step', scope: 'activerecord.errors.services')
      @success = false
      return
    end

    answer.authorizer_id = user_id
    answer.last_update_ip = @remote_ip
    answer.skip_external_url_validator = true

    if answer.authorize!
      @record = answer.reload
      @success = true
    else
      @success = false
      @errors = answer.errors.full_messages
    end
  end

  def reject_by_content_and_step_id(content_id, step_id, user_id)
    answer = Answer.find_by(content_id:, step_id:)

    unless answer && answer.waiting_authorization?
      @errors << I18n.t('answer.step_is_not_awaiting_approval', scope: 'activerecord.errors.services', step_name: Answer.find_by(id: answer.id, content_id:).step.name)
      return @success = false
    end

    unless answer.requires_authorization?
      @errors << I18n.t('answer.step_requires_approval_but_no_authorizer', scope: 'activerecord.errors.services')
      return @success = false
    end

    unless answer.step.step_permissions.approvement.for_user(user_id).exists?
      @errors << I18n.t('answer.user_cannot_approve_this_step', scope: 'activerecord.errors.services')
      @success = false
      return
    end

    answer.last_update_ip = @remote_ip

    if answer.reject!
      @record = answer.reload
      @success = true
    else
      @success = false
      @errors = answer.errors.full_messages
    end
  end

  private

  def sanitize_join_for_content_query(business_id)
    ActiveRecord::Base.sanitize_sql_array([
                                            'JOIN contents ON contents.business_id = ? and contents.id = answers.content_id',
                                            business_id
                                          ])
  end

  def get_emails_from_users_and_departments(step)
    step.step_permissions.includes(:user, department: :users).map do |permission|
      if permission.user.present?
        permission.user.email
      else
        permission.department&.users&.kept&.map(&:email)
      end
    end.flatten.uniq
  end

  def extract_values(answer)
    other_fields = @parameters.keys.select do |key|
      field = answer.current_fields.find { |f| f.id == key }

      field.present?
    end
    sanitize_fields(answer)

    deleted_fields = answer.values.present? ? answer.values.reject { |field| @parameters.keys.include?(field) } : {}
    @parameters.slice(*other_fields).merge(deleted_fields).to_h
  end

  def sanitize_fields(answer)
    fields = Field.for_step(answer.step_id).select(:id, :type, :text_transformation).to_a

    fields.each do |field|
      next unless @parameters.keys.include?(field.id)

      @parameters[field.id] = @parameters[field.id].to_s if field.date? && !@parameters[field.id].is_a?(String)
      @parameters[field.id] = @parameters[field.id].try(:strip) || @parameters[field.id]
    end

    multiple_fields = fields.select { |f| f.type.to_sym == :multiple }
    multiple_fields.each do |field|
      next unless @parameters[field.id].is_a?(String)

      @parameters[field.id] = (@parameters[field.id].try(:split, ',') || []).map(&:strip)
    end

    text_fields = fields.select { |f| %i[text text_area multiple].include?(f.type.to_sym) }
    text_fields.each do |field|
      value = @parameters[field.id]

      next if value.blank?

      @parameters[field.id] = if value.is_a?(Array)
                                value.map { |val| transform_text(replace_text(val), field) }
                              else
                                transform_text(replace_text(value), field)
                              end
    end

    upload_files(answer)
  end

  def valid_files?(answer)
    @success = true
    @errors = []

    validator = FilesValidationService.new

    unless validator.valid?(answer, @parameters.to_h)
      @errors.concat(validator.errors)
      @success = false
    end

    @success
  end

  def valid_permission?(answer, user_id)
    permission_validator_service = StepValidators::PermissionValidator.new

    unless permission_validator_service.valid?(answer.step_id, user_id)
      @errors = permission_validator_service.errors
      return @success = false
    end

    true
  end

  def valid_field_rules?(answer)
    @success = true

    return @success if @skip_field_validations

    field_validator = AnswerValidators::FieldRulesValidator.new

    unless field_validator.valid?(answer)
      @errors.concat(field_validator.errors)
      return @success = false
    end

    @success
  end

  def valid_rules?(answer)
    @success = true
    rules_validation_service = BusinessValidationRulesRunner.new(answer, User.find_by(id: @current_user), @parents)

    return @success if @skip_business_validations || @skip_business_rules || @skip_validation_rules

    if rules_validation_service.errors.present?
      @errors.concat(rules_validation_service.errors)
      return @success = false
    end

    @success
  end

  def validate_field_rules(answer)
    initial_errors_amount = answer.errors.full_messages.length
    FieldValidator.new.validate(answer)

    initial_errors_amount == answer.errors.full_messages.length
  end

  def upload_files(answer)
    field_ids = Field.for_step(answer.step_id).where(type: :upload).pluck(:id)
    field_ids.each do |field_id|
      next if @parameters[field_id].blank?

      storage_service = StorageService.new

      @parameters[field_id] = [@parameters[field_id]].flatten.select(&:present?).map do |file|
        if file.is_a?(Hash)
          base64_file = Base64File.new(filename: file[:filename], data: file[:base64])

          storage_service.perform_base64(
            key: "#{Apartment::Tenant.current}/#{answer.content_id}/#{answer.id}/#{field_id}/#{file[:filename]}",
            file: base64_file
          )
        elsif file.is_a?(ActionDispatch::Http::UploadedFile)
          storage_service.perform(
            key: "#{Apartment::Tenant.current}/#{answer.content_id}/#{answer.id}/#{field_id}/#{file.original_filename}",
            file: file,
          )
        else
          file
        end
      end

      @parameters[field_id].uniq!
    end
  end

  def delete_attachments(answer)
    field_ids = Field.for_step(answer.step_id).where(type: :upload).pluck(:id)
    s3_provider = S3ProviderService.new

    field_ids.each do |id|
      files = s3_provider.list_files("#{Apartment::Tenant.current}/#{answer.content_id}/#{answer.id}/#{id}")
      files.to_a.reject { |file| answer.values[id]&.include?(file.public_url) }.each(&:destroy)
    end
  end

  def save_answer(answer)
    if for_review?
      if answer.save validate: false
        @record = answer.reload
        @success = true
      else
        @errors << answer.errors.full_messages
        @success = false

        raise ActiveRecord::Rollback
      end
    elsif answer.under_review?
      transition = :finish!
      transition = :reopen! if answer.requires_authorization?

      if answer.send(transition)
        user = User.find_by(id: @parameters['user_id'])

        note = I18n.t('services.answer.step_reviewed.message', date: I18n.l(Time.zone.now, format: :default), step: answer.step.name, user: user.name).to_s
        note << "\r\n#{I18n.t('services.answer.step_reviewed.note', note: @parameters['review_notes'])}" if @parameters['review_notes'].present?

        StepRevisionMailer.step_reviewed(answer, note).deliver

        content = answer.content

        content.update(note: "#{content.note}\r\n#{note}")

        @record = answer.reload
        @success = true
      else
        @errors = answer.errors.full_messages
        @success = false

        raise ActiveRecord::Rollback
      end
    else
      if !answer.waiting_authorization? || !answer.requires_authorization?
        transition = :finish!
        transition = (answer.pending? ? :wait_authorization! : :reopen!) if answer.requires_authorization?

        if answer.send(transition)
          AuthorizationMailer.notification_mail(answer).deliver if transition == :wait_authorization!

          @success = true
        else
          @errors = answer.errors.full_messages
          @success = false

          raise ActiveRecord::Rollback
        end
      elsif answer.save
        @success = true
      else
        @errors = answer.errors.full_messages
        @success = false
        raise ActiveRecord::Rollback
      end

      if @success == true
        answer.touch

        @record = answer.reload

        delete_attachments(answer)
      else
        @errors = answer.errors.full_messages
        raise ActiveRecord::Rollback
      end
    end
  end

  def data_replacement
    @data_replacement ||= DataReplacement.all
  end

  def replace_text(text)
    return text if data_replacement.empty?

    data_replacement.find_each do |record|
      text = text.split(/ /).map { |word| word.gsub(/^#{Regexp.escape(record.text)}$/i, record.replacement.to_s) }.join(' ')
    end

    text
  end

  def transform_text(text, field)
    return text.upcase if field.uppercase?
    return text.downcase if field.lowercase?
    return custom_titleize(text) if field.capitalize?

    text
  end

  def custom_titleize(text)
    text.split(/\s/).map(&:capitalize).join(' ')
  end

  def for_review?
    [true, 'true'].include?(@parameters['for_review'])
  end

  def missing_fields(answer, field_values)
    # Tentar remover das outras funções
    answer.step.fields.kept.select(:id).each_with_object(field_values) do |field, field_values|
      field_values[field.id] ||= ''
    end
  end
end
