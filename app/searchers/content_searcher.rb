class ContentSearcher
  DEFAULT_END_ROW = 25

  ASC = 'asc'.freeze

  STATUS_COLUMN = 'status'.freeze
  ID_COLUMN = 'id'.freeze
  CREATED_AT_COLUMN = 'created_at'.freeze
  UPDATED_AT_COLUMN = 'updated_at'.freeze
  CREATED_BY_NAME_COLUMN = 'created_by_name'.freeze

  TEXT_FILTER_TYPE = 'text'.freeze
  DATE_FILTER_TYPE = 'date'.freeze
  NUMBER_FILTER_TYPE = 'number'.freeze

  CONTAINS_FILTER = 'contains'.freeze
  NOT_CONTAINS_FILTER = 'notContains'.freeze
  EQUALS_FILTER = 'equals'.freeze
  NOT_EQUAL_FILTER = 'notEqual'.freeze
  STARTS_WITH_FILTER = 'startsWith'.freeze
  ENDS_WITH_FILTER = 'endsWith'.freeze
  GREATER_THAN_FILTER = 'greaterThan'.freeze
  GREATER_THAN_OR_EQUAL_FILTER = 'greaterThanOrEqual'.freeze
  LESS_THAN_FILTER = 'lessThan'.freeze
  LESS_THAN_OR_EQUAL_FILTER = 'lessThanOrEqual'.freeze
  IN_RANGE_FILTER = 'inRange'.freeze

  AND_OPERATOR = 'AND'.freeze
  OR_OPERATOR = 'OR'.freeze

  attr_reader :metadata

  def initialize(parameters = {})
    @parameters = parameters.with_indifferent_access

    @business_id = @parameters.delete(:business_id)
    @user = @parameters.delete(:user)
    @query = @parameters.delete(:query)
  end

  def search
    scope = base_scope
    if sort_params.empty?
      scope = scope.order(created_at: :desc)
    else
      sort_params.each_with_index do |sort_item, index|
        scope = sort_records(scope, sanitize_col_id(sort_item[:colId]), sort_item[:sort], index)
      end
    end

    scope.where!(status: search_params.dig('status', 'values')) if search_params.dig('status', 'values').present?
    search_params.delete('status')

    search_params.each_with_index { |(field_id, filter), index| scope = filter_records(scope, sanitize_col_id(field_id), filter, index) } unless search_params.empty?

    scope = scope.where('contents.id::text ilike :query or contents.keywords like lower(:query) or users.name ilike :query', query: "%#{@query}%") if @query.present?

    @metadata = { total: scope.reselect(:id).count }

    scope.limit(page_size).offset(start_row)
  end

  def search_detail(content_id)
    ContentValue
      .where(content_id:)
      .joins(:field, answer: :step, content: :business)
      .joins(<<~SQL.squish)
        INNER JOIN field_options ON
        field_options.field_id = content_values.field_id AND
        field_options.value = content_values.value AND
        field_options.content_id NOT IN (
          SELECT id FROM contents WHERE deleted_at IS NOT NULL
        )
      SQL
      .select(
        'steps.name AS step_name',
        'field_options.label AS value',
        'fields.label AS field_label',
        'field_options.content_id',
        'fields.reference_business_id AS business_id'
      )
      .map(&:attributes)
  end

  private

  def base_scope
    contents = Content.joins('LEFT JOIN users ON users.id = contents.created_by_id')
                      .joins('LEFT JOIN answers ON answers.content_id = contents.id')
                      .not_draft.where(business_id: @business_id).select(:id, :status, :created_at, :updated_at, :note, :current_answer_id, :deleted_at, :created_by_id, :business_id, :parent_id)

    if @user.present?
      access_control_service = AccessControlService.new(@business_id, @user, contents)
      contents = access_control_service.run
    end

    contents = contents.where(created_by_id: @user.id) if @user.try(:limited?)
    contents = contents.where(created_by_id: User.joins(:departments).where(departments: { id: @user.departments.select(:department_id) }).select(:id)) if @user.presence && @user.departments.any?(&:limited?)

    contents.distinct
  end

  def start_row
    @parameters[:startRow] || 0
  end

  def end_row
    @parameters[:endRow] || DEFAULT_END_ROW
  end

  def page_size
    end_row - start_row
  end

  def sort_params
    @parameters[:sort] || []
  end

  def search_params
    @parameters[:search] || {}
  end

  def sort_records(scope, field_id, sort_direction, index)
    nulls_order = sort_direction == ASC ? 'NULLS FIRST' : 'NULLS LAST'

    if field_id == STATUS_COLUMN
      scope = scope.order("contents.status #{sort_direction}, contents.keywords #{sort_direction}")

      scope = scope.select('contents.status, contents.keywords')
    elsif field_id == CREATED_BY_NAME_COLUMN
      scope = scope.order("users.name #{sort_direction}")

      scope = scope.select('users.name')
    elsif [CREATED_AT_COLUMN, UPDATED_AT_COLUMN].include?(field_id)
      scope = scope.order("contents.#{field_id} #{sort_direction}")

      scope = scope.select("contents.#{field_id}")
    else
      ordered_field = Field.find(field_id)

      scope = scope.joins(ActiveRecord::Base.sanitize_sql_array(["LEFT JOIN content_values content_value_order_#{index} on (content_value_order_#{index}.content_id = contents.id AND content_value_order_#{index}.field_id = ?)", field_id]))

      if ordered_field.reference_business_id
        scope = scope.joins(ActiveRecord::Base.sanitize_sql_array(["LEFT JOIN field_options fo2_#{index} on (fo2_#{index}.field_id = ? and fo2_#{index}.value = content_value_order_#{index}.value)", ordered_field.id]))
        scope = scope.joins(ActiveRecord::Base.sanitize_sql_array(["LEFT JOIN contents fo2_contents_#{index} on (fo2_#{index}.content_id = fo2_contents_#{index}.id and fo2_contents_#{index}.deleted_at IS NULL)"]))
        scope = scope.order("fo2_#{index}.label #{sort_direction} #{nulls_order}")
        scope = scope.select("fo2_#{index}.label")
      else
        scope = scope.order("content_value_order_#{index}.value #{sort_direction} #{nulls_order}")
        scope = scope.select("content_value_order_#{index}.value")
      end
    end

    scope
  end

  def filter_records(scope, field_id, filter, index)
    if field_id == ID_COLUMN
      scope = scope.where(sql_clause(filter, 'contents.id'))
    elsif field_id == CREATED_BY_NAME_COLUMN
      scope = scope.where(sql_clause(filter, 'users.name'))
    elsif [CREATED_AT_COLUMN, UPDATED_AT_COLUMN].include?(field_id)
      if filter[:operator].blank?
        scope = scope.where(sql_clause(filter, "contents.#{field_id}", true))
      else
        filter1 = filter[:condition1]
        filter2 = filter[:condition2]

        first_condition = sql_clause(filter1, "contents.#{field_id}", true)
        second_condition = sql_clause(filter2, "contents.#{field_id}", true)
        operator = filter[:operator]

        scope = scope.where(Arel::Nodes::InfixOperation.new(
          operator,
          Arel::Nodes::SqlLiteral.new(first_condition),
          Arel::Nodes::SqlLiteral.new(second_condition),
        ))
      end
    else
      scope = scope.joins(ActiveRecord::Base.sanitize_sql_array(["LEFT JOIN content_values content_value_filter_#{index} ON (content_value_filter_#{index}.content_id = contents.id AND content_value_filter_#{index}.field_id = ?)", field_id]))

      if filter[:operator].blank?
        scope = scope.where(sql_clause(filter, "content_value_filter_#{index}.value"))
      else
        filter1 = filter[:condition1]
        filter2 = filter[:condition2]

        first_condition = sql_clause(filter1, "content_value_filter_#{index}.value")
        second_condition = sql_clause(filter2, "content_value_filter_#{index}.value")

        scope = scope.where(Arel::Nodes::InfixOperation.new(
          "AND",
          Arel::Nodes::SqlLiteral.new(first_condition),
          Arel::Nodes::SqlLiteral.new(second_condition),
        ))
      end
    end

    scope
  end

  def sql_clause(filter, column, add_time_zone = false)
    case filter[:type]
    when CONTAINS_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{column} ILIKE ?", "%#{filter[:filter]}%"])
    when NOT_CONTAINS_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{column} NOT ILIKE ?", "%#{filter[:filter]}%"])
    when STARTS_WITH_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{column} ILIKE ?", "#{filter[:filter]}%"])
    when ENDS_WITH_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{column} ILIKE ?", "%#{filter[:filter]}"])
    when EQUALS_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["(#{column}::TIMESTAMP + INTERVAL '#{Time.zone.now.utc_offset / 1.hour} hours')::DATE = ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter) && add_time_zone
      return ActiveRecord::Base.sanitize_sql_array(["#{column}::DATE = ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter)
      return ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC = ?", filter[:filter]]) if number_filter?(filter)

      ActiveRecord::Base.sanitize_sql_array(["#{column} = ?", filter[:filter]])
    when NOT_EQUAL_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["(#{column}::TIMESTAMP + INTERVAL '#{Time.zone.now.utc_offset / 1.hour} hours')::DATE != ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter) && add_time_zone
      return ActiveRecord::Base.sanitize_sql_array(["#{column}::DATE != ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter)
      return ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC != ?", filter[:filter]]) if number_filter?(filter)

      ActiveRecord::Base.sanitize_sql_array(["#{column} != ?", filter[:filter]])
    when GREATER_THAN_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["(#{column}::TIMESTAMP + INTERVAL '#{Time.zone.now.utc_offset / 1.hour} hours')::DATE >= ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter) && add_time_zone
      return ActiveRecord::Base.sanitize_sql_array(["#{column}::DATE >= ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter)

      ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC > ?", filter[:filter]]) if number_filter?(filter)
    when GREATER_THAN_OR_EQUAL_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC >= ?", filter[:filter]]) if number_filter?(filter)
    when LESS_THAN_FILTER
      return ActiveRecord::Base.sanitize_sql_array(["(#{column}::TIMESTAMP + INTERVAL '#{Time.zone.now.utc_offset / 1.hour} hours')::DATE <= ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter) && add_time_zone
      return ActiveRecord::Base.sanitize_sql_array(["#{column}::DATE <= ?", Time.zone.parse(filter[:dateFrom]).to_date]) if date_filter?(filter)

      ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC < ?", filter[:filter]]) if number_filter?(filter)
    when LESS_THAN_OR_EQUAL_FILTER
      ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC <= ?", filter[:filter]]) if number_filter?(filter)
    when IN_RANGE_FILTER
      if date_filter?(filter)
        from = Time.zone.parse(filter[:dateFrom]).to_date
        to = Time.zone.parse(filter[:dateTo]).to_date

        add_time_zone ? ActiveRecord::Base.sanitize_sql_array(["(#{column}::TIMESTAMP + INTERVAL '#{Time.zone.now.utc_offset / 1.hour} hours')::DATE BETWEEN ? AND ?", from, to]) : ActiveRecord::Base.sanitize_sql_array(["#{column}::DATE BETWEEN ? AND ?", from, to])
      elsif number_filter?(filter)
        ActiveRecord::Base.sanitize_sql_array(["#{column}::NUMERIC BETWEEN ? AND ?", filter[:filter], filter[:filterTo]])
      end
    end
  end

  def date_filter?(filter)
    filter[:filterType] == DATE_FILTER_TYPE
  end

  def number_filter?(filter)
    filter[:filterType] == NUMBER_FILTER_TYPE
  end

  # if the same field appears more than once in the table columns, the frontend gives a suffix to the field_id: field_id_1
  def sanitize_col_id(col_id)
    return col_id unless col_id.match?(/_\d+\z/)

    col_id.split('_').first
  end
end
