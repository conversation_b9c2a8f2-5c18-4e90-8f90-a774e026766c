class DashboardSearcher
  include ActiveModel::Validations

  GREATER_THAN = 'gt'.freeze
  LESS_THAN = 'lt'.freeze
  EQUALS = 'eq'.freeze
  DEFAULT_PAGE_SIZE = 100
  ALL = 'all'.freeze
  DONE = 'done'.freeze
  PENDING = 'pending'.freeze
  UNDER_REVIEW = 'under_review'.freeze
  WAITING_AUTHORIZATION = 'waiting_authorization'.freeze
  REJECTED = 'rejected'.freeze
  CHANGING = 'changing'.freeze

  OPERATORS = {
    GREATER_THAN => '>',
    LESS_THAN => '<',
    EQUALS => '='
  }.freeze

  attr_reader :business_group_id, :business_id, :days_elapsed, :days_elapsed_search, :metadata

  validates :business_group_id, presence: true
  validates :business_id, presence: true
  validates :days_elapsed, presence: true, if: proc { days_elapsed_search.present? }
  validates :days_elapsed, numericality: { only_integer: true, greater_than_or_equal_to: 0 }, if: proc { days_elapsed.present? }
  validates :days_elapsed_search, presence: true, if: proc { days_elapsed.present? }
  validates :days_elapsed_search, inclusion: { in: [GREATER_THAN, LESS_THAN, EQUALS] }, if: proc { days_elapsed_search.present? }

  def initialize(parameters)
    @parameters = parameters

    @business_group_id = parameters.delete(:business_group_id)
    @business_id = parameters.delete(:business_id)
    @step_id = parameters.delete(:step_id)
    @from = parameters.delete(:from)
    @to = parameters.delete(:to)
    @concluded_at_from = parameters.delete(:concluded_at_from)
    @concluded_at_to = parameters.delete(:concluded_at_to)
    @status = parameters.delete(:status)
    @actives = [true, 'true'].include?(parameters.delete(:actives))
    @inactives = [true, 'true'].include?(parameters.delete(:inactives))
    @days_elapsed = parameters.delete(:days_elapsed)
    @days_elapsed_search = parameters.delete(:days_elapsed_search)
    @user_id = parameters.delete(:user_id)
    @created_by_id = parameters.delete(:created_by_id)
  end

  def search
    return if invalid?

    results = Content.not_sub_content.not_draft.joins(:business).where(business_id: @business_id, businesses: { business_group_id: @business_group_id }).includes(answers: %i[user authorizer])

    results = results.where('contents.created_at >= ?', Time.zone.parse(@from).beginning_of_day) if @from.present?
    results = results.where('contents.created_at <= ?', Time.zone.parse(@to).end_of_day) if @to.present?

    results = results.where('contents.concluded_at >= ?', Time.zone.parse(@concluded_at_from).beginning_of_day) if @concluded_at_from.present?
    results = results.where('contents.concluded_at <= ?', Time.zone.parse(@concluded_at_to).end_of_day) if @concluded_at_to.present?

    results =
      case @status
      when DONE
        results.done
      when PENDING
        results.pending
      when UNDER_REVIEW
        results.under_review
      when WAITING_AUTHORIZATION
        results.waiting_authorization
      when REJECTED
        results.rejected
      when CHANGING
        results.changing
      else
        results
      end

    results =
      if (@actives && @inactives) || (!@actives && !@inactives)
        results.with_discarded
      elsif @actives
        results.kept
      elsif @inactives
        results.with_discarded.discarded
      end

    results = results.joins(:answers).where(answers: { user_id: @user_id }).distinct if @user_id.present?
    results = results.joins(:answers).where(answers: { created_by_id: @created_by_id }).distinct if @created_by_id.present?

    operator = OPERATORS[@days_elapsed_search]
    results = results.where(ActiveRecord::Base.sanitize_sql_array(["(COALESCE(contents.concluded_at, ?)::date - (contents.created_at)::date) #{operator} ?", Date.today, @days_elapsed])) if @days_elapsed.present?

    results = results.joins(:current_answer).where(current_answer: { step_id: @step_id }) if @step_id.present?

    if @parameters[:current_user].limited?
      results = results.where(created_by_id: @parameters[:current_user].id)
    elsif @parameters[:current_user].try(:department).try(:limited?)
      results = results.where(created_by_id: @parameters[:current_user].department.users.pluck(:id))
    end

    results = add_obsolete_values_scope(results) if with_obsolete_values?

    results = results.paginate(page: page_param, per_page: per_page_param).order('contents.created_at DESC, contents.name ASC')

    @metadata = {
      page: page_param,
      perpage: per_page_param,
      total: results.count,
      pages: (results.count / per_page_param.to_i) + 1
    }

    results
  end

  private

  def page_param
    @parameters.try(:dig, :pagination, :page) || 1
  end

  def per_page_param
    @parameters.try(:dig, :pagination, :perpage) || DEFAULT_PAGE_SIZE
  end

  def with_obsolete_values?
    [true, 'true'].include?(@parameters[:with_obsolete_values])
  end

  def add_obsolete_values_scope(scope)
    reference_values = Arel::Table.new(:reference_values)

    contents = Content.arel_table
    content_values = ContentValue.arel_table
    fields = Field.arel_table
    field_options = FieldOption.arel_table

    cte_definition = content_values
                     .project('UNNEST(string_to_array(regexp_replace(value, \'[\["\]]\', \'\', \'ig\'), \', \')) as field', 'field_id')
                     .join(contents).on(contents[:id].eq(content_values[:content_id]))
                     .join(fields).on(fields[:id].eq(content_values[:field_id]))
                     .where(contents[:business_id].eq(@business_id))
                     .where(content_values[:value].not_eq(''))
                     .where(fields[:type].in([Field.types[:reference], Field.types[:multiple_reference]]))
    cte = Arel::Nodes::As.new(reference_values, cte_definition)

    query = reference_values.project('DISTINCT content_values.content_id')
                            .join(content_values).on(content_values[:field_id].eq(reference_values[:field_id]))
                            .join(fields).on(fields[:id].eq(content_values[:field_id]))
                            .where(content_values[:value].not_eq(''))
                            .where(fields[:type].in([Field.types[:reference], Field.types[:multiple_reference]]))
                            .where(
                              field_options
                                .project('1')
                                .join(contents).on(contents[:id].eq(field_options[:content_id]))
                                .where(field_options[:value].eq(reference_values[:field]))
                                .where(contents[:deleted_at].eq(nil))
                                .exists.not
                            )
                            .with(cte)

    content_ids = ContentValue.find_by_sql(query).pluck(:content_id)

    scope.where(id: content_ids)
  end
end
