class FieldSearcher
  def initialize(parameters = {})
    @parameters = parameters
  end

  def available_options_for_reference_field
    field = Field.find(@parameters[:id])

    field_options = FieldOption
      .joins('INNER JOIN contents ON contents.id = field_options.content_id')
      .where(field_id: @parameters[:id])

    if search_deleted_options?(field)
      field_options = field_options.where("contents.deleted_at IS NULL OR field_options.value = ?", @parameters[:value])
    else
      field_options = field_options.where("contents.deleted_at IS NULL")
    end

    field_options = field_options.where('field_options.label ilike :query or field_options.value ilike :query', query: "%#{@parameters[:q]}%") if @parameters[:q].present?

    field_options = field_options.offset(0)

    if @parameters[:parent_value].present? && @parameters[:parent_field_id].present?
      target_field = Field.select(:reference_business_id).find(@parameters[:id])
      parent_field = Field.select(:reference_value_field_id, :reference_business_id, :reference_value_use_key_fields).find(@parameters[:parent_field_id])

      field_options = field_options.for_dependent_parent_field(target_field, parent_field, @parameters[:parent_value])
    end

    if @parameters[:value].present?
      if field.multiple_reference?
        field_options = field_options.where("field_options.value = ?", @parameters[:value])
      else
        query_item = field_options.find_by(value: @parameters[:value])
      end
    end

    query = FieldOption.with(field_options: field_options).from('field_options').select('*')

    query = query.order("LENGTH(field_options.label)") if @parameters[:q].present?
    query = query.order(:label) unless @parameters[:q].present?

    query = query.limit(@parameters[:limit]) if @parameters[:limit]

    values = []

    if query_item.present? && query.exists?(label: query_item.label, value: query_item.value)
      query = query.where.not(label: query_item.label, value: query_item.value)
      values.push({ label: query_item.label, value: query_item.value, order: 1 }.stringify_keys)
    end

    query.each.with_index(values.count) do |option, index|
      values.push({ label: option.label, value: option.value, order: (index + 1) }.stringify_keys)
    end

    values
  end

  def results
    @fields = Field.kept
    @fields = @fields.for_step(@parameters[:step_id]) if @parameters[:step_id].present?
    @fields = @fields.for_business(@parameters[:business_id]) if @parameters[:business_id].present?
    @fields = @fields.for_business(@parameters[:business_ids]) if @parameters[:business_ids].present?
    @fields = @fields.where("label ilike :query", query: "%#{@parameters[:q]}%") if @parameters[:q].present?
    @fields = @fields.limit(@parameters[:limit]) if @parameters[:limit].present?

    uniq_labels_ids = @fields.select(:id, :label).uniq{ |f| f.label }.pluck(:id)
    @fields.where(id: uniq_labels_ids).order(:order)
  end

  def pk_fields_for_business
    return [] if @parameters[:business_id].blank?

    business = Business.find(@parameters[:business_id])

    results = []
    results = business.parent&.key_fields&.order('businesses_fields.created_at')&.to_a if business.sub_business && business.parent

    results += business.key_fields&.order('businesses_fields.created_at').to_a
    results
  end

  def search_deleted_options?(field)
    field.allow_to_select_deleted_option? && @parameters[:value].present?
  end
end
