class UpdateCloudwatchQueueStatisticsWorker
  include Sidekiq::Worker

  sidekiq_options retry: 0, queue: :statistics

  QUEUES = %w[save_answer_high_priority_worker save_answer_low_priority_worker save_answer_staff_high_priority_worker save_answer_staff_low_priority_worker elasticsearch_priority elasticsearch].freeze

  def perform
    return send_to_cloudwatch(cloudwatch_stats) if send_statistics

    Sidekiq.logger.info "--> UpdateCloudwatchQueueStatisticsWorker: #{cloudwatch_stats}"
  end

  def send_to_cloudwatch(statistics)
    client.put_metric_data statistics
  end

  def send_statistics
    !(Rails.env.development? || Rails.env.test?)
  end

  def client
    Aws::CloudWatch::Client.new(region: Rails.application.credentials.aws_region, access_key_id: Rails.application.credentials['S3_ACCESS_KEY_ID'], secret_access_key: Rails.application.credentials['S3_SECRET_ACCESS_KEY'])
  end

  def queue_size(name)
    Sidekiq::Queue.new(name).size
  end

  def cloudwatch_stats
    {
      namespace: 'Worker',
      metric_data: [
        {
          metric_name: 'SidekiqQueueSize',
          dimensions: [{ name: 'ClusterName', value: "fourmdg-#{Rails.env}" }],
          timestamp: Time.zone.now,
          value: QUEUES.reduce(0) { |sum, queue| sum + queue_size(queue) },
          unit: 'Count'
        }
      ]
    }
  end
end
