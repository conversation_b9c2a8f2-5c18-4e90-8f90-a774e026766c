class BusinessKeyFieldIndexWorker
  include Sidekiq::Worker

  sidekiq_options queue: :default, retry: 0

  def perform(tenant, sql_command, index_name)
    Apartment::Tenant.switch! tenant


    ActiveRecord::Base.connection_pool.with_connection do
      ActiveRecord::Base.connection.execute(ActiveRecord::Base.sanitize_sql_array(["DROP INDEX CONCURRENTLY IF EXISTS #{index_name}"]))
      ActiveRecord::Base.connection.execute(sql_command) if sql_command
    end
  end
end
