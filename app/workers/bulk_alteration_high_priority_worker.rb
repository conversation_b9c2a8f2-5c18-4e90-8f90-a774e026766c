class BulkAlterationHighPriorityWorker
  include Sidekiq::Worker

  sidekiq_options retry: 2, queue: :bulk_alteration_high_priority_worker

  def perform(tenant, alteration_processing_id, content_id, current_user_id)
    Apartment::Tenant.switch! tenant

    alteration_processing = AlterationProcessing.find(alteration_processing_id)
    begin
      alteration_processing.processing! unless alteration_processing.processing?
      alteration_processing.bulk_saving_answer.processing! unless alteration_processing.bulk_saving_answer.processing?

      save_alteration(alteration_processing, content_id, current_user_id)

      if alteration_processing.total_alterations == alteration_processing.reload.processed_count
        alteration_processing.done!

        update_bulk_saving_answer(alteration_processing)
      end
    rescue StandardError => e
      error_message = e.message.try(:gsub, "'", '') || e.message

      increment_error(alteration_processing.id, content_id, ["\"#{error_message}\""])

      alteration_processing.failed!

      update_bulk_saving_answer(alteration_processing)
    end
  end

  def update_bulk_saving_answer(alteration_processing)
    return if AlterationProcessing.exists?(bulk_saving_answer_id: alteration_processing.bulk_saving_answer_id, status: %i[created processing])

    alteration_processing.bulk_saving_answer.update(status: :done, end_at: Time.zone.now)
  end

  def save_alteration(alteration_processing, content_id, current_user_id)
    content = Content.find(content_id)

    case alteration_processing.bulk_action
    when 'updating'
      alteration_processing.alterations.group_by { |d| d['step_id'] }.each do |step_id, alterations|
        update_errors = []

        answer = content.answers.find_by(step_id: step_id)

        new_values = alterations.map { |alteration| { alteration['field_id'] => alteration['value'] } }.reduce({}, :merge)

        service = AnswerService.new(new_values.merge('content_id' => content.id, 'user_id' => alteration_processing.bulk_saving_answer.user_id, 'origin' => Answer.origins[:bulk_alteration]))

        service.update(answer&.id)

        update_errors += service.errors if service.errors

        if update_errors.empty?
          AlterationProcessing.increment_successes(alteration_processing.id)

          Sidekiq.logger.debug "UPDATING - SUCCESS content_id: #{content_id}"
        else
          complementary_information = "Etapa: #{answer.step.name} - "
          errors = update_errors.map { |error| complementary_information.concat(error) }

          increment_error(alteration_processing.id, content_id, errors)

          Sidekiq.logger.debug "UPDATING - ERROR content_id: #{content_id}"
        end
      end
    when 'inactivate'
      service = ContentService.new.tap { |service| service.destroy(content_id, { current_user: User.find(current_user_id) }) }

      if service.success
        AlterationProcessing.increment_successes(alteration_processing.id)

        Sidekiq.logger.debug "INACTIVATE - SUCCESS content_id: #{content_id}"
      else
        increment_error(alteration_processing.id, content_id, service.errors)

        Sidekiq.logger.debug "INACTIVATE - ERROR content_id: #{content_id}"
      end
    when 'form_enrichment'
      service = BulkAlterationFormEnrichmentService.new(alteration_processing, content_id, current_user_id)
      service.process

      if service.success
        AlterationProcessing.increment_successes(alteration_processing.id)

        Sidekiq.logger.debug "FORM ENRICHMENT - SUCCESS content_id: #{content_id}"
      else
        increment_error(alteration_processing.id, content_id, service.errors)

        Sidekiq.logger.debug "FORM ENRICHMENT - ERROR content_id: #{content_id}"
      end
    when 'approve'
      alteration_processing.approvals.each do |approval|
        content.answers.where(step_id: approval['step_id']).each do |answer|
          service = AnswerService.new('content_id' => content_id, 'authorizer_id' => alteration_processing.bulk_saving_answer.user_id, 'origin' => Answer.origins[:bulk_alteration])

          service.authorize(answer.id)

          if service.success
            AlterationProcessing.increment_successes(alteration_processing.id)

            Sidekiq.logger.debug "APPROVE - SUCCESS content_id: #{content_id}"
          else
            complementary_information = "Etapa: #{answer.step.name} - "
            errors = service.errors.map { |error| complementary_information.concat(error) }

            increment_error(alteration_processing.id, content_id, errors)

            Sidekiq.logger.debug "APPROVE - ERROR content_id: #{content_id}"
          end
        end
      end
    end

    AlterationProcessing.where(id: alteration_processing.id).update_all('"processed_count" = "processed_count" + 1')
    alteration_processing.save
  end

  protected

  def increment_error(alteration_processing_id, content_id, array_of_errors)
    AlterationProcessing.increment_error(alteration_processing_id, content_id, array_of_errors)
  end
end
