class CheckNotIndexedContentWorker
  include Sidekiq::Worker

  BATCH_SIZE = 500
  DAYS_TO_SEARCH = 1.year.ago.to_date

  sidekiq_options queue: :default, lock: :until_executed, lock_ttl: 6.hours

  def perform(tenant = nil)
    if tenant.blank?
      enqueue_for_all_tenants
    else
      process_tenant(tenant)
    end
  end

  private

  def service
    @service ||= ElasticSearcherService.new
  end

  def enqueue_for_all_tenants
    Company.where(use_elasticsearch: true).pluck(:subdomain).each { |subdomain| self.class.perform_async(subdomain) }
  end

  def process_tenant(tenant)
    return unless tenant_enabled_for_search?(tenant)

    Apartment::Tenant.switch!(tenant)
    process_businesses(tenant)
  end

  def tenant_enabled_for_search?(tenant)
    Company.find_by(subdomain: tenant)&.use_elasticsearch?
  end

  def process_businesses(tenant)
    Business.kept.where(integrate_elastic: true).find_in_batches(batch_size: BATCH_SIZE) do |batch|
      batch.each do |business|
        next unless business.contents.not_draft.exists?

        find_missing_content(business, tenant)
      end
    end
  end

  def find_missing_content(business, tenant)
    Content.unscoped.where(business: business)
      .where('contents.created_at >= ?', DAYS_TO_SEARCH)
      .not_draft.pluck(:id)
      .each_slice(BATCH_SIZE) do |ids|
        process_missing_content_batch(ids, business, tenant)
      end
  end

  def process_missing_content_batch(ids, business, tenant)
    response = search_in_elasticsearch(ids, tenant, business.id)
    return unless response && ids.size > response['hits']['total']['value']

    missing_ids = ids - response['hits']['hits'].map { |el| el['_id'] }
    enqueue_missing_content(missing_ids, tenant)
  end

  def search_in_elasticsearch(ids, tenant, business_id)
    index_name = "#{tenant}-#{business_id}"

    unless index_exists?(index_name)
      Rails.logger.warn "Elasticsearch index '#{index_name}' does not exist. Skipping search and enqueueing all content for indexing."
      enqueue_missing_content(ids, tenant)
      return nil
    end

    service.client.search(
      index: index_name,
      body: {
        query: { bool: { must: [{ terms: { '__CONTENT_ID.keyword': ids } }] } },
        fields: [], _source: false, size: BATCH_SIZE
      }
    )
  rescue Elastic::Transport::Transport::Errors::NotFound => e
    Rails.logger.warn "Elasticsearch index '#{index_name}' not found during search: #{e.message}"
    enqueue_missing_content(ids, tenant)
    nil
  rescue StandardError => e
    Rails.logger.error "Error searching in Elasticsearch for index '#{index_name}': #{e.message}"
    Rails.logger.error "Backtrace: #{e.backtrace.first(5).join("\n")}"
    nil
  end

  def enqueue_missing_content(ids, tenant)
    ids.each do |id|
      Elasticsearch::PutDocumentWorker.perform_async(tenant, id)
    end
  end

  def index_exists?(index_name)
    service.client.indices.exists?(index: index_name)
  rescue StandardError => e
    Rails.logger.error "Error checking if index '#{index_name}' exists: #{e.message}"
    false
  end
end
