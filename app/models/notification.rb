# == Schema Information
#
# Table name: notifications
#
#  id                  :uuid             not null, primary key
#  destiny_departments :uuid             default([]), is an Array
#  destiny_users       :uuid             default([]), is an Array
#  discarded_at        :datetime
#  dismissed_by        :uuid             default([]), is an Array
#  message             :text             not null
#  title               :string           not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  user_id             :uuid             not null
#
# Indexes
#
#  index_notifications_on_discarded_at  (discarded_at)
#  index_notifications_on_user_id       (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Notification < ApplicationRecord
  include Discard::Model
  belongs_to :user

  scope :without_destination, -> { where("destiny_users = '{}' AND destiny_departments = '{}'") }
  scope :for_user, ->(user_id) { where("? = ANY(destiny_users)", user_id) }
  scope :for_department, ->(departments) { where('destiny_departments @> ARRAY[?]::uuid[]', departments) }
  scope :not_dismissed, ->(user_id) { where.not('? = ANY(dismissed_by)', user_id) }

  validates :message, presence: true, allow_blank: false
  validates :title, presence: true, allow_blank: false
end
