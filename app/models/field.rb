# == Schema Information
#
# Table name: fields
#
#  id                             :uuid             not null, primary key
#  allow_add_new                  :boolean          default(FALSE)
#  allow_to_select_deleted_option :boolean          default(FALSE)
#  char_max_limit                 :integer
#  default_value                  :jsonb
#  deleted_at                     :datetime
#  enable_char_count              :boolean          default(FALSE), not null
#  enabled                        :boolean          default(TRUE)
#  height                         :integer          default("small")
#  input_variable                 :string
#  label                          :string           not null
#  options                        :json
#  order                          :integer          not null
#  output_variable                :string
#  reference_value_use_key_fields :boolean          default(FALSE)
#  required                       :boolean          default(FALSE)
#  show_on_form                   :boolean          default(FALSE)
#  size                           :integer          not null
#  text_transformation            :integer
#  tooltip                        :string
#  type                           :integer          not null
#  update_disabled                :boolean          default(FALSE)
#  validate_sub_business_contents :boolean
#  visible                        :boolean          default(TRUE)
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  reference_business_id          :uuid
#  reference_field_id             :uuid
#  reference_sub_business_id      :uuid
#  reference_value_field_id       :uuid
#  template_id                    :uuid
#
# Indexes
#
#  index_fields_on_deleted_at                 (deleted_at)
#  index_fields_on_reference_business_id      (reference_business_id)
#  index_fields_on_reference_field_id         (reference_field_id)
#  index_fields_on_reference_sub_business_id  (reference_sub_business_id)
#  index_fields_on_reference_value_field_id   (reference_value_field_id)
#  index_fields_on_template_id                (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (reference_business_id => businesses.id)
#  fk_rails_...  (reference_field_id => fields.id)
#  fk_rails_...  (reference_sub_business_id => businesses.id)
#  fk_rails_...  (reference_value_field_id => fields.id)
#  fk_rails_...  (template_id => templates.id)
#
class Field < ApplicationRecord
  include SoftDeletable
  include TranslateEnum
  include Orderable
  include Translatable
  include TranslatedAttribute

  UNPERMITED_FIELD_TYPES_FOR_CONDITIONS = %w[upload link sub_business].freeze
  UNPERMITED_FIELD_TYPES_FOR_ELASTICSEARCH = [:instruction].freeze

  attr_accessor :eligible_for_condition

  after_initialize :set_eligible_for_condition

  has_paper_trail ignore: %i[updated_at created_at id], on: %i[create update destroy]

  OPTIONS_JSON_SCHEMA = Rails.root.join('config', 'schemas', 'fields', 'options.json_schema').to_s.freeze

  MAX_CHARS = { 'text' => 1000, 'text_area' => 20_000 }.freeze
  MAX_CHARS_DEFAULT_VALUE = 524_288


  self.inheritance_column = :_type_disabled

  belongs_to :template, optional: false
  belongs_to :reference_sub_business, -> { unscoped.where(sub_business: true) }, class_name: 'Business', optional: true, inverse_of: false
  belongs_to :reference_business, class_name: 'Business', optional: true, inverse_of: false
  belongs_to :reference_field, class_name: 'Field', optional: true, inverse_of: false
  belongs_to :reference_value_field, class_name: 'Field', optional: true, inverse_of: false

  has_many :validations, class_name: 'FieldValidation', inverse_of: :field, dependent: :destroy
  has_many :field_options, dependent: :destroy
  has_many :show_on_list_fields, dependent: :destroy

  validates :type, :size, :label, presence: true, allow_blank: false

  validates :reference_field_id, :reference_business_id, presence: true, if: :reference_or_multiple_reference?
  validates :reference_value_field_id, presence: true, if: proc { reference_or_multiple_reference? && !reference_value_use_key_fields? }
  validates :reference_field_id, :reference_value_field_id, :reference_business_id, absence: true, unless: :reference_or_multiple_reference?
  validates :reference_sub_business_id, presence: true, if: :sub_business?
  validates :reference_sub_business_id, absence: true, unless: :sub_business?
  validates :label, uniqueness: { case_sensitive: false, scope: :template_id, conditions: -> { where(deleted_at: nil) } }
  validate :references_itself?, if: :reference_or_multiple_reference?
  validate :reference_to_a_valid_sub_business?, if: :sub_business?

  validates :char_max_limit, presence: true, numericality: { only_integer: true, greater_than: 0, less_than_or_equal_to: :get_max_char }, if: proc { enable_char_count? }

  validates :options, presence: true, allow_blank: false, if: :dropdown?
  validate :validate_options_json_schema, if: :dropdown?
  validates :options, absence: true, unless: :dropdown?
  validate :duplicated_options?, if: proc { dropdown? && options.present? }
  validate :in_use?, on: :update
  validate :same_label_with_different_type_with_steps?, if: proc { template.present? }
  validates :height, presence: { if: proc { text_area? } }
  validates :height, absence: { if: proc { !text_area? } }

  scope :for_step, ->(step_id) { joins(template: :step_templates).where(step_templates: { step_id: step_id }) }
  scope :for_business, ->(business_id) { kept.joins(template: :steps).where(steps: { business_id: business_id, deleted_at: nil }, templates: { deleted_at: nil }).order('step_templates."order", fields."order"') }
  scope :for_elasticsearch, -> { kept.where.not(type: UNPERMITED_FIELD_TYPES_FOR_ELASTICSEARCH) }

  before_undiscard :same_label_with_different_type?
  before_validation :set_char_max_limit, if: proc { !enable_char_count? }
  before_validation :set_order, on: :create
  before_validation :remove_options, unless: :dropdown?
  before_validation :remove_text_transformation, unless: proc { text? || text_area? || multiple? }
  before_validation :set_defaults_for_sub_business, if: :sub_business?
  before_validation :disable_allow_add_new, unless: proc { dropdown? || reference? || multiple_reference? }
  before_validation :nullify_height, unless: proc { text_area? }
  before_validation :falsify_allow_to_select_deleted_option, unless: :reference_or_multiple_reference?
  before_save :falsify_enable_char_count, unless: proc { text? || text_area? }
  before_create :validate_reference_field
  before_update :validate_reference_field
  after_update :rearrange_fields, if: :saved_change_to_order?
  after_save :reinitialize_elastic_index, if: :saved_change_to_label_or_type?
  after_update :verify_field_validations
  after_save :update_options
  after_save :manage_field_validation

  before_undiscard :set_order
  before_discard :remove_order

  after_discard :destroy_show_on_list_fields
  after_discard :reinitialize_elastic_index
  after_discard :delete_header_businesses
  # after_discard :destroy_dependent_field_rules


  after_save :invalidate_cache

  attribute :type, :integer
  enum type: {
    text:          0,
    integer:       1,
    text_area:     2,
    dropdown:      3,
    date:          4,
    decimal:       5,
    telephone:     6,
    reference:     7,
    multiple:      8,
    upload:        9,
    link:         10,
    sub_business: 11,
    multiple_reference: 12,
    instruction:  13
  }
  translate_enum :type

  attribute :size, :integer
  enum size: {
    small: 0,
    normal: 1,
    big: 2
  }
  translate_enum :size

  attribute :height, :integer
  enum height: {
    small: 0,
    normal: 1,
    big: 2
  }, _suffix: true
  translate_enum :height

  attribute :text_transformation, :integer
  enum text_transformation: {
    uppercase: 0,
    lowercase: 1,
    capitalize: 2
  }
  translate_enum :text_transformation

  def empty_value
    case type
      when 'text', 'text_area', 'telephone', 'dropdown', 'reference'
        ''
      when 'date', 'integer', 'decimal', 'sub_business', 'link'
        nil
      when 'multiple', 'multiple_reference', 'upload'
        []
    end
  end

  def parse_value(value)
    return nil if value.nil?

    case type
      when 'date'
        Date.iso8601(value)
      when 'integer'
        value.to_s
      when 'decimal'
        value.to_s
      when 'link'
        value.is_a?(String) ? JSON.parse(value) : value
      else
        value
    end
    rescue
      nil
  end

  def delete_header_businesses
    BusinessHeader.where(field_id: id).destroy_all
  end

  def verify_field_validations
    if type == 'text' || type == 'text_area'
      validations.each do |validation|
        if validation.type == 'extension' || validation.type == 'file_size'
          validation.destroy
        end
      end
    else
      validations.each do |validation|
        if validation.type == 'number_of_characters' || validation.type == 'content' || validation.type == 'regex'
          validation.destroy
        end
      end
    end
  end

  def set_eligible_for_condition
    actual_type = if attribute_present?('type')
                    type.nil? ? 'text' : type
                  else
                    'text'
                  end

    self.eligible_for_condition = actual_type != 'upload' && actual_type != 'link' && actual_type != 'sub_business'
  end

  def was_discarded?
    deleted_at.present?
  end

  def validate_reference_field
    return unless reference_value_use_key_fields
    raise ReferenceFieldWithoutKeysError, reference_business if reference_or_multiple_reference? && !was_discarded? && !reference_business.has_key?
  end

  def options
    self[:options]&.sort_by { |option| option['order'] }
  end

  def get_max_char
    MAX_CHARS.fetch(type, MAX_CHARS_DEFAULT_VALUE)
  end

  def reference_or_multiple_reference?
    reference? || multiple_reference?
  end

  def available_options
    return options if dropdown?
    return FieldSearcher.new(id: id).available_options_for_reference_field if reference_or_multiple_reference?
  end

  def form_name
    label.parameterize
  end

  def elasticsearch_name
    label.delete('.')
  end

  def elasticsearch_type
    case type
    when 'integer'
      'long'
    when 'decimal'
      'float'
    when 'date'
      'date'
    when 'sub_business'
      'nested'
    when 'text', 'text_area', 'telephone', 'dropdown', 'reference', 'multiple', 'multiple_reference', 'link'
      'text'
    else
      'text' # fallback para tipos não mapeados
    end
  end

  def elasticsearch_allowed_type?
    UNPERMITED_FIELD_TYPES_FOR_ELASTICSEARCH.exclude?(type.to_sym)
  end

  def self.cached_field(id)
    Rails.cache.fetch(id) { Field.find(id) }
  end

  def show_on_form_values?
    show_on_form?
  end

  def show_on_list_values?(business_id)
    show_on_list_fields.where(business_id: business_id).any?
  end

  private

  def verify_field_validations
    if type == 'text' || type == 'text_area'
      validations.each do |validation|
        if validation.type == 'extension' || validation.type == 'file_size'
          validation.destroy
        end
      end
    else
      validations.each do |validation|
        if validation.type == 'number_of_characters' || validation.type == 'content' || validation.type == 'regex'
          validation.destroy
        end
      end
    end
  end


  def saved_change_to_label_or_type?
    saved_change_to_label? || saved_change_to_type?
  end

  def destroy_show_on_list_fields
    ShowOnListField.where(field_id: id).destroy_all
  end

  # def destroy_dependent_field_rules
  #   DependentFieldRule.for_condition_field(self.id).or(DependentFieldRule.where(field_id: self.id)).destroy_all
  # end

  def nullify_height
    self.height = nil
  end

  def same_label_with_different_type_with_steps?
    business_id = template.steps.pluck(:business_id).uniq.first

    other_fields = Field.where.not(id: id).where.not(type: type).for_business(business_id).where(label: label)

    errors.add(:base, I18n.t('field.same_label_with_different_type', scope: 'activerecord.errors.models', steps: other_fields.pluck('steps.name').join(', '))) if other_fields.exists?
  end

  def same_label_with_different_type?
    if template.active_fields.pluck(:label).count(label) >= 1
      errors.add(
        :base,
        I18n.t(
          'field.same_label_with_different_type',
          scope: 'activerecord.errors.models'
          )
      )
      throw(:abort)
    end
  end

  def references_itself?
    errors.add(:base, I18n.t('field.cannot_reference_itself', scope: 'activerecord.errors.models')) if reference_field_id == id
    errors.add(:base, I18n.t('field.cannot_reference_itself', scope: 'activerecord.errors.models')) if reference_value_field_id == id && !reference_value_use_key_fields?
  end

  def reference_to_a_valid_sub_business?
    errors.add(:base, I18n.t('field.subbusiness_cannot_reference_itself', scope: 'activerecord.errors.models')) unless reference_sub_business&.sub_business?
    errors.add(:base, I18n.t('field.duplicate_subbusiness_reference', scope: 'activerecord.errors.models')) if Field.kept.where(reference_sub_business_id: reference_sub_business_id).where.not(id: id).exists?
    errors.add(:base, I18n.t('field.max_subbusiness_reached', scope: 'activerecord.errors.models')) if template.fields.kept.where(type: :sub_business).count > Step::SUBBUSINESS_LIMIT
  end

  def duplicated_options?
    labels = options.pluck('label').compact.map(&:downcase)
    values = options.pluck('value').compact.map(&:downcase)

    errors.add(:base, I18n.t('field.unallowed_duplicates', scope: 'activerecord.errors.models')) if (values.size != values.uniq.size) || (labels.size != labels.uniq.size)
  end

  def in_use?
    errors.add(:base, I18n.t('field.unallowed_update', scope: 'activerecord.errors.models')) if Answer.where("data ? 'values' AND data->'values' ? '#{id}'").exists? && type_changed?
  end

  def set_order
    self.order = template.active_fields.count if template.present?
  end

  def set_char_max_limit
    self.char_max_limit = get_max_char
  end

  def remove_order
    self.order = 1000 + order
  end

  def remove_options
    self.options = nil
  end

  def remove_text_transformation
    self.text_transformation = nil
  end

  def disable_allow_add_new
    self.allow_add_new = false
  end

  def manage_field_validation
    search_params = { field_id: id, type: :number_of_characters, operator: :less_than, data: char_max_limit_before_last_save&.next.to_s }
    update_params = { data: char_max_limit&.next, error_message: "#{label} - Limite maximo de caracteres: #{char_max_limit}" }

    return FieldValidation.find_or_create_by(search_params).tap { |fv| fv.assign_attributes(update_params) }.save! if enable_char_count?

    FieldValidation.find_by(search_params)&.destroy
  end

  def build_join_for_field_labels
    build_join_for_field_labels ||= ActiveRecord::Base.sanitize_sql_array([
      'JOIN content_values field_labels ON field_labels.content_id = contents.id AND field_labels.field_id = ?',
      reference_field_id
    ])
  end

  def build_join_for_field_values
    if reference_value_use_key_fields?
      ActiveRecord::Base.sanitize_sql_array([
        <<~SQL,
          JOIN (
            SELECT
              cv.content_id,
              string_agg(cv.value, '|' ORDER BY bf.created_at) AS value
            FROM businesses_fields bf
            JOIN content_values cv ON bf.field_id = cv.field_id
            WHERE bf.business_id = ?
            GROUP BY cv.content_id
          ) field_values ON field_values.content_id = contents.id
        SQL
        reference_business_id
      ])
    else
      ActiveRecord::Base.sanitize_sql_array([
        "JOIN content_values field_values ON field_values.content_id = contents.id AND field_values.field_id = ?",
        reference_value_field_id
      ])
    end
  end

  def update_options(content_id = nil)
    return unless reference_or_multiple_reference?

    records_to_delete = field_options
    records_to_delete = records_to_delete.where(content_id: content_id) if content_id.present?
    records_to_delete.delete_all

    field_options_cte = Content
                          .select(
                            "contents.id AS content_id",
                            "NULLIF(field_labels.value, '') AS label",
                            "NULLIF(field_values.value, '') AS value")
                          .joins(build_join_for_field_labels)
                          .joins(build_join_for_field_values)
                          .where(business_id: reference_business_id)

    field_options_cte = field_options_cte.unscope(where: :deleted_at) if allow_to_select_deleted_option

    field_options_cte = field_options_cte.where(id: content_id) if content_id.present?

    insert_sql = <<~SQL.squish
      WITH field_options_cte AS (
        #{field_options_cte.to_sql}
      )
      INSERT INTO field_options("order", field_id, content_id, label, "value", created_at, updated_at)
      SELECT
        0 "order",
        ?::uuid field_id,
        field_options_cte.content_id,
        field_options_cte.label,
        field_options_cte."value",
        NOW(),
        NOW()
      FROM field_options_cte
      WHERE field_options_cte.label IS NOT NULL
        AND field_options_cte."value" IS NOT NULL
    SQL

    ActiveRecord::Base.connection.execute(
      ActiveRecord::Base.send(:sanitize_sql_array, [insert_sql, id]),
    )
  end

  def rearrange_fields
    old_value, new_value = saved_change_to_order
    direction = (new_value - old_value).positive? ? 'asc' : 'desc'

    value = <<-SQL.squish.strip_heredoc
      UPDATE fields f SET "order" = (f2."order" - 1) FROM (#{template.active_fields.select("id, row_number() OVER(ORDER BY \"order\" asc, updated_at #{direction}) as order").to_sql}) f2 WHERE f.id = f2.id;
    SQL

    Field.connection.execute value
  end

  def set_defaults_for_sub_business
    self.size = :big
    self.enabled = true
    self.visible = true
    self.show_on_form = false
  end

  def invalidate_cache
    Rails.cache.delete(id)

    true
  end

  def falsify_enable_char_count
    self.enable_char_count = false
  end

  def falsify_allow_to_select_deleted_option
    self.allow_to_select_deleted_option = false
  end

  def validate_options_json_schema
    return if options.blank?

    begin
      errors.add(:options, 'are not valid according to the JSON schema') unless JSON::Validator.validate(OPTIONS_JSON_SCHEMA, options)
    rescue JSON::Schema::ValidationError => e
      errors.add(:options, "are not valid according to the JSON schema: #{e.message}")
    end
  end

  def reinitialize_elastic_index
    return unless elasticsearch_allowed_type?
    return if template.nil?
    return if template.businesses.empty?

    template.businesses.each do |business|
      next unless business.integrate_elastic?
      if new_record?
        ElasticSearcherService.new.create_index(business.id, [self])
      else
        Elasticsearch::BusinessSetupWorker.perform_in(Time.zone.tomorrow.midnight - Time.current, Apartment::Tenant.current, business.id) unless Elasticsearch::BusinessSetupWorker.job_exists?(Apartment::Tenant.current, business.id)
      end
    end
  end
end
