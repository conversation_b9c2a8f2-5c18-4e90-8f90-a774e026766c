# == Schema Information
#
# Table name: administrators
#
#  id                     :uuid             not null, primary key
#  allow_password_change  :boolean          default(FALSE), not null
#  approved               :boolean          default(TRUE), not null
#  authorization_token    :string
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  deleted_at             :datetime
#  email                  :string
#  encrypted_password     :string           default(""), not null
#  image                  :string
#  last_active_at         :datetime
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  name                   :string
#  nickname               :string
#  owner                  :boolean          default(FALSE), not null
#  provider               :string           default("email"), not null
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  sign_in_count          :integer          default(0), not null
#  tokens                 :jsonb
#  uid                    :string           default(""), not null
#  unconfirmed_email      :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_administrators_on_confirmation_token    (confirmation_token) UNIQUE
#  index_administrators_on_email                 (email) UNIQUE
#  index_administrators_on_reset_password_token  (reset_password_token) UNIQUE
#  index_administrators_on_uid_and_provider      (uid,provider) UNIQUE
#
class Administrator < ApplicationRecord
  has_paper_trail ignore: %i[
    confirmation_sent_at
    confirmation_token
    current_sign_in_at
    confirmation_token
    current_sign_in_at
    current_sign_in_ip
    last_active_at
    last_sign_in_at
    last_sign_in_ip
    reset_password_sent_at
    reset_password_token
    sign_in_count
    unconfirmed_email
    updated_at
    tokens
    updated_at
    created_at
    id
    uid
  ], on: [:create, :destroy, :update]

  # Include default devise modules.
  devise :database_authenticatable, :registerable, :recoverable, :rememberable, :secure_validatable, :trackable, :validatable, :omniauthable

  include DeviseTokenAuth::Concerns::User
  include SoftDeletable

  scope :approved, -> { where(approved: true) }

  after_create_commit :send_email_requesting_approvement
  after_update :send_email_when_user_is_approved
  after_create :update_generate_token

  validate :validade_updating_email, on: :update
  validate :validate_email_domain_valid

  def update_generate_token
    self.update_column("authorization_token", generate_token)
  end

  def generate_token
    SecureRandom.hex(32)
  end

  def token_validation_response
    super.merge(tenant: Apartment::Tenant.current)
  end

  def active_for_authentication?
    errors.clear

    if discarded?
      errors.add(:base, I18n.t('locked', scope: 'devise.failure'))
      return false
    end

    super
  end

  def send_email_requesting_approvement
    UserApprovementMailer.request_approvement_for_administrator(self).deliver_now unless approved
  end

  def send_email_when_user_is_approved
    UserApprovementMailer.administrator_approved(self).deliver_now if saved_change_to_approved? && approved
  end

  def validade_updating_email
    errors.add(:base, I18n.t('updating_email', scope: 'devise.oauth')) if (provider == 'google_oauth2' || provider == 'entra_id') && email_change.present?
  end

  def validate_email_domain_valid
    return if email.blank?
    return if Company.current.blank?

    return if email_change.blank?

    email_domain = email.split('@').last
    if provider == 'email' && Company.current.auth_domain.include?(email_domain)
      errors.add(:base, I18n.t('devise.omniauth.domain_already_registered', email_domain: email_domain))
    end
  end
end
