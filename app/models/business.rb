# == Schema Information
#
# Table name: businesses
#
#  id                                     :uuid             not null, primary key
#  allow_to_skip_validations_when_bulking :boolean          default(FALSE), not null
#  apply_field_rule                       :boolean          default(TRUE), not null
#  apply_field_validation_rule            :boolean          default(TRUE), not null
#  bulk_insert_on_first_step_validates_pk :boolean          default(FALSE), not null
#  deleted_at                             :datetime
#  description                            :string
#  enable_validation_web_service          :boolean          default(TRUE)
#  enable_verification_web_service        :boolean          default(TRUE)
#  fill_default_field_value               :boolean          default(TRUE), not null
#  help_url                               :string
#  icon                                   :string
#  integrate_elastic                      :boolean          default(FALSE), not null
#  name                                   :string           not null
#  notification                           :boolean          default(FALSE), not null
#  show_bulk_alteration                   :boolean          default(FALSE), not null
#  show_bulk_insert                       :boolean          default(FALSE), not null
#  show_on_dashboard                      :boolean          default(FALSE)
#  show_on_list_created_at                :boolean          default(FALSE)
#  show_on_list_created_by_name           :boolean          default(FALSE)
#  show_on_list_updated_at                :boolean          default(FALSE)
#  show_on_list_updated_by_name           :boolean          default(FALSE)
#  show_on_top_answers                    :boolean          default(FALSE), not null
#  sub_business                           :boolean          default(FALSE), not null
#  validate_fields                        :boolean          default(TRUE), not null
#  validate_required_fields               :boolean          default(TRUE), not null
#  webhook_url                            :string
#  who_can_delete_contents                :integer          default("only_coordinators"), not null
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  business_group_id                      :uuid
#
# Indexes
#
#  index_businesses_on_business_group_id  (business_group_id)
#  index_businesses_on_deleted_at         (deleted_at)
#  index_businesses_on_name               (name)
#  index_businesses_on_sub_business       (sub_business)
#
# Foreign Keys
#
#  fk_rails_...  (business_group_id => business_groups.id)
#
class Business < ApplicationRecord
  include SoftDeletable
  include TranslateEnum
  include Translatable
  include TranslatedAttribute

  attr_accessor :update_answer_index

  has_paper_trail ignore: %i[updated_at created_at id], on: %i[create update destroy]

  belongs_to :business_group, optional: false

  has_many :contents, dependent: :destroy
  has_many :steps, -> { order('deleted_at NULLS FIRST').order(order: :asc) }, inverse_of: :business
  has_many :active_steps, -> { kept.order(order: :asc) }, class_name: 'Step', inverse_of: :business
  has_many :fields, through: :active_steps
  has_many :answers, through: :contents
  has_many :answer_versions, through: :answers, source: :versions
  has_many :dependent_field_rules, dependent: :destroy
  has_many :show_on_list_fields, dependent: :destroy

  before_validation :strip_whitespace

  # PS: Não ordenar na associação
  # rubocop:disable Rails/HasAndBelongsToMany
  has_and_belongs_to_many :key_fields, class_name: 'Field', join_table: 'businesses_fields'
  # rubocop:enable Rails/HasAndBelongsToMany
  validates :key_fields, length: { maximum: 5 }

  validates :name, presence: true, allow_blank: false
  validate :not_updatable_sub_business
  validates :webhook_url,
            allow_blank: true,
            format: { with: %r{\Ahttps:\/\/.*\z}, message: "deve começar com 'https://'" }
  scope :not_sub_business, -> { where(sub_business: false) }
  scope :sub_business, -> { where(sub_business: true) }
  scope :with_permission_for_user, ->(user) { where(id: StepPermission.for_user(user).joins(step: :business).distinct('businesses.id').select('businesses.id')) }

  after_update :restore_parent, if: proc { deleted_at.blank? && saved_change_to_deleted_at? }
  after_save :update_answer_index!, if: :update_answer_index

  after_discard :remove_show_on_list_fields, :remove_dependent_field_rules

  attribute :who_can_delete_contents, :integer
  enum who_can_delete_contents: {
    only_coordinators: 0,
    everyone: 1,
    nobody: 2,
    only_creator: 3
  }

  def remove_show_on_list_fields
    show_on_list_fields.destroy_all
  end

  def remove_dependent_field_rules
    dependent_field_rules.discard_all
  end

  def created_by
    administrator = Administrator.where(id: versions.first&.whodunnit)
    return administrator&.first&.id if versions.first&.whodunnit && administrator.present?
  end

  def parent
    Field.kept.find_by(reference_sub_business_id: id)&.template&.steps&.first&.business
  end

  def parent_step
    Field.kept.find_by(reference_sub_business_id: id)&.template&.steps&.first
  end

  def has_key?
    key_fields.any?
  end

  private

  def strip_whitespace
    self.name = name.strip if name.present?
  end

  def update_answer_index!
    index_name = "answers_data_#{id.gsub('-', '')}"
    first_step_id = active_steps&.first&.id

    return if first_step_id.blank?

    index_fields = key_field_ids.map { |field_id| "(data->'values'->>'#{field_id}')" }

    create_index_sql = key_field_ids.length.zero? ? nil : "CREATE INDEX CONCURRENTLY #{index_name} ON answers USING btree(#{index_fields.join(',')}) WHERE step_id = '#{first_step_id}'"

    BusinessKeyFieldIndexWorker.perform_async(Apartment::Tenant.current, create_index_sql, index_name)
  end

  after_discard do
    unless sub_business
      subbusiness_ids = Field.for_business(id).where(type: :sub_business).pluck(:reference_sub_business_id)
      Business.where(id: subbusiness_ids).discard_all
    end
  end

  after_undiscard do
    unless sub_business
      subbusiness_ids = Field.for_business(id).where(type: :sub_business).pluck(:reference_sub_business_id)
      Business.where(id: subbusiness_ids).undiscard_all
    end
  end

  def restore_parent
    parent&.update! deleted_at: nil
  end

  def not_updatable_sub_business
    errors.add(:sub_business, :cannot_be_updated) if id && sub_business != sub_business_was
  end
end
