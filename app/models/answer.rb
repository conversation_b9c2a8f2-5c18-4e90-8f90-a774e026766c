# == Schema Information
#
# Table name: answers
#
#  id                     :uuid             not null, primary key
#  authorized_at          :datetime
#  authorizer_token       :string
#  available_at           :datetime
#  concluded_at           :datetime
#  data                   :jsonb
#  deleted_at             :datetime
#  filled_at              :datetime
#  first_fill_at          :datetime
#  last_update_ip         :inet
#  origin                 :integer
#  position               :integer          default(0), not null
#  required_authorization :boolean
#  status                 :integer          default("pending"), not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  authorizer_id          :uuid
#  content_id             :uuid
#  created_by_id          :uuid
#  review_requested_by_id :uuid
#  step_authorizer_id     :uuid
#  step_id                :uuid
#  user_id                :uuid
#
# Indexes
#
#  index_answers_on_content_id  (content_id)
#  index_answers_on_status      (status)
#  index_answers_on_step_id     (step_id)
#  index_answers_on_updated_at  (updated_at)
#  index_answers_on_user_id     (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (authorizer_id => users.id)
#  fk_rails_...  (content_id => contents.id)
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (review_requested_by_id => users.id)
#  fk_rails_...  (step_id => steps.id)
#  fk_rails_...  (user_id => users.id)
#
class Answer < ApplicationRecord
  include SoftDeletable
  include TranslateEnum
  include AASM

  # The current_user is mandatory to discard or undiscard an answer. It's used to keep the versions up to who executes the actions
  attr_accessor :skip_external_url_validator,
                :skip_field_validator,
                :skip_authorizer_validation,
                :skip_reopen_done_answers,
                :required_fields,
                :current_user,
                :current_user_ip,
                :skip_mandatory_fields,
                :skip_webhook,
                :steps_to_change_ids

  store_accessor :data, :values

  belongs_to :content, optional: false, touch: true
  belongs_to :with_deleted_content, -> { unscope(:where) }, foreign_key: 'content_id', class_name: 'Content', optional: true, inverse_of: false # use only to join table
  belongs_to :step, optional: false
  belongs_to :with_deleted_step, -> { unscope(:where) }, foreign_key: 'step_id', class_name: 'Step', optional: true, inverse_of: false # use only to join table
  belongs_to :user, optional: true
  belongs_to :authorizer, optional: true, class_name: 'User', inverse_of: false
  belongs_to :created_by, optional: true, class_name: 'User', inverse_of: false
  belongs_to :review_requested_by, optional: true, class_name: 'User', inverse_of: false
  belongs_to :step_authorizer, class_name: 'User', optional: true

  has_many :content_values, dependent: :destroy
  has_many :answer_processings, dependent: :destroy
  has_many :versions, class_name: 'AnswerVersion', dependent: :destroy

  DATA_JSON_SCHEMA = Rails.root.join('config', 'schemas', 'answers', 'pending.json_schema').to_s.freeze
  DONE_DATA_JSON_SCHEMA = Rails.root.join('config', 'schemas', 'answers', 'done.json_schema').to_s.freeze

  validates :step, uniqueness: { scope: %i[content] }
  validates :position, presence: true, allow_blank: false, numericality: { only_integer: true, greater_than_or_equal_to: 0 }, uniqueness: { scope: %i[content] }
  validates :data, presence: true, allow_blank: false, if: :concluded?
  validate :validate_data_json_schema, if: :concluded?
  validate :available_at_presence_when_concluded

  def available_at_presence_when_concluded
    errors.add(:base, I18n.t('answer.attributes.available_at.step_is_not_yet_available', scope: 'activerecord.errors.models')) if concluded? && available_at.blank?
  end

  validates :filled_at, presence: true, if: :concluded?
  validates :first_fill_at, presence: true, if: :concluded?
  validates :user_id, presence: true, if: :concluded?

  validates :concluded_at, presence: true, if: :done?

  validates :authorizer_id, presence: true, if: proc { done? && requires_authorization? && !skip_authorizer_validation }
  validates :authorized_at, presence: true, if: proc { done? && requires_authorization? && !skip_authorizer_validation }
  validate :user_can_authorize?, if: proc { done? && requires_authorization? && authorizer_id.present? }
  validate :cannot_update_available_at, if: proc { available_at_changed? && !available_at_was.nil? }, on: :update
  validate :cannot_update_first_fill_at, if: proc { first_fill_at_changed? && !first_fill_at_was.nil? }, on: :update
  validate :valid_pk_field?, if: proc { |answer| answer.concluded? && step.order.zero? }

  before_validation :sanitize_multiple_fields, :strip_dropdown_values, :remove_templates_from_data__legacy, if: proc { |answer| answer.concluded? && answer.values.present? }
  before_save :update_created_at_of_the_first_save
  before_create :check_position

  after_update :reopen_done_answers, if: proc { steps_to_change_ids || (step.steps_to_change_ids.present? && !skip_reopen_done_answers) }
  after_save :update_content_values, :update_field_options_from_reference_fields, :update_content_current_answer, :update_content_keywords, :save_version, :update_content_updated_at
  after_save :update_content_status, if: proc { saved_change_to_status? }
  after_commit :trigger_webhook, on: %i[update create], unless: :skip_webhook?

  validates_with FieldValidator, unless: :skip_field_validator
  validates_with ExternalUrlValidator, unless: :skip_external_url_validator
  validates_with AnswerValidators::MultipleFieldsValuesValidator, AnswerValidators::LinkValuesValidator, if: :concluded?
  validates_with AnswerValidators::ReferenceValuesValidator, AnswerValidators::DateValuesValidator, AnswerValidators::DropdownValuesValidator, AnswerValidators::FieldUpdateDisabledValidator, AnswerValidators::DecimalFieldValidator, AnswerValidators::IntegerFieldValidator
  validate :required_values_validator, if: :validator_mandatory_fields

  scope :not_empty, -> { where("data ->> 'values' != '{}'") }
  scope :ascending_order, -> { order(position: :asc) }

  attribute :origin, :integer
  enum origin: {
    client: 0,
    api: 1,
    bulk_saving: 2,
    bulk_alteration: 3
  }

  attribute :status, :integer
  enum status: {
    pending: 0,
    done: 1,
    waiting_authorization: 2,
    rejected: 3,
    changing: 4,
    under_review: 5
  }

  translate_enum :status

  aasm column: :status, enum: true, whiny_transitions: false do
    state :pending, initial: true
    state :done, after_enter: :update_next_answer_availability
    state :waiting_authorization
    state :rejected
    state :changing
    state :under_review

    event :finish do
      before do
        self.concluded_at = Time.zone.now if concluded_at.blank?
        self.required_authorization = false
      end

      transitions from: %i[waiting_authorization pending done rejected changing under_review], to: :done, unless: :requires_authorization?
    end

    event :wait_authorization do
      transitions from: :pending, to: :waiting_authorization, if: :requires_authorization?
    end

    event :authorize do
      before do
        self.authorized_at = Time.zone.now
        self.concluded_at = Time.zone.now
        self.required_authorization = true
      end

      transitions from: :waiting_authorization, to: :done, if: :requires_authorization?
    end

    event :reject do
      transitions from: :waiting_authorization, to: :rejected, if: :requires_authorization?
    end

    event :reopen do
      before do
        self.authorizer = nil
        self.authorized_at = nil
        self.concluded_at = nil
        self.required_authorization = nil
      end

      transitions from: %i[done rejected under_review changing], to: :waiting_authorization, if: :requires_authorization?
    end

    event :in_change do
      before do
        self.authorizer = nil
        self.authorized_at = nil
        self.concluded_at = nil
        self.required_authorization = nil
        self.skip_external_url_validator = true
      end

      transitions from: :done, to: :changing
    end

    event :review do
      before do
        self.skip_external_url_validator = true
      end

      transitions from: %i[waiting_authorization done rejected changing], to: :under_review
    end
  end

  def skip_webhook?
    content.business.skip_webhook || self.skip_webhook
  end

  def trigger_webhook
    return if content.business&.webhook_url.blank? || status != 'done'

    tenant = Apartment::Tenant.current

    answer_params = {
      tenant:,
      content_id:,
      step_id:,
      user_id:,
      data:,
      webhook_url: content.business.webhook_url
    }

    CreateAnswersWebhookWorker.perform_async(answer_params.to_json)
  end

  def validator_mandatory_fields
    (skip_mandatory_fields.nil? || !skip_mandatory_fields) && concluded?
  end

  def requires_authorization?
    step.step_permissions.approvement.exists?
  end

  def reject!
    update_attribute(:status, :rejected)
  end

  def authorize!
    update_attribute(:status, :done)
    update_attribute(:concluded_at, Time.now)
  end

  def content
    Content.unscoped { super }
  end

  def current_fields
    step.templates.map(&:fields).flatten
  end

  def concluded?
    waiting_authorization? || done? || rejected?
  end

  def update_next_answer_availability
    next_answer = content&.answers&.[](position + 1)

    return if next_answer.blank? || next_answer.available_at.present?

    if next_answer.concluded? && next_answer.data.empty?
      next_answer.update!(available_at: Time.zone.now, concluded_at: nil, status: :pending)
    else
      next_answer.update!(available_at: Time.zone.now)
    end
  end

  def dynamic_profile_schema
    concluded? ? DONE_DATA_JSON_SCHEMA : DATA_JSON_SCHEMA
  end

  def sub_contents
    Content.left_joins(content_values: :field).where(parent_id: content_id, fields: { order: 0 }).order('content_values.value')
  end

  def fill_empty_values_for_fields!
    self.values = Field.for_step(step_id).pluck(:id).map { |field_id| { field_id => nil } }.reduce({}, :merge).merge(values || {})
  end

  private

  def required_values_validator
    AnswerValidators::RequiredValuesValidator.new.validate(self, User.find_by(id: @current_user))
  end

  def cannot_update_available_at
    errors.add(:available_at, :update_unallowed)
  end

  def cannot_update_first_fill_at
    errors.add(:first_fill_at, 'Não é permitido atualizar a data do primeiro preenchimento')
  end

  def valid_pk_field?
    content_for_pk = content.other_content_for_same_pk(values || {})
    errors.add(:base, I18n.t('answer.duplicate_key_field_value', pk_labels: content.business.key_fields.pluck(:label).join('|'), duplicate_pk_labels: content_for_pk.first.edit_url, scope: 'activerecord.errors.models')) if content_for_pk.exists?
  end

  # Remover depois de remover este atributo em  todas as bases e ambientes
  def remove_templates_from_data__legacy
    data.except! 'templates'
  end

  def strip_dropdown_values
    dropdown_fields = Field.for_step(step_id).where(type: %i[dropdown reference]).pluck(:id)

    (values.keys & dropdown_fields).each do |field_id|
      values[field_id] = values[field_id].strip unless values[field_id].nil?
    end
  end

  def sanitize_multiple_fields
    multiple_fields = Field.for_step(step_id).where(type: %i[multiple multiple_reference]).pluck(:id)

    (values.keys & multiple_fields).each do |field_id|
      values[field_id] = [] if values[field_id].blank?
      values[field_id] = [values[field_id]] if values[field_id].is_a? String
      values[field_id] = values[field_id].reject { |v| v == 'null' || v.blank? }
    end
  end

  def user_can_authorize?
    errors.add(:base, I18n.t('answer.attributes.authorizer.cannot_approve', scope: 'activerecord.errors.models')) unless step.step_permissions.approvement.for_user(authorizer_id).exists?
  end

  def check_position
    position = content.answers.count if position.nil?
  end

  def update_field_options_from_reference_fields
    return unless values

    Field.where(reference_field_id: values.keys).or(Field.where(reference_value_field_id: values.keys)).each do |reference_field|
      reference_field.send(:update_options, content_id)
    end
  end

  def update_content_keywords
    Content.update_keywords("contents.id = '#{content_id}'")
  end

  def update_content_values
    return unless content

    content.content_values.where(answer_id: id).delete_all

    sql = "insert into content_values(answer_id, content_id, field_id, value, created_at, updated_at) select '#{id}', '#{content.id}', t.key::uuid, t.value, now(), now()
    from answers
    INNER JOIN jsonb_each_text(answers.data) e ON e.key = 'values'
    INNER JOIN jsonb_each_text(e.value::jsonb) t ON true
    INNER JOIN FIELDS ON FIELDS.ID::text = t.KEY
    where answers.id = '#{id}'"

    ActiveRecord::Base.connection.execute(sql)
  end

  def save_version
    return if values.blank?

    previous_changes = {} unless self.previous_changes.present? && self.previous_changes['data'].present?

    before_changes = {}
    after_changes = {}

    self.previous_changes['data']&.each_with_index do |version, index|
      version&.dig('values')&.each do |field_id, field_value|
        field = Field.find_by(id: field_id)
        next if field.nil?

        field_label = field.label
        index.zero? ? before_changes[field_label] = field_value : after_changes[field_label] = field_value
      end
    end

    object_changes = after_changes.each_with_object({}) do |(field_label, field_value), changes|
      changes[field_label] = [before_changes[field_label], field_value] if before_changes[field_label] != field_value
    end

    AnswerVersion.create(
      filled_at: filled_at || Time.zone.now,
      whodunnit_id: version_whodunnit_id,
      answer_id: id,
      event: version_event,
      values:,
      ip: version_ip,
      step_authorizer_id:,
      form_values: Field.for_step(step_id).pluck(:label, :id).to_h { |label, id| [label, values[id]] },
      origin: Answer.origins[origin],
      object_changes: object_changes,
      old_status: Answer.statuses[status_previous_change&.first],
      new_status: Answer.statuses[status_previous_change&.last]
    )
  end

  def version_event
    return :discard if deleted_at
    return :undiscard if deleted_at.nil? && versions.last&.event == 'discard'
    return :create if versions.count.zero?

    :update
  end

  def version_whodunnit_id
    return @current_user&.id if %i[discard undiscard].include?(version_event)

    user.id
  end

  def version_ip
    return @current_user_ip if %i[discard undiscard].include?(version_event)

    last_update_ip
  end

  def update_content_current_answer
    content.update_current_answer
  end

  def update_content_status
    content.update_progress
  end

  def update_created_at_of_the_first_save
    self.created_at = Time.zone.now if created_by_id_was.blank?
  end

  def update_content_updated_at
    max_date = content.answers.pluck(:updated_at).max

    content.update_column(:updated_at, max_date) unless max_date.nil?
  end

  def reopen_done_answers
    return if under_review?
    steps_to_change_ids = if self.steps_to_change_ids
      Step.where(id: self.steps_to_change_ids)
    else
      step.steps_to_change.select(:id)
    end

    steps_to_change_ids.each do |step|
      answer = content.answers.done.find_by(step_id: step.id)

      next unless answer

      answer.assign_attributes(authorizer_id: nil, authorized_at: nil, concluded_at: nil, required_authorization: nil, skip_external_url_validator: true, status: :changing)

      answer.save(validate: false)
    end
  end

  def validate_data_json_schema
    return unless data.present?

    begin
      errors.add(:data, 'is not valid according to the JSON schema') unless JSON::Validator.validate(dynamic_profile_schema, data)
    rescue JSON::Schema::ValidationError => e
      errors.add(:data, "is not valid according to the JSON schema: #{e.message}")
    end
  end
end
