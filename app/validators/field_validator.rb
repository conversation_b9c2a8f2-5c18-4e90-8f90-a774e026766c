class FieldValidator < ActiveModel::Validator
  # rubocop:disable Style/HashEachMethods
  def validate(record)
    return if record.values.blank? || !record.content.business.validate_fields

    field_ids = record.values.keys

    fields = Field.where(id: field_ids, deleted_at: nil)
                  .where(FieldValidation.where(field_id: field_ids).arel.exists)

    fields.each do |field|
      field.validations.each do |validation|
        case FieldValidation.types[validation['type']]
        when FieldValidation.types[:number_of_characters]
          record.errors.add(:base, validation['error_message']) unless check_number_of_characters_of(record.values[field.id], validation['operator'], validation['data'].to_i, field)
        when FieldValidation.types[:content]
          record.errors.add(:base, validation['error_message']) unless check_content_of(record.values[field.id], validation['operator'], validation['data'])
        when FieldValidation.types[:regex]
          record.errors.add(:base, validation['error_message']) unless check_regex_of(record.values[field.id], validation['operator'], validation['data'], field)
        when FieldValidation.types[:number_of_records]
          sub_business_id = validation.field.reference_sub_business_id
          quantity_of_lines = Content.not_draft.where(parent_id: record.content.id, business_id: sub_business_id).size
          record.errors.add(:base, validation['error_message']) unless check_number_of_lines_for_sub_business(quantity_of_lines, validation['operator'], validation['data'].to_i)
        end
      end
    end
  end
  # rubocop:enable Style/HashEachMethods

  private

  def check_number_of_lines_for_sub_business(value, operator, count)
    case FieldValidation.operators[operator]
    when FieldValidation.operators[:less_than]
      value < count
    when FieldValidation.operators[:equal_to]
      value == count
    when FieldValidation.operators[:greater_than]
      value > count
    end
  end

  def check_number_of_characters_of(text, operator, count, field)
    case FieldValidation.operators[operator]
    when FieldValidation.operators[:less_than]
      if field.multiple?
        text.map {|txt| txt.size < count }.all?
      else
        text.size < count
      end
    when FieldValidation.operators[:equal_to]
      if field.multiple?
        text.map {|txt| txt.size == count }.all?
      else
        text.size == count
      end
    when FieldValidation.operators[:greater_than]
      if field.multiple?
        text.map {|txt| txt.size > count }.all?
      else
        text.size > count
      end
    end
  end

  def check_content_of(text, operator, pattern)
    case FieldValidation.operators[operator]
    when FieldValidation.operators[:contains]
      text.include?(pattern)
    when FieldValidation.operators[:not_contains]
      text.exclude?(pattern)
    end
  end

  def check_regex_of(text, operator, pattern, field)
    case FieldValidation.operators[operator]
    when FieldValidation.operators[:match]
      if field.multiple?
        text.map {|txt| (pattern.to_regexp =~ txt).present? }.all?
      else
        (pattern.to_regexp =~ text).present?
      end
    when FieldValidation.operators[:not_match]
      if field.multiple?
        text.map {|txt| (pattern.to_regexp =~ txt).nil? }.all?
      else
        (pattern.to_regexp =~ text).nil?
      end
    end
  end
end
