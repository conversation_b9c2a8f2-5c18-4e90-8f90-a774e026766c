# :nocov:
class ApplicationController < ActionController::API
  include DeviseTokenAuth::Concerns::SetUserByToken
  include ActionController::MimeResponds
  include Pundit::Authorization

  rescue_from Pundit::NotAuthorizedError, with: :record_not_found

  before_action :check_for_session_life_span, :set_locale

  respond_to :json

  devise_token_auth_group :member, contains: %i[user administrator]
  before_action :configure_permitted_parameters, if: :devise_controller?

  def pundit_user
    current_user || current_administrator
  end

  def check_access_tenant
    company_id = request.headers['HTTP_COMPANY_ID']

    if company_id.present?
      company = Company.find(company_id)

      Apartment::Tenant.switch! company.subdomain
    end

    yield

    Apartment::Tenant.switch! 'public' if company_id.present?
  end

  def append_info_to_payload(payload)
    super

    user = current_user || current_administrator

    return unless (Rails.env.production? || Rails.env.staging? || Rails.env.sandbox? || Rails.env.academy? || Rails.env.uat?) && user

    payload[:user_id] = user.id
    payload[:user_name] = user.name.presence || user.email
    payload[:user_last_login] = user.last_sign_in_at
    payload[:tenant] = Apartment::Tenant.current
    payload[:ip] = remote_ip(request)
  end

  protected

  def configure_permitted_parameters
    added_attrs = %i[name email password password_confirmation remember_me]

    devise_parameter_sanitizer.permit :sign_up, keys: added_attrs
  end

  def check_for_session_life_span
    return if current_member.nil?

    if request.path.ends_with?('sign_out') # the user is signing out
      current_member.update_columns(last_active_at: nil)
    elsif current_member.last_active_at.nil? # the last_active_at was not recorded yet
      update_current_member_last_active_at
    else
      time_passed_since_last_sign_in = Time.zone.now.to_i - current_member.last_active_at.to_i
      company_life_span = Company.current.token_life_span_in_minutes.minutes

      if time_passed_since_last_sign_in > company_life_span.to_i
        current_member.update_columns(tokens: {}, last_active_at: nil)

        head :forbidden
      else
        update_current_member_last_active_at
      end
    end
  rescue StandardError => e
    Rails.logger.error "Error on application controller: #{e}"
  end

  def update_current_member_last_active_at
    current_member&.touch(:last_active_at)
  end

  def user_for_paper_trail
    current_member&.id
  end

  def info_for_paper_trail
    { whodunnit_type: current_member&.class&.name, ip: remote_ip(request) }
  end

  def set_company_enable_internationalization
    @company_enable_internationalization = Companies::EnableInternationalizationQuery.call
  end

  private

  def record_not_found(exception)
    render json: { error: "Not found", code: '404' }, status: :not_found
  end

  def extract_locale_from_accept_language_header
    browser_locale = request.env['HTTP_ACCEPT_LANGUAGE'].try(:scan, /^[a-z]{2}/).try(:first).try(:to_sym)

    I18n.available_locales.include?(browser_locale) ? browser_locale : I18n.default_locale
  end

  def set_locale
    I18n.locale = extract_locale_from_accept_language_header
  end

  def default_url_options(_options = {})
    { locale: I18n.locale }
  end

  def remote_ip(request)
    request.headers['HTTP_X_REAL_IP'] || request.remote_ip
  end
end
# :nocov:
