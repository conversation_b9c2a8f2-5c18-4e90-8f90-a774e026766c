class DashboardController < ApplicationController
  before_action :authenticate_user!, except: %i[duplicated_fields]
  before_action :authenticate_administrator!, only: %i[duplicated_fields]

  def index
    business_groups = Business.where(show_on_dashboard: true).select(:business_group_id)

    records = BusinessGroup.kept.where(
      id: business_groups
    )

    @business_groups = Dashboard::BusinessGroupDecorator.decorate_collection(records).select { |group| group.top_doing.present? }
  end

  def report
    searcher = DashboardSearcher.new(search_params)

    if searcher.valid?
      @results = Reports::ContentDecorator.decorate_collection(searcher.search)

      @metadata = searcher.metadata
    else
      render json: { errors: get_searcher_errors(searcher.errors) }, status: :unprocessable_entity
    end
  end

  def duplicated_fields
    searcher = DuplicatedFieldSearcher.new

    render json: DuplicatedFieldPresenter.new(searcher.search).as_json, status: :ok
  end

  private

  def search_params
    params.require(:datatable).permit(:business_group_id, :business_id, :step_id, :from, :to, :concluded_at_from, :concluded_at_to, :status, :actives, :inactives, :days_elapsed, :days_elapsed_search, :user_id, :created_by_id, :with_obsolete_values, pagination: {}).merge(current_user: current_user).tap do |whitelisted|
      whitelisted.each do |key, value|
        whitelisted[key] = nil if value == 'null'
      end
    end
  end

  def get_searcher_errors(searcher_errors)
    return [] if searcher_errors.blank?

    searcher_errors.messages.values.flatten
  end
end
