# == Schema Information
#
# Table name: contents
#
#  id                :uuid             not null, primary key
#  concluded_at      :datetime
#  created_by_ip     :inet
#  deleted_at        :datetime
#  deletion_reason   :string
#  draft             :boolean          default(FALSE)
#  keywords          :string
#  name              :string           not null
#  note              :text
#  status            :integer          default("pending"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  business_id       :uuid
#  created_by_id     :uuid
#  current_answer_id :uuid
#  deleted_by_id     :uuid
#  parent_id         :uuid
#
# Indexes
#
#  index_contents_on_business_id        (business_id)
#  index_contents_on_created_by_id      (created_by_id)
#  index_contents_on_current_answer_id  (current_answer_id)
#  index_contents_on_deleted_at         (deleted_at)
#  index_contents_on_deleted_by_id      (deleted_by_id)
#  index_contents_on_draft              (draft)
#  index_contents_on_parent_id          (parent_id)
#  index_contents_on_status             (status)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (current_answer_id => answers.id)
#  fk_rails_...  (deleted_by_id => users.id)
#  fk_rails_...  (parent_id => contents.id)
#
class ContentsController < ApplicationController
  before_action :authenticate_member!, except: %i[index]
  before_action :load_content, only: %i[show show_on_list_values show_on_form_values show_modifications all_values summary_values note]
  before_action :check_limited, only: %i[show note]
  before_action :set_company_enable_internationalization, only: %i[show_modifications]

  include SkipTranslation

  def index
    statuses = request.url.split('?').last.split('&').reject { |param| !param.include?('status') }.map { |status| status.split('status=').last }
    respond_to do |format|
      format.datatable do
        render json: ContentDatatable.new(params, business_id: params[:business_id], current_user:, statuses:), status: :ok
      end

      format.json do
        searcher = ContentSearcher.new(search_params.merge(current_user:))

        @contents = searcher.search
        @metadata = searcher.metadata

        @business_id = params[:business_id]
        access_control_scope
        @current_user = current_user
      end
    end
  end

  def access_control_scope
    @access_control_scope ||= DependentFieldRule.where(business_id: @business_id).access_control
  end

  def show
    @content = @content.decorate
  end

  def create
    service = ContentService.new(
      params.merge(
        created_by_id: current_user.try(:id), remote_ip: remote_ip(request)
      ),
      ContentValidators::ContentParamsValidator,
      create_fields
    )

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = ContentService.new(
      params.merge(
        update_by_user: current_user.try(:id), remote_ip: remote_ip(request)
      ),
      ContentValidators::ContentParamsValidator,
      update_fields
    )

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def note; end

  def show_on_list_values
    render json: @content.show_on_list_values
  end

  def show_on_form_values
    render json: @content.show_on_form_values(include_steps: params[:include_steps] == 'true', user_id: current_user.id)
  end

  def show_modifications; end

  def all_values
    render json: @content.answers.map(&:values).reject(&:nil?).reduce(:merge)
  end

  def summary_values
    step_values = {}

    @content.answers.each { |answer| step_values[answer.step.id] = { values: answer.values || {} } }

    render json: step_values
  end

  def reference_detail
    render json: ContentSearcher.new.search_detail(params[:id])
  end

  def destroy
    service = ContentService.new

    service.destroy(params[:id], destroy_params.merge(current_user:, current_user_ip: remote_ip(request)))

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def restore
    service = ContentService.new

    service.restore(params[:id], { current_user:, current_user_ip: remote_ip(request) })

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def search_params
    return {} if params[:grid_params].blank?

    JSON.parse(params[:grid_params]).merge(query: params[:query], business_id: params[:business_id], user: current_user)
  end

  def update_fields
    %i[business_id note updated_by_user].freeze
  end

  def create_fields
    %i[business_id parent_id parents created_by_id].freeze
  end

  def load_content
    @content = with_discarded ? Content.with_discarded.find(params[:id]) : Content.find(params[:id])
  end

  def with_discarded
    params[:with_discarded].present? && [true, 'true'].include?(params[:with_discarded])
  end

  def check_limited
    render json: { errors: [I18n.t('forbidden_to_view_record', scope: 'activerecord.errors.messages')] }, status: :forbidden if limited?
  end

  def limited?
    return false if @content.created_by_id == current_user.id
    return true if current_user.limited?
    return false if current_user.departments.blank?

    departments = current_user.departments
    departments.all?(&:limited?) && current_user.departments.all? { |department| department.users.pluck(:id).none? { |id| @content.created_by_id == id } }
  end

  def destroy_params
    params.permit(:deletion_reason)
  end
end
