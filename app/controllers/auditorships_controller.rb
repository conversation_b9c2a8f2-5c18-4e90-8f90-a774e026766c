class AuditorshipsController < ApplicationController
  before_action :authenticate_administrator!

  def index
    searcher = AuditorshipSearcher.new(search_params)

    @additional_informations = {}
    @results = searcher.search.map do |record|
      service = AuditorshipService.new
      service.change_id_to_label_or_name_of_record(record) unless record.item_type == 'DependentFieldRule' && DependentFieldRule.find_by(id: record.item_id).nil?
      @additional_informations[record.id] = service.additional_information(record)
      record
    end
    @metadata = searcher.metadata
  end

  def summary
    render json: get_summary, status: :ok
  end

  private

  def search_params
    params.permit(:startRow, :endRow, :search)
  end

  def base_scope
    @base_scope ||= PaperTrail::Version.all.order(created_at: :desc)
  end

  def get_summary
    {
      item_types: base_scope.pluck(:item_type).uniq,
      item_ids: base_scope.pluck(:item_id, :item_type).uniq(&:first).map do |e|
        begin
          record = e.second.classify.constantize.find(e.first)
        rescue ActiveRecord::RecordNotFound
          nil
        end
        auditorship_item(e.second, record)
      end.compact,
      events: base_scope.pluck(:event).uniq,
      whodunnits: base_scope.where.not(whodunnit: nil).pluck(:whodunnit, :whodunnit_type).uniq(&:first).map do |e|
        e.second.classify.constantize.find(e.first).slice(:name, :id).merge(type: e.second)
      rescue ActiveRecord::RecordNotFound
        nil
      end.compact,
      whodunnit_types: base_scope.pluck(:whodunnit_type).uniq,
      attributes: base_scope.pluck('jsonb_object_keys(object_changes)').uniq,
      translated_attributes: base_scope.pluck('jsonb_object_keys(object_changes)').uniq.map! { |attr| I18n.t("auditorship.summary.#{attr}", default: attr) }
    }
  end

  def auditorship_item(type, record)
    case type
    when 'DependentFieldRule', 'BusinessHeader', 'ShowOnListField'
      { id: record&.id, type: type, name: I18n.t("auditorship.auditables.#{type}", default: type), business: record&.business&.name }
    when 'Administrator', 'BusinessGroup', 'Business', 'Department', 'Template', 'User'
      { id: record&.id, type: type, name: record&.name }
    when 'Field'
      { id: record&.id, type: type, name: record&.label }
    when 'StepTemplate', 'FieldValidation', 'StepPermission', 'FieldValidation', 'DependentReferenceField'
      { id: record&.id, type: type, name: I18n.t("auditorship.auditables.#{type}", default: type) }
    when 'Step'
      { id: record&.id, type: type, name: record&.name, business: record&.business&.name }
    end
  end
end
