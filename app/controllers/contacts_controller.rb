# == Schema Information
#
# Table name: contacts
#
#  id          :uuid             not null, primary key
#  ddd         :string(4)
#  ddi         :string(4)
#  description :string
#  email       :string
#  phone       :string(11)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#

class ContactsController < ApplicationController
  before_action :authenticate_administrator!, except: %i[index]

  def index
    respond_to do |format|
      format.datatable do
        render json: ContactDatatable.new(params), status: :ok
      end
      format.json do
        @records = Contact.all.order('created_at')        
      end
    end
  end

  def show
    @contact = Contact.find(params[:id])
  end

  def create
    service = ContactService.new(create_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = ContactService.new(create_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = ContactService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private  

  def create_params
    params.permit(:description, :email, :phone, :ddd, :ddi)
  end
end
