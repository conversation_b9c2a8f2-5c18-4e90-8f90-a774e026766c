# == Schema Information
#
# Table name: public.bulk_destroying_contents
#
#  id            :uuid             not null, primary key
#  business_name :string
#  end_at        :datetime
#  start_at      :datetime
#  status        :integer          default("created"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  business_id   :string
#  company_id    :uuid
#
# Indexes
#
#  index_bulk_destroying_contents_on_company_id  (company_id)
#
# Foreign Keys
#
#  fk_rails_...  (company_id => companies.id)
#
class BulkDestroyingContentsController < ApplicationController
  before_action :authenticate_administrator!

  def index
    respond_to do |format|
      format.datatable do
        authorize BulkDestroyingContent
        render json: BulkDestroyingContentDatatable.new(params), status: :ok
      end
      format.json do
        @records = BulkDestroyingContent.all
      end
    end
  end

  def show
    authorize BulkDestroyingContent
    @record = BulkDestroyingContent.find(params[:id])
  end

  def destroy_all_contents
    authorize BulkDestroyingContent
    service = BulkDestroyingContentService.new(destroying_params)


    service.create

    if service.success
      render json: { bulk_destroying_content: service.record }, status: :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy_selected_contents
    authorize BulkDestroyingContent
    service = BulkDestroyingContentService.new(destroying_params)

    service.create

    if service.success
      render json: { bulk_destroying_content: service.record }, status: :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def process_orphans
    authorize BulkDestroyingContent
    service = BulkDestroyingContentService.new

    service.process_orphans params[:id]

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  private

  def destroying_params
    if JSON.parse(params[:content_ids]).present?
      params.permit(:company_id, :business_id, :business_name).merge(content_ids: JSON.parse(params[:content_ids]))
    else
      params.permit(:company_id, :business_id, :business_name, :only_actives)
    end
  end
end
