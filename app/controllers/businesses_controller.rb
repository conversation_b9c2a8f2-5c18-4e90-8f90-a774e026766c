# == Schema Information
#
# Table name: businesses
#
#  id                                     :uuid             not null, primary key
#  allow_to_skip_validations_when_bulking :boolean          default(FALSE), not null
#  apply_field_rule                       :boolean          default(TRUE), not null
#  apply_field_validation_rule            :boolean          default(TRUE), not null
#  bulk_insert_on_first_step_validates_pk :boolean          default(FALSE), not null
#  deleted_at                             :datetime
#  description                            :string
#  enable_validation_web_service          :boolean          default(TRUE)
#  enable_verification_web_service        :boolean          default(TRUE)
#  fill_default_field_value               :boolean          default(TRUE), not null
#  help_url                               :string
#  icon                                   :string
#  integrate_elastic                      :boolean          default(FALSE), not null
#  name                                   :string           not null
#  notification                           :boolean          default(FALSE), not null
#  show_bulk_alteration                   :boolean          default(FALSE), not null
#  show_bulk_insert                       :boolean          default(FALSE), not null
#  show_on_dashboard                      :boolean          default(FALSE)
#  show_on_list_created_at                :boolean          default(FALSE)
#  show_on_list_created_by_name           :boolean          default(FALSE)
#  show_on_list_updated_at                :boolean          default(FALSE)
#  show_on_list_updated_by_name           :boolean          default(FALSE)
#  show_on_top_answers                    :boolean          default(FALSE), not null
#  sub_business                           :boolean          default(FALSE), not null
#  validate_fields                        :boolean          default(TRUE), not null
#  validate_required_fields               :boolean          default(TRUE), not null
#  webhook_url                            :string
#  who_can_delete_contents                :integer          default("only_coordinators"), not null
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  business_group_id                      :uuid
#
# Indexes
#
#  index_businesses_on_business_group_id  (business_group_id)
#  index_businesses_on_deleted_at         (deleted_at)
#  index_businesses_on_name               (name)
#  index_businesses_on_sub_business       (sub_business)
#
# Foreign Keys
#
#  fk_rails_...  (business_group_id => business_groups.id)
#
class BusinessesController < ApplicationController
  include SkipTranslation

  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!, except: %i[index show fields export_all_steps_models]
  before_action :authenticate_member!, only: %i[index show fields export_all_steps_models]
  before_action :check_permission, only: %i[show], if: -> { current_user }
  before_action :set_company_enable_internationalization, only: %i[index show fields]
  before_action :set_user_and_business_on_redis, only: %i[create update destroy]

  def index
    respond_to do |format|
      format.datatable do
        render json: BusinessDatatable.new(params), status: :ok
      end
      format.json do
        @businesses = Business

        @businesses = @businesses.not_sub_business unless ['true', true].include? params[:include_sub_businesses]
        @businesses = Business.unscoped.where(sub_business: params[:sub_business]) if params[:sub_business]
        @businesses = @businesses.where(integrate_elastic: params[:integrate_elastic]) if params[:integrate_elastic]

        @businesses = @businesses.kept if params[:only_actives].present?
        @businesses = @businesses.includes(:business_group).all.order('business_groups.name').order('businesses.deleted_at NULLS FIRST').order(:name)
        @businesses = @businesses.with_permission_for_user(current_user) if current_user
      end
    end
  end

  def show
    @business = Business.find(params[:id])
  end

  def create
    service = BusinessService.new(create_business_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = BusinessService.new(update_business_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = BusinessService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = BusinessService.new

    service.activate(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def fields
    @records = FieldGroupedByStepSearcher.new(params).results
  rescue StandardError => e
    render json: { errors: e.message }, status: :bad_request
  end

  def export_all_steps_models
    business = Business.find_by(id: params[:id])

    unless business
      return render json: { errors: [I18n.t('not_found', scope: 'activerecord.errors.messages')] }, status: :not_found
    end

    workbook = RubyXL::Workbook.new
    workbook.worksheets.clear

    name_count = Hash.new(0)

    business.active_steps.each do |step|
      name_count[step.name] += 1
      worksheet_name = name_count[step.name] > 1 ? "#{step.name.truncate(28)}_#{name_count[step.name]}" : step.name.truncate(31)
      worksheet = workbook.add_worksheet(format_for_worksheet_name(worksheet_name))

      fields = step.step_templates.ascending_order.flat_map do |step_template|
        step_template.template.active_fields
      end

      fields = fields.reject { |field| field.type == 'sub_business' }

      fields = order_key_fields(fields, business.key_fields)
      fields = order_key_fields(fields, business.parent.key_fields) if business.sub_business?

      fields.each_with_index do |field, index|
        worksheet.add_cell(0, index, field.label || "Unnamed Field")
      end
    end

    filename = "#{business.name.parameterize}.xlsx"
    headers['Content-Disposition'] = "attachment; filename=\"#{filename}\""

    send_data workbook.stream.string, filename: filename, type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  end


  private

  def set_user_and_business_on_redis
    Rails.cache.write("logger", {administrator: current_administrator.id, administrator_email: current_administrator.email, business: params[:id], business_name: params[:name], tenant: Apartment::Tenant.current})
  end

  def format_for_worksheet_name(name)
    # Remove invalid characters for worksheet name

    name.gsub(/[\[\]:*?\/\\]/, '')
  end

  def order_key_fields(fields, key_fields)
    key_fields.each do |key_field|
      fields.delete(key_field)
      fields.unshift(key_field)
    end

    fields
  end

  def check_permission
    render json: { errors: [I18n.t('forbidden_to_view_record', scope: 'activerecord.errors.messages')] }, status: :forbidden unless user_permitted?
  end

  def user_permitted?
    StepPermission.for_user(current_user).joins(step: :business).where('business_id = ?', params[:id]).exists?
  end

  def create_business_params
    update_business_params.merge(params.permit(:sub_business))
  end

  def update_business_params
    params.permit(:skip_webhook, :integrate_elastic, :webhook_url, :name, :description, :type, :business_group_id, :show_on_dashboard, :show_on_top_answers,
                  :show_bulk_alteration, :bulk_insert_on_first_step_validates_pk,
                  :show_bulk_insert, :validate_fields, :enable_validation_web_service, :enable_verification_web_service, :fill_default_field_value, :validate_required_fields,
                  :apply_field_rule, :apply_field_validation_rule, :notification, :help_url,
                  :allow_to_skip_validations_when_bulking, :show_on_list_created_at, :show_on_list_updated_at,
                  :show_on_list_created_by_name, :show_on_list_updated_by_name, :who_can_delete_contents, :icon, key_field_ids: [])
  end
end
