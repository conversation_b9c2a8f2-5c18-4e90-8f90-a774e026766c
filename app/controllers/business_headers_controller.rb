# == Schema Information
#
# Table name: business_headers
#
#  id          :uuid             not null, primary key
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  business_id :uuid             not null
#  field_id    :uuid             not null
#  step_id     :uuid             not null
#
# Indexes
#
#  index_business_headers_on_business_id  (business_id)
#  index_business_headers_on_field_id     (field_id)
#  index_business_headers_on_step_id      (step_id)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (field_id => fields.id)
#  fk_rails_...  (step_id => steps.id)
#
class BusinessHeadersController < ApplicationController
  before_action :authenticate_member!, only: %i[index field_with_value]
  before_action :authenticate_administrator!, except: %i[index field_with_value]
  before_action :set_company_enable_internationalization, only: %i[field_with_value]

  include SkipTranslation

  def index
    @records = BusinessHeader.all
    @records = @records.where(business_id: params[:business_id]) if params[:business_id]

    @records
  end

  def create
    service = BusinessHeaderService.new(business_header_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = BusinessHeaderService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def field_with_value
    @records = BusinessHeaderDecorator.decorate(params).fields_with_values

    @records
  end

  private

  def business_header_params
    params.permit(:business_id, :step_id, :field_id)
  end
end
