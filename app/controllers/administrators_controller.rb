# == Schema Information
#
# Table name: administrators
#
#  id                     :uuid             not null, primary key
#  allow_password_change  :boolean          default(FALSE), not null
#  approved               :boolean          default(TRUE), not null
#  authorization_token    :string
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  deleted_at             :datetime
#  email                  :string
#  encrypted_password     :string           default(""), not null
#  image                  :string
#  last_active_at         :datetime
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  name                   :string
#  nickname               :string
#  owner                  :boolean          default(FALSE), not null
#  provider               :string           default("email"), not null
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  sign_in_count          :integer          default(0), not null
#  tokens                 :jsonb
#  uid                    :string           default(""), not null
#  unconfirmed_email      :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_administrators_on_confirmation_token    (confirmation_token) UNIQUE
#  index_administrators_on_email                 (email) UNIQUE
#  index_administrators_on_reset_password_token  (reset_password_token) UNIQUE
#  index_administrators_on_uid_and_provider      (uid,provider) UNIQUE
#
class AdministratorsController < ApplicationController
  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!
  before_action :validate_public_admin, except: :index

  def index
    respond_to do |format|
      format.datatable do
        authorize Administrator
        render json: AdministratorDatatable.new(params, current_administrator: current_administrator, current_tenant: Apartment::Tenant.current), status: :ok
      end
      format.json do
        @administrators = Administrator.all.order('deleted_at NULLS FIRST').order(:name)
      end
    end
  end

  def show
    @administrator = Administrator.find(params[:id])
  end

  def create
    service = AdministratorService.new(administrator_params)
    authorize service
    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = AdministratorService.new(administrator_params)
    authorize service
    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = AdministratorService.new
    authorize service
    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = AdministratorService.new

    service.restore(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def administrator_params
    params.permit(:name, :email, :password, :password_confirmation, :approved, :owner)
  end

  def validate_public_admin
    return render json: { errors: I18n.t('forbidden', scope: 'activerecord.errors.messages') }, status: :unauthorized if Apartment::Tenant.current == 'public' && (!current_administrator.owner && current_administrator.id != params[:id])
  end
end
