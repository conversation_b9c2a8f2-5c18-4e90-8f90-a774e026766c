class ChatsController < ApplicationController
  before_action :authenticate_user!

  def login
    service = Twilio::UserService.new

    service.login(current_user)

    if service.success?
      render json: { token: service.record.to_jwt }, status: :ok
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def users
    @records = User.kept.where(chat_enabled: true).where.not(id: current_user.id)
    @records = @records.search(params[:q]) if params[:q].present?

    @records = @records.select(:id, :name).reorder(:name)
  end
end
