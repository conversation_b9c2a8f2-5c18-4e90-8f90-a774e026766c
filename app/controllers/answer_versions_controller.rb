# == Schema Information
#
# Table name: answer_versions
#
#  id                 :bigint           not null, primary key
#  event              :integer          default("update"), not null
#  event_comment      :text
#  filled_at          :datetime
#  form_values        :jsonb
#  ip                 :inet
#  new_status         :integer
#  object_changes     :jsonb
#  old_status         :integer
#  origin             :integer
#  values             :jsonb
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  answer_id          :uuid
#  step_authorizer_id :uuid
#  whodunnit_id       :uuid
#
# Indexes
#
#  index_answer_versions_on_answer_id  (answer_id)
#
# Foreign Keys
#
#  fk_rails_...  (answer_id => answers.id)
#  fk_rails_...  (whodunnit_id => users.id)
#
class AnswerVersionsController < ApplicationController
  before_action :authenticate_user!

  def index
    return render json: { errors: I18n.t('data_range_greater_than_six_months') }, status: :unprocessable_entity unless validate_date_range_less_than_six_months(params)

    render json: AnswerVersionDatatable.new(params, current_user: current_user), status: :ok
  end

  private

  def validate_date_range_less_than_six_months(params)
    return true if params[:start_on].blank? || params[:end_on].blank?

    start_date = Date.parse(params[:start_on])
    end_date = Date.parse(params[:end_on])

    return true if (start_date..end_date).count <= 180

    false
  end
end
